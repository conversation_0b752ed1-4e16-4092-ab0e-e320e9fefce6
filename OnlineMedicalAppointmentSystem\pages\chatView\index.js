const App = getApp();
var intervalId;
Page({
  data: {
    msgs1: [],
    cmemo: "",
    url: App.Config.fileBasePath,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  onShow() {
    this.getMsgs1();
    var that = this;
    intervalId = setInterval(function () {
      that.getMsgs1();
    }, 5000);
  },

  onHide() {
    clearInterval(intervalId);
  },

  onUnload() {
    clearInterval(intervalId);
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  getMsgs1() {
    //设置要传递的参数
    let param = {
      lname: wx.getStorageSync("lname"),
      did: this.data.globalOption.id,
    };

    wx.request({
      url: App.Config.basePath + "/chatinfo_List",
      data: param,
      success: (res) => {
        this.setData({
          msgs1: res.data.data, //把从服务器端得到的值赋值给数组
        });

        // 获取页面高度并减去80px的底部间距
        wx.createSelectorQuery()
          .selectViewport()
          .scrollOffset()
          .exec((res) => {
            const pageHeight = res[0].scrollHeight - 280;
          });
      },
    });
  },

  //得到留言框输入的值
  getInputValue(e) {
    this.setData({
      cmemo: e.detail.value,
    });
  },

  //发送消息
  liuyan(e) {
    var that = this;
    if (this.data.cmemo == "") {
      this.showModal("消息内容不能为空");
    } else {
      //设置要传递的参数
      let param = {
        lname: wx.getStorageSync("lname"),
        did: this.data.globalOption.id,
        content: this.data.cmemo,
        flag: "1", // 用户发送的消息
        sendtime: new Date().toLocaleString(),
        loadmsg: `正在发送中`,
      };

      App.HttpService.saveData(param, "/chatinfo_Add").then((data) => {
        //执行服务器Servlet
        App.WxService.showToast({
          title: "发送成功!",
          icon: "none",
          duration: 500,
        });
        setTimeout(function () {
          that.getMsgs1();
          that.setData({
            cmemo: "",
          });
        }, 500);
      });
    }
  },

  //删除
  delData1(e) {
    const dataset = e.currentTarget.dataset;

    //设置要传递的参数
    let param = {
      id: dataset.id,
      f: 1,
    };
    App.WxService.showModal({
      title: "友情提示",
      content: "确定要删除吗？",
    }).then((data) => {
      if (data.confirm == 1) {
        App.HttpService.delData(param, "/chatinfo_Delete").then((data) => {
          //执行服务器Servlet
          this.getMsgs1(); //获取数据
        });
      }
    });
  },
});
