.container {

}

.patient-select {
    padding: 10rpx;
    border: 1px solid #ddd;
    border-radius: 4rpx;
    margin-bottom: 10rpx;
}

.add-patient {
    margin-top: 10rpx;
}

.patient-picker {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

.picker-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
}

.picker-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
}

.picker-header {
    padding: 20rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.picker-header .close {
    font-size: 40rpx;
    color: #999;
}

.picker-body {
    max-height: 60vh;
    overflow-y: auto;
}

.patient-item {
    padding: 20rpx;
    border-bottom: 1px solid #eee;
}

.patient-info {
    font-size: 24rpx;
    color: #999;
    margin-left: 20rpx;
}

.patient-list {
    margin-bottom: 20rpx;
}

.patient-item {
    padding: 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
    display: flex;
    flex-direction: column;
}

.patient-item.selected {
    border-color: #39b54a;
    background: #f0fff0;
}

.patient-item .name {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
}

.patient-item .info {
    font-size: 26rpx;
    color: #666;
}

.add-patient {
    margin-top: 20rpx;
}
