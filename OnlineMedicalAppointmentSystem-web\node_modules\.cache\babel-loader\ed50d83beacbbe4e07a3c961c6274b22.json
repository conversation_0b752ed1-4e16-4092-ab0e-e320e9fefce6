{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsAdd.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "pname", "required", "message", "trigger", "mounted", "methods", "save", "$refs", "validate", "valid", "url", "post", "then", "res", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "<PERSON><PERSON><PERSON><PERSON>", "val", "pmemo"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsAdd.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"科室名称\" prop=\"pname\">\r\n<el-input v-model=\"formData.pname\" placeholder=\"科室名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"科室介绍\" prop=\"pmemo\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.pmemo\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nimport <PERSON>Editor from \"../../../components/WangEditor\";\nexport default {\n  name: 'PartsAdd',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          pname: [{ required: true, message: '请输入科室名称', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    mounted() {\r\n    \r\n    },\r\n\r\n \n    methods: {    \n   // 添加\n    save() {       \n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n           if (valid) {\n             let url = base + \"/parts/add\";\n             this.btnLoading = true;\n             request.post(url, this.formData).then((res) => { //发送请求         \n               if (res.code == 200) {\n                 this.$message({\n                   message: \"操作成功\",\n                   type: \"success\",\n                   offset: 320,\n                 });              \n                this.$router.push({\n                path: \"/PartsManage\",\n                });\n               } else {\n                 this.$message({\n                   message: res.msg,\n                   type: \"error\",\n                   offset: 320,\n                 });\n               }\n               this.btnLoading=false;\n             });\n           }        \n           \n         });\n    },\n    \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/PartsManage\",\n          });\n        },       \n              \n          \n           \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.pmemo = val;\n    },\r\n   \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";AAoBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAC/D;IAEJ,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG,CAEV,CAAC;EAGDC,OAAO,EAAE;IACV;IACCC,IAAIA,CAAA,EAAG;MACF,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIC,GAAE,GAAInB,IAAG,GAAI,YAAY;UAC7B,IAAI,CAACM,UAAS,GAAI,IAAI;UACtBP,OAAO,CAACqB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACZ,QAAQ,CAAC,CAACc,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZb,OAAO,EAAE,MAAM;gBACfc,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACH,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAClBC,IAAI,EAAE;cACN,CAAC,CAAC;YACH,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZb,OAAO,EAAEW,GAAG,CAACQ,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACpB,UAAU,GAAC,KAAK;UACvB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACP,CAAC;IAEE;IACCyB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAIG;IACRG,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAAC1B,QAAQ,CAAC2B,KAAI,GAAID,GAAG;IAC3B;EAEE;AACN", "ignoreList": []}]}