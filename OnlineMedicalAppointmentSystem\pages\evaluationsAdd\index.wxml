<view class="container">
	<diy-navbar bgColor="green" isBack="{{true}}">
		<view slot="backText"> 返回 </view>
		<view slot="content"> {{evaluation ? '评价详情' : '发布评价'}} </view>
	</diy-navbar>

	<view wx:if="{{evaluation}}" class="padding-lg bg-white">
		<view class="text-center padding-bottom">
			<text class="text-xl">{{evaluation.by2}}</text>
		</view>
		<view class="star-rating text-center">
			<block wx:for="{{ [1, 2, 3, 4, 5] }}" wx:key="*this">
				<text class="iconfont {{ item <= evaluation.score ? 'diy-icon-starfill' : 'diy-icon-star' }}"
					style="color: {{ item <= evaluation.score ? 'red' : '#ccc' }};"></text>
			</block>
		</view>
		<view class="padding-tb text-content">
			{{evaluation.comment}}
		</view>
		<view class="text-gray text-sm text-right">
			评价时间：{{evaluation.etime}}
		</view>
	</view>

	<form wx:else bindsubmit="submitForm" bindreset="resetForm" class="flex diy-form diy-col-24 justify-center">
		<view class="padding-lg bg-white">


			<!-- 星级评分 -->
			<view class="star-rating text-center">
				<block wx:for="{{ [1, 2, 3, 4, 5] }}" wx:key="*this">
					<text class="iconfont {{ item <= rate ? 'diy-icon-starfill' : 'diy-icon-star' }} star"
						data-rate="{{ item }}" catchtap="changeRate"
						style="color: {{ item <= rate ? 'red' : '#ccc' }};"></text>
				</block>
			</view>

			<!-- 评价内容 -->
			<view class="padding-tb">
				<textarea class="full-width" name="comment" placeholder="请输入评价内容" maxlength="200"></textarea>
			</view>

			<!-- 隐藏字段 -->
			<input hidden name="rid" value="{{globalOption.rid}}" />
			<input hidden name="did" value="{{globalOption.did}}" />
			<input hidden name="score" value="{{score}}" />
			<input hidden name="lname" value="{{lname}}" />

			<view class="flex justify-center padding-top">
				<button form-type="submit" class="cu-btn bg-green">提交评价</button>
			</view>
		</view>
	</form>
</view>