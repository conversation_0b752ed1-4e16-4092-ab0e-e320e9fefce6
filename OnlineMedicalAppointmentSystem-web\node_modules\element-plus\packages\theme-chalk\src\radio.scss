@use "sass:map";

@import 'mixins/mixins';
@import 'mixins/utils';
@import 'mixins/var';
@import 'mixins/button';
@import 'common/var';

@include b(radio) {
  @include set-component-css-var('radio', $--radio);
}

@include b(radio) {
  color: var(--el-radio-font-color);
  font-weight: var(--el-radio-font-weight);
  line-height: 1;
  position: relative;
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  outline: none;
  font-size: var(--el-font-size-base);
  margin-right: 30px;
  @include utils-user-select(none);

  @include when(bordered) {
    padding: map.get($--radio-bordered-padding, 'default');
    border-radius: var(--el-border-radius-base);
    border: var(--el-border-base);
    box-sizing: border-box;
    height: map.get($--radio-bordered-height, 'default');

    &.is-checked {
      border-color: var(--el-color-primary);
    }

    &.is-disabled {
      cursor: not-allowed;
      border-color: var(--el-border-color-lighter);
    }

    & + .#{$namespace}-radio.is-bordered {
      margin-left: 10px;
    }
  }

  @each $size in (medium, small, mini) {
    &.is-bordered {
      padding: map.get($--radio-bordered-padding, $size);
      border-radius: var(--el-border-radius-base);
      height: map.get($--radio-bordered-height, $size);
      .#{$namespace}-radio__label {
        font-size: map.get($--button-font-size, $size);
      }
      .#{$namespace}-radio__inner {
        height: map.get($--radio-bordered-input-height, $size);
        width: map.get($--radio-bordered-input-width, $size);
      }
    }
  }

  &:last-child {
    margin-right: 0;
  }

  @include e(input) {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-block;
    line-height: 1;
    position: relative;
    vertical-align: middle;

    @include when(disabled) {
      .#{$namespace}-radio__inner {
        background-color: map.get($--radio-disabled, 'input-fill');
        border-color: map.get($--radio-disabled, 'input-border-color');
        cursor: not-allowed;

        &::after {
          cursor: not-allowed;
          background-color: map.get($--radio-disabled, 'icon-color');
        }

        & + .#{$namespace}-radio__label {
          cursor: not-allowed;
        }
      }
      &.is-checked {
        .#{$namespace}-radio__inner {
          background-color: map.get($--radio-disabled, 'checked-input-fill');
          border-color: map.get(
            $--radio-disabled,
            'checked-input-border-color'
          );

          &::after {
            background-color: map.get($--radio-disabled, 'checked-icon-color');
          }
        }
      }
      & + span.#{$namespace}-radio__label {
        color: var(--el-text-color-placeholder);
        cursor: not-allowed;
      }
    }

    @include when(checked) {
      .#{$namespace}-radio__inner {
        border-color: map.get($--radio-checked, 'input-border-color');
        background: map.get($--radio-checked, 'icon-color');

        &::after {
          transform: translate(-50%, -50%) scale(1);
        }
      }

      & + .#{$namespace}-radio__label {
        color: map.get($--radio-checked, 'font-color');
      }
    }

    @include when(focus) {
      .#{$namespace}-radio__inner {
        border-color: var(--el-radio-input-border-color-hover);
      }
    }
  }
  @include e(inner) {
    border: var(--el-radio-input-border);
    border-radius: var(--el-radio-input-border-radius);
    width: var(--el-radio-input-width);
    height: var(--el-radio-input-height);
    background-color: var(--el-radio-input-background-color);
    position: relative;
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;

    &:hover {
      border-color: var(--el-radio-input-border-color-hover);
    }

    &::after {
      width: 4px;
      height: 4px;
      border-radius: var(--el-radio-input-border-radius);
      background-color: var(--el-color-white);
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transition: transform 0.15s ease-in;
    }
  }

  @include e(original) {
    opacity: 0;
    outline: none;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
  }

  &:focus:not(.is-focus):not(:active):not(.is-disabled) {
    /*获得焦点时 样式提醒*/
    .#{$namespace}-radio__inner {
      box-shadow: 0 0 2px 2px var(--el-radio-input-border-color-hover);
    }
  }

  @include e(label) {
    font-size: var(--el-radio-font-size);
    padding-left: 10px;
  }
}
