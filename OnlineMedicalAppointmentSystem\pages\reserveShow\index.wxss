.flex2-clz {
    margin-left: 8px;
    border-bottom-left-radius: 6px;
    box-shadow: 0px 1px 3px rgba(31, 31, 31, 0.16);
    overflow: hidden;
    width: calc(100% - 8px - 8px) !important;
    font-size: 14px;
    border-top-left-radius: 6px;
    margin-top: 8px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    margin-bottom: 8px;
    margin-right: 8px;
}
.grid{
    line-height: 25px;
    font-size: 12px;
    padding:3px;
}