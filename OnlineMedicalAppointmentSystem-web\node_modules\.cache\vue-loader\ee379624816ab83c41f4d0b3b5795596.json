{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\Header.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\Header.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\Header.vue"], "names": [], "mappings": ";AAyFA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACV,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;OACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;;EAEF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;IAEF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;;EAEH,CAAC;AACH,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/Header.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div class=\"nav-header\" style=\"background:#8BC34A;\">\r\n        <a href=\"/main\" class=\"brand-logo\">\r\n\r\n   医院挂号预约系统\r\n        </a>\r\n      \r\n    </div>\r\n\r\n<div class=\"header\" style=\"background:#8BC34A;\">\r\n        <div class=\"header-content\">\r\n            <nav class=\"navbar navbar-expand\">\r\n                <div class=\"collapse navbar-collapse justify-content-between\">\r\n                    <div class=\"header-left\">\r\n       \r\n                    </div>\r\n                    <ul class=\"navbar-nav header-right\">\r\n       \r\n       \r\n        \r\n          <li class=\"nav-item dropdown notification_dropdown\">\r\n              <a class=\"nav-link bell dz-fullscreen\"  href=\"javascript:void(0);\" @click=\"toggleFullScreen\">\r\n               <svg id=\"icon-full\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" stroke=\"currentColor\" stroke-width=\"2\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"css-i6dzq1\"><path d=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\" style=\"stroke-dasharray: 37, 57; stroke-dashoffset: 0;\"></path></svg>\r\n               <svg id=\"icon-minimize\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"A098AE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"feather feather-minimize\"><path d=\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\" style=\"stroke-dasharray: 37, 57; stroke-dashoffset: 0;\"></path></svg>\r\n              </a>\r\n          </li>\t\r\n          <li class=\"nav-item ps-3\">\r\n            <div class=\"dropdown header-profile2\">\r\n              <a class=\"nav-link\" href=\"javascript:void(0);\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" @click=\"toggleShowExist\">\r\n                <div class=\"header-info2 d-flex align-items-center\">\r\n                  <div class=\"header-media\">\r\n                    <img src=\"../assets/images/tab/1.jpg\" alt=\"\">\r\n                  </div>\r\n                  <div class=\"header-info\">\r\n                    <h6>{{userLname}}</h6>\r\n                    <p>{{role}}</p>\r\n                  </div>\r\n                  \r\n                </div>\r\n              </a>\r\n              <div class=\"dropdown-menu dropdown-menu-end\" style=\"display: block; right: 0; top: 50;\" v-show=\"showexist\">\r\n                <div class=\"card border-0 mb-0\">\r\n                  <div class=\"card-header py-2\">\r\n                    <div class=\"products\">\r\n                      <img src=\"../assets/images/tab/1.jpg\" class=\"avatar avatar-md\" alt=\"\">\r\n                      <div>\r\n                        <h6>{{userLname}}</h6>\r\n                        <span>{{role}}</span>\t\r\n                      </div>\t\r\n                    </div>\r\n                  </div>\r\n              \r\n                  <div class=\"card-footer px-0 py-2\" >\r\n                    <!-- <a href=\"/\" target=\"_blank\" class=\"dropdown-item ai-icon \">\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M9.15722 20.7714V17.7047C9.1572 16.9246 9.79312 16.2908 10.581 16.2856H13.4671C14.2587 16.2856 14.9005 16.9209 14.9005 17.7047V17.7047V20.7809C14.9003 21.4432 15.4343 21.9845 16.103 22H18.0271C19.9451 22 21.5 20.4607 21.5 18.5618V18.5618V9.83784C21.4898 9.09083 21.1355 8.38935 20.538 7.93303L13.9577 2.6853C12.8049 1.77157 11.1662 1.77157 10.0134 2.6853L3.46203 7.94256C2.86226 8.39702 2.50739 9.09967 2.5 9.84736V18.5618C2.5 20.4607 4.05488 22 5.97291 22H7.89696C8.58235 22 9.13797 21.4499 9.13797 20.7714V20.7714\" stroke=\"#130F26\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                        </svg>                   \r\n\r\n                      <span class=\"ms-2\">网站首页 </span>\r\n                    </a>    -->\r\n                    \r\n                    <a href=\"javascript:void(0);\" @click=\"changepassword\" class=\"dropdown-item ai-icon \">\r\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                        <path d=\"M16.4232 9.4478V7.3008C16.4232 4.7878 14.3852 2.7498 11.8722 2.7498C9.35925 2.7388 7.31325 4.7668 7.30225 7.2808V7.3008V9.4478\" stroke=\"#130F26\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                        <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.683 21.2496H8.042C5.948 21.2496 4.25 19.5526 4.25 17.4576V13.1686C4.25 11.0736 5.948 9.37659 8.042 9.37659H15.683C17.777 9.37659 19.475 11.0736 19.475 13.1686V17.4576C19.475 19.5526 17.777 21.2496 15.683 21.2496Z\" stroke=\"#130F26\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                        <path d=\"M11.8628 14.2027V16.4237\" stroke=\"#130F26\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                      </svg>\r\n\r\n                      <span class=\"ms-2\">修改密码 </span>\r\n                    </a>\r\n                    <a href=\"javascript:void(0);\" @click=\"exit\" class=\"dropdown-item ai-icon\">\r\n                      <svg class=\"profle-logout\" xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#ff7979\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"></path><polyline points=\"16 17 21 12 16 7\"></polyline><line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"></line></svg>\r\n                      <span class=\"ms-2 text-danger\">退出登录 </span>\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n                \r\n              </div>\r\n            </div>\r\n          </li>\r\n                    </ul>\r\n                </div>\r\n    </nav>\r\n  </div>\r\n</div>\r\n\r\n\r\n</template>\r\n<script>\r\nimport $ from \"jquery\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      activeIndex: \"1\",\r\n      activeIndex2: \"1\",\r\n      showexist: false,\r\n      userLname: \"\",\r\n      role: \"\",\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");  \r\n\r\n    //判断是否登录\r\n    if(this.userLname == null){     \r\n       this.$router.push(\"/\");\r\n    }\r\n\r\n  },\r\n  methods: {\r\n    handleSelect(key, keyPath) {\r\n      console.log(key, keyPath);\r\n    },\r\n    toggleShowExist() {    \r\n      this.showexist = !this.showexist;\r\n\r\n      if(this.showexist){\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      }else{\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }   \r\n\r\n    },\r\n\r\n    toggleFullScreen() {\r\n      var elem = document.documentElement;\r\n      if (elem.requestFullscreen) {\r\n        elem.requestFullscreen();\r\n      } else if (elem.webkitRequestFullscreen) {\r\n        elem.webkitRequestFullscreen();\r\n      } else if (elem.msRequestFullscreen) {\r\n        elem.msRequestFullscreen(); \r\n      }\r\n    },\r\n\r\n    //修改密码\r\n    changepassword(){\r\n      this.$router.push(\"/password\");\r\n    },\r\n\r\n    //退出登录  \r\n    exit: function() {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    \r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.skin1{\r\n  background: #607D8B;\r\n}\r\n\r\n\r\n\r\n.dropdown-menu-end{\r\n  right: 0;\r\n  top: 50px;\r\n}\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}