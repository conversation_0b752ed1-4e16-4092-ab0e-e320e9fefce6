@import '../common/var';

@include b(time-spinner) {
  &.has-seconds {
    .#{$namespace}-time-spinner__wrapper {
      width: 33.3%;
    }
  }

  @include e(wrapper) {
    max-height: 192px;
    overflow: auto;
    display: inline-block;
    width: 50%;
    vertical-align: top;
    position: relative;

    &
      .#{$namespace}-scrollbar__wrap:not(.#{$namespace}-scrollbar__wrap--hidden-default) {
      padding-bottom: 15px;
    }

    @include when(arrow) {
      box-sizing: border-box;
      text-align: center;
      overflow: hidden;

      .#{$namespace}-time-spinner__list {
        transform: translateY(-32px);
      }

      .#{$namespace}-time-spinner__item:hover:not(.disabled):not(.active) {
        background: $--color-white;
        cursor: default;
      }
    }
  }

  @include e(arrow) {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    position: absolute;
    left: 0;
    width: 100%;
    z-index: var(--el-index-normal);
    text-align: center;
    height: 30px;
    line-height: 30px;
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary);
    }

    &.#{$namespace}-icon-arrow-up {
      top: 10px;
    }

    &.#{$namespace}-icon-arrow-down {
      bottom: 10px;
    }
  }

  @include e(input) {
    &.#{$namespace}-input {
      width: 70%;

      .#{$namespace}-input__inner {
        padding: 0;
        text-align: center;
      }
    }
  }

  @include e(list) {
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center;

    &::after,
    &::before {
      content: '';
      display: block;
      width: 100%;
      height: 80px;
    }
  }

  @include e(item) {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: var(--el-text-color-regular);

    &:hover:not(.disabled):not(.active) {
      background: $--background-color-base;
      cursor: pointer;
    }

    &.active:not(.disabled) {
      color: var(--el-text-color-primary);
      font-weight: bold;
    }

    &.disabled {
      color: var(--el-text-color-placeholder);
      cursor: not-allowed;
    }
  }
}
