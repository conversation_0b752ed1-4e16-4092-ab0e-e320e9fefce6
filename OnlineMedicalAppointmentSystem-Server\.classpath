<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="melibrary.com.genuitec.eclipse.j2eedt.core.MYECLIPSE_JAVAEE_5_CONTAINER"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/aliyun-java-sdk-core-3.3.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/aliyun-java-sdk-dysmsapi-1.0.0.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/antlr-2.7.6rc1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/aopalliance-1.0.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/asm-3.3.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/asm-attrs.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/asm.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/aspectjweaver-1.6.11.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/c3p0-0.9.0.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/c3p0-*******.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/cglib-2.1.3.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/cglib-2.2.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-attributes-api.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-attributes-compiler.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-codec-1.9.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-collections-3.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-dbcp-1.2.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-dbcp.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-fileupload-1.3.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-io-2.4.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-lang.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-logging-1.0.4.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-logging-1.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/commons-pool-1.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/connector.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/cos.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/dwr.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/ehcache-1.2.4.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/FCKeditor-2.3.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/fckeditor-java-core-2.4.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/freemarker-2.3.8.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/freemarker.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/gson-2.3.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/hibernate3.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/httpclient-4.5.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/httpcore-4.4.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jakarta-oro-2.0.8.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/java-core-2.4.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/javaee.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/javassist-3.17.1-GA.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jcommon-1.0.14.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jdbc2_0-stdext.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jdo2-api.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jotm.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/json.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jspsmartupload.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jstl-1.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/jta.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/liuliu.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/log4j-1.2.14.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/msbase.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/mssqlserver.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/msutil.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/mybatis-3.2.7.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/mybatis-spring-1.2.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/mysql-connector-java-5.0.4-bin.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/ognl-2.6.11.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/oro-2.0.8.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/oscache-2.1.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/pager-taglib-2.0.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/persistence.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/poi-3.15.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/proxool-0.8.3.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/slf4j-api-1.5.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/slf4j-api-1.7.5.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/slf4j-log4j12-1.7.5.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/slf4j-simple-1.5.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-aspects-3.2.0.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/sqljdbc.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/ueditor-1.1.2.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/xapool.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/xml-apis.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/xwork-2.0.4.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="owner.project.facets" value="java"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-aop-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-beans-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-context-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-context-support-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-core-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-expression-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-jdbc-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-oxm-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-test-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-tx-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-web-4.3.10.RELEASE.jar"/>
	<classpathentry kind="lib" path="WebRoot/WEB-INF/lib/spring-webmvc-4.3.10.RELEASE.jar"/>
	<classpathentry kind="output" path="WebRoot/WEB-INF/classes"/>
</classpath>
