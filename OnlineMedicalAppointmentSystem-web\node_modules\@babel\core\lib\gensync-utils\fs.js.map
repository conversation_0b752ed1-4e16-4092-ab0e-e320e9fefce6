{"version": 3, "names": ["_fs", "data", "require", "_gensync", "readFile", "exports", "gens<PERSON>", "sync", "fs", "readFileSync", "errback", "stat", "statSync"], "sources": ["../../src/gensync-utils/fs.ts"], "sourcesContent": ["import fs from \"node:fs\";\nimport gensync from \"gensync\";\n\nexport const readFile = gensync<[filepath: string, encoding: \"utf8\"], string>({\n  sync: fs.readFileSync,\n  errback: fs.readFile,\n});\n\nexport const stat = gensync({\n  sync: fs.statSync,\n  errback: fs.stat,\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAGE,SAAMA,CAAC,CAA+C;EAC5EC,IAAI,EAAEC,IAACA,CAAC,CAACC,YAAY;EACrBC,OAAO,EAAEF,IAACA,CAAC,CAACJ;AACd,CAAC,CAAC;AAEK,MAAMO,IAAI,GAAAN,OAAA,CAAAM,IAAA,GAAGL,SAAMA,CAAC,CAAC;EAC1BC,IAAI,EAAEC,IAACA,CAAC,CAACI,QAAQ;EACjBF,OAAO,EAAEF,IAACA,CAAC,CAACG;AACd,CAAC,CAAC;AAAC", "ignoreList": []}