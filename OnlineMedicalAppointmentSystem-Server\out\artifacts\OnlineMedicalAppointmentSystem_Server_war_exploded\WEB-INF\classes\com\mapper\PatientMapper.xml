<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.PatientMapper">
	<select id="findPatientList"  resultType="Patient">
		select * from patient 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Patient">
	    select  *  
        from patient a  	
		<where>
      		<if test="peoid != null and peoid !=0 ">
		    and a.peoid = #{peoid}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="peoname != null and peoname != ''">
		    and a.peoname = #{peoname}
		</if>
		<if test="phone != null and phone != ''">
		    and a.phone = #{phone}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} peoid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from patient a  
		<where>
      		<if test="peoid != null and peoid !=0 ">
		    and a.peoid = #{peoid}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="peoname != null and peoname != ''">
		    and a.peoname = #{peoname}
		</if>
		<if test="phone != null and phone != ''">
		    and a.phone = #{phone}
		</if>
		<if test="gender != null and gender != ''">
		    and a.gender = #{gender}
		</if>
		<if test="age != null and age !=0 ">
		    and a.age = #{age}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryPatientById" parameterType="int" resultType="Patient">
    select  *  
     from patient a  	 where a.peoid=#{value}
  </select>
 
	<insert id="insertPatient" useGeneratedKeys="true" keyProperty="peoid" parameterType="Patient">
    insert into patient
    (lname,peoname,phone,gender,age)
    values
    (#{lname},#{peoname},#{phone},#{gender},#{age});
  </insert>
	
	<update id="updatePatient" parameterType="Patient" >
    update patient 
    <set>
		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="peoname != null and peoname != ''">
		    peoname = #{peoname},
		</if>
		<if test="phone != null and phone != ''">
		    phone = #{phone},
		</if>
		<if test="gender != null and gender != ''">
		    gender = #{gender},
		</if>
		<if test="age != null ">
		    age = #{age},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="peoid != null or peoid != ''">
      peoid=#{peoid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deletePatient" parameterType="int">
    delete from  patient where peoid=#{value}
  </delete>

	
	
</mapper>

 
