{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue?vue&type=style&index=0&id=634fc380&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZWRpdG9yIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1hcmdpbjogMCBhdXRvOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHotaW5kZXg6IDA7DQp9DQoNCi50b29sYmFyMSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7DQp9DQoNCi50ZXh0IHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2NjYzsNCiAgbWluLWhlaWdodDogMjAwcHg7DQp9DQo="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue"], "names": [], "mappings": ";AAmLA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACZ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/WangEditor.vue", "sourceRoot": "", "sourcesContent": ["<template lang=\"html\">\r\n  <div class=\"editor\">\r\n    <div ref=\"toolbar\" class=\"toolbar1\">\r\n    </div>\r\n    <div ref=\"editor\" class=\"text\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport E from \"wangeditor\";\r\nimport request, { base } from \"../../utils/http\";\r\n\r\nexport default {\r\n  name: \"editorItem\",\r\n  data() {\r\n    return {\r\n      // uploadPath,\r\n      editor: null,\r\n      info_: null,\r\n    };\r\n  },\r\n\r\n  props: {\r\n    modelValue: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    isClear: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n\r\n\r\n  watch: {\r\n    isClear(val) {\r\n      // 触发清除文本域内容\r\n      if (val) {\r\n        this.editor.txt.clear();\r\n        this.info_ = null;\r\n      }\r\n    },\r\n    modelValue: function (value) {\r\n      console.log(value);\r\n      if (value !== this.editor.txt.html()) {\r\n        this.editor.txt.html(this.value);\r\n      }\r\n    },\r\n    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值\r\n  },\r\n\r\n  mounted() {\r\n    this.seteditor();\r\n    this.editor.txt.html(this.modelValue);\r\n  },\r\n  methods: {\r\n    seteditor() {\r\n      this.editor = new E(this.$refs.toolbar, this.$refs.editor);\r\n      this.editor.config.uploadImgShowBase64 = false; // base 64 存储图片\r\n      this.editor.config.uploadImgServer = base + \"/common/uploadFile\"; // 配置服务器端地址\r\n      this.editor.config.uploadImgHeaders = {}; // 自定义 header\r\n      this.editor.config.uploadFileName = \"file\"; // 后端接受上传文件的参数名\r\n      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M\r\n      this.editor.config.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片\r\n      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间\r\n\r\n      // 配置菜单\r\n      this.editor.config.menus = [\r\n        \"head\", // 标题\r\n        \"bold\", // 粗体\r\n        \"fontSize\", // 字号\r\n        \"fontName\", // 字体\r\n        \"italic\", // 斜体\r\n        \"underline\", // 下划线\r\n        \"strikeThrough\", // 删除线\r\n        \"foreColor\", // 文字颜色\r\n        \"backColor\", // 背景颜色\r\n        \"link\", // 插入链接\r\n        \"list\", // 列表\r\n        \"justify\", // 对齐方式\r\n        \"quote\", // 引用\r\n        \"emoticon\", // 表情\r\n        \"image\", // 插入图片\r\n        \"table\", // 表格\r\n        \"video\", // 插入视频\r\n        \"code\", // 插入代码\r\n        \"undo\", // 撤销\r\n        \"redo\", // 重复\r\n        \"fullscreen\", // 全屏\r\n      ];\r\n\r\n      this.editor.config.uploadImgHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 插入图片失败回调\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 图片上传成功回调\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 网络超时的回调\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 图片上传错误的回调\r\n        },\r\n        customInsert: (insertImg, result, editor) => {\r\n          // 图片上传成功，插入图片的回调\r\n          //result为上传图片成功的时候返回的数据，这里我打印了一下发现后台返回的是data：[{url:\"路径的形式\"},...]\r\n          // console.log(result.data[0].url)\r\n          //insertImg()为插入图片的函数\r\n          //循环插入图片\r\n          // for (let i = 0; i < 1; i++) {\r\n          console.log(result);\r\n          let url = result.resdata.url;\r\n          insertImg(url);\r\n          // }\r\n        },\r\n      };\r\n\r\n      // 在menus配置后添加视频上传相关配置\r\n      this.editor.config.uploadVideoServer = base + \"/common/uploadFile\"; // 视频上传服务器地址\r\n      this.editor.config.uploadVideoName = \"file\"; // 视频上传参数名\r\n      this.editor.config.uploadVideoMaxSize = 100 * 1024 * 1024; // 限制视频大小为100M\r\n      this.editor.config.uploadVideoAccept = ['mp4', 'mov', 'avi', 'wmv']; // 限制视频格式\r\n\r\n      // 添加视频上传的钩子函数\r\n      this.editor.config.uploadVideoHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 视频上传失败的回调\r\n          console.log('视频上传失败', result)\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 视频上传成功的回调\r\n          console.log('视频上传成功', result)\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 视频上传错误的回调\r\n          console.log('视频上传错误')\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 视频上传超时的回调\r\n          console.log('视频上传超时')\r\n        },\r\n        customInsert: (insertVideo, result) => {\r\n          console.log('插入视频', result)\r\n          let url = result.resdata.url\r\n          // 设置视频的显示样式\r\n          insertVideo(url, null, {\r\n            width: '100%',\r\n            height: 'auto',\r\n            controls: 'controls'\r\n          })\r\n        }\r\n      }\r\n\r\n      // 配置视频上传的校验\r\n      this.editor.config.uploadVideoCheck = function (videoFile) {\r\n        // 视频大小限制\r\n        if (videoFile.size > 100 * 1024 * 1024) {\r\n          return '视频大小不能超过100M'\r\n        }\r\n        return true\r\n      }\r\n\r\n      this.editor.config.onchange = (html) => {\r\n        this.info_ = html; // 绑定当前组件的值\r\n        this.$emit(\"change\", this.info_); // 将内容同步到父组件中\r\n      };\r\n      // 创建富文本编辑器\r\n      this.editor.create();\r\n      this.editor.txt.html(this.modelValue)\r\n\r\n\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"css\">\r\n.editor {\r\n  width: 100%;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  z-index: 0;\r\n}\r\n\r\n.toolbar1 {\r\n  border: 1px solid #ccc;\r\n}\r\n\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n"]}]}