package com.service;

import com.mapper.StatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Override
    public int getPatientCount() throws Exception {
        return statisticsMapper.getPatientCount();
    }

    @Override
    public int getDoctorCount() throws Exception {
        return statisticsMapper.getDoctorCount();
    }

    @Override
    public int getTodayAppointmentCount() throws Exception {
        return statisticsMapper.getTodayAppointmentCount();
    }

    @Override
    public int getTotalAppointmentCount() throws Exception {
        return statisticsMapper.getTotalAppointmentCount();
    }

    @Override
    public int getDoctorTodayAppointmentCount(Integer doctorId) throws Exception {
        return statisticsMapper.getDoctorTodayAppointmentCount(doctorId);
    }

    @Override
    public int getDoctorTotalAppointmentCount(Integer doctorId) throws Exception {
        return statisticsMapper.getDoctorTotalAppointmentCount(doctorId);
    }
}
