<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE taglib
  PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.1//EN"
  "http://java.sun.com/j2ee/dtds/web-jsptaglibrary_1_1.dtd">

<taglib>
  <tlibversion>1.0</tlibversion>
  <jspversion>1.1</jspversion>
  <shortname>pg</shortname>
  <uri>http://jsptags.com/tags/navigation/pager</uri>
  <info>
    The Pager Tag Library is the easy and flexible way to implement paging of
    large data sets in JavaServer Pages (JSP). It can emulate all currently
    known paging styles with minimal effort. It also includes re-usable index
    styles that emulate the search result navigators of popular web sites
    such as Google[sm], AltaVista® and Yahoo!. The Pager Tag Library does most
    of the work for you by dynamically organizing your data set into pages and
    generating a browsable index with virtually any look desired.
  </info>

  <tag>
    <name>pager</name>
    <tagclass>com.jsptags.navigation.pager.PagerTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.PagerTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>url</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>items</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>maxItems</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>maxPageItems</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>maxIndexPages</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>isOffset</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>index</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>scope</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>param</name>
    <tagclass>com.jsptags.navigation.pager.ParamTag</tagclass>
    <bodycontent>empty</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>name</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>value</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>item</name>
    <tagclass>com.jsptags.navigation.pager.ItemTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>index</name>
    <tagclass>com.jsptags.navigation.pager.IndexTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.IndexTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>first</name>
    <tagclass>com.jsptags.navigation.pager.FirstTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.JumpTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
      <name>unless</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>prev</name>
    <tagclass>com.jsptags.navigation.pager.PrevTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.PageTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
      <name>ifnull</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>page</name>
    <tagclass>com.jsptags.navigation.pager.PageTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.JumpTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>pages</name>
    <tagclass>com.jsptags.navigation.pager.PagesTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.PageTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>next</name>
    <tagclass>com.jsptags.navigation.pager.NextTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.PageTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
      <name>ifnull</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>last</name>
    <tagclass>com.jsptags.navigation.pager.LastTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.JumpTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
      <name>unless</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>skip</name>
    <tagclass>com.jsptags.navigation.pager.SkipTag</tagclass>
    <teiclass>com.jsptags.navigation.pager.PageTagExtraInfo</teiclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>id</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>export</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
      <name>ifnull</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>pages</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

</taglib>
