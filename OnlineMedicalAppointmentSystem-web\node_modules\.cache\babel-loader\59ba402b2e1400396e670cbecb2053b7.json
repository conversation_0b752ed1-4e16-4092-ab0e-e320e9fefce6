{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["E", "request", "base", "name", "data", "editor", "info_", "props", "modelValue", "type", "String", "default", "isClear", "Boolean", "watch", "val", "txt", "clear", "value", "console", "log", "html", "mounted", "seteditor", "methods", "$refs", "toolbar", "config", "uploadImgShowBase64", "uploadImgServer", "uploadImgHeaders", "uploadFileName", "uploadImgMaxSize", "uploadImgMaxLength", "uploadImgTimeout", "menus", "uploadImgHooks", "fail", "xhr", "result", "success", "timeout", "error", "customInsert", "insertImg", "url", "resdata", "uploadVideoServer", "uploadVideoName", "uploadVideoMaxSize", "uploadVideoAccept", "uploadVideoHooks", "insertVideo", "width", "height", "controls", "uploadVideoCheck", "videoFile", "size", "onchange", "$emit", "create"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue"], "sourcesContent": ["<template lang=\"html\">\r\n  <div class=\"editor\">\r\n    <div ref=\"toolbar\" class=\"toolbar1\">\r\n    </div>\r\n    <div ref=\"editor\" class=\"text\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport E from \"wangeditor\";\r\nimport request, { base } from \"../../utils/http\";\r\n\r\nexport default {\r\n  name: \"editorItem\",\r\n  data() {\r\n    return {\r\n      // uploadPath,\r\n      editor: null,\r\n      info_: null,\r\n    };\r\n  },\r\n\r\n  props: {\r\n    modelValue: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    isClear: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n\r\n\r\n  watch: {\r\n    isClear(val) {\r\n      // 触发清除文本域内容\r\n      if (val) {\r\n        this.editor.txt.clear();\r\n        this.info_ = null;\r\n      }\r\n    },\r\n    modelValue: function (value) {\r\n      console.log(value);\r\n      if (value !== this.editor.txt.html()) {\r\n        this.editor.txt.html(this.value);\r\n      }\r\n    },\r\n    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值\r\n  },\r\n\r\n  mounted() {\r\n    this.seteditor();\r\n    this.editor.txt.html(this.modelValue);\r\n  },\r\n  methods: {\r\n    seteditor() {\r\n      this.editor = new E(this.$refs.toolbar, this.$refs.editor);\r\n      this.editor.config.uploadImgShowBase64 = false; // base 64 存储图片\r\n      this.editor.config.uploadImgServer = base + \"/common/uploadFile\"; // 配置服务器端地址\r\n      this.editor.config.uploadImgHeaders = {}; // 自定义 header\r\n      this.editor.config.uploadFileName = \"file\"; // 后端接受上传文件的参数名\r\n      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M\r\n      this.editor.config.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片\r\n      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间\r\n\r\n      // 配置菜单\r\n      this.editor.config.menus = [\r\n        \"head\", // 标题\r\n        \"bold\", // 粗体\r\n        \"fontSize\", // 字号\r\n        \"fontName\", // 字体\r\n        \"italic\", // 斜体\r\n        \"underline\", // 下划线\r\n        \"strikeThrough\", // 删除线\r\n        \"foreColor\", // 文字颜色\r\n        \"backColor\", // 背景颜色\r\n        \"link\", // 插入链接\r\n        \"list\", // 列表\r\n        \"justify\", // 对齐方式\r\n        \"quote\", // 引用\r\n        \"emoticon\", // 表情\r\n        \"image\", // 插入图片\r\n        \"table\", // 表格\r\n        \"video\", // 插入视频\r\n        \"code\", // 插入代码\r\n        \"undo\", // 撤销\r\n        \"redo\", // 重复\r\n        \"fullscreen\", // 全屏\r\n      ];\r\n\r\n      this.editor.config.uploadImgHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 插入图片失败回调\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 图片上传成功回调\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 网络超时的回调\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 图片上传错误的回调\r\n        },\r\n        customInsert: (insertImg, result, editor) => {\r\n          // 图片上传成功，插入图片的回调\r\n          //result为上传图片成功的时候返回的数据，这里我打印了一下发现后台返回的是data：[{url:\"路径的形式\"},...]\r\n          // console.log(result.data[0].url)\r\n          //insertImg()为插入图片的函数\r\n          //循环插入图片\r\n          // for (let i = 0; i < 1; i++) {\r\n          console.log(result);\r\n          let url = result.resdata.url;\r\n          insertImg(url);\r\n          // }\r\n        },\r\n      };\r\n\r\n      // 在menus配置后添加视频上传相关配置\r\n      this.editor.config.uploadVideoServer = base + \"/common/uploadFile\"; // 视频上传服务器地址\r\n      this.editor.config.uploadVideoName = \"file\"; // 视频上传参数名\r\n      this.editor.config.uploadVideoMaxSize = 100 * 1024 * 1024; // 限制视频大小为100M\r\n      this.editor.config.uploadVideoAccept = ['mp4', 'mov', 'avi', 'wmv']; // 限制视频格式\r\n\r\n      // 添加视频上传的钩子函数\r\n      this.editor.config.uploadVideoHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 视频上传失败的回调\r\n          console.log('视频上传失败', result)\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 视频上传成功的回调\r\n          console.log('视频上传成功', result)\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 视频上传错误的回调\r\n          console.log('视频上传错误')\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 视频上传超时的回调\r\n          console.log('视频上传超时')\r\n        },\r\n        customInsert: (insertVideo, result) => {\r\n          console.log('插入视频', result)\r\n          let url = result.resdata.url\r\n          // 设置视频的显示样式\r\n          insertVideo(url, null, {\r\n            width: '100%',\r\n            height: 'auto',\r\n            controls: 'controls'\r\n          })\r\n        }\r\n      }\r\n\r\n      // 配置视频上传的校验\r\n      this.editor.config.uploadVideoCheck = function (videoFile) {\r\n        // 视频大小限制\r\n        if (videoFile.size > 100 * 1024 * 1024) {\r\n          return '视频大小不能超过100M'\r\n        }\r\n        return true\r\n      }\r\n\r\n      this.editor.config.onchange = (html) => {\r\n        this.info_ = html; // 绑定当前组件的值\r\n        this.$emit(\"change\", this.info_); // 将内容同步到父组件中\r\n      };\r\n      // 创建富文本编辑器\r\n      this.editor.create();\r\n      this.editor.txt.html(this.modelValue)\r\n\r\n\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"css\">\r\n.editor {\r\n  width: 100%;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  z-index: 0;\r\n}\r\n\r\n.toolbar1 {\r\n  border: 1px solid #ccc;\r\n}\r\n\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAUA,OAAOA,CAAA,MAAO,YAAY;AAC1B,OAAOC,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAEhD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAEDC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX;EACF,CAAC;EAGDG,KAAK,EAAE;IACLF,OAAOA,CAACG,GAAG,EAAE;MACX;MACA,IAAIA,GAAG,EAAE;QACP,IAAI,CAACV,MAAM,CAACW,GAAG,CAACC,KAAK,CAAC,CAAC;QACvB,IAAI,CAACX,KAAI,GAAI,IAAI;MACnB;IACF,CAAC;IACDE,UAAU,EAAE,SAAAA,CAAUU,KAAK,EAAE;MAC3BC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;MAClB,IAAIA,KAAI,KAAM,IAAI,CAACb,MAAM,CAACW,GAAG,CAACK,IAAI,CAAC,CAAC,EAAE;QACpC,IAAI,CAAChB,MAAM,CAACW,GAAG,CAACK,IAAI,CAAC,IAAI,CAACH,KAAK,CAAC;MAClC;IACF;IACA;EACF,CAAC;EAEDI,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAAClB,MAAM,CAACW,GAAG,CAACK,IAAI,CAAC,IAAI,CAACb,UAAU,CAAC;EACvC,CAAC;EACDgB,OAAO,EAAE;IACPD,SAASA,CAAA,EAAG;MACV,IAAI,CAAClB,MAAK,GAAI,IAAIL,CAAC,CAAC,IAAI,CAACyB,KAAK,CAACC,OAAO,EAAE,IAAI,CAACD,KAAK,CAACpB,MAAM,CAAC;MAC1D,IAAI,CAACA,MAAM,CAACsB,MAAM,CAACC,mBAAkB,GAAI,KAAK,EAAE;MAChD,IAAI,CAACvB,MAAM,CAACsB,MAAM,CAACE,eAAc,GAAI3B,IAAG,GAAI,oBAAoB,EAAE;MAClE,IAAI,CAACG,MAAM,CAACsB,MAAM,CAACG,gBAAe,GAAI,CAAC,CAAC,EAAE;MAC1C,IAAI,CAACzB,MAAM,CAACsB,MAAM,CAACI,cAAa,GAAI,MAAM,EAAE;MAC5C,IAAI,CAAC1B,MAAM,CAACsB,MAAM,CAACK,gBAAe,GAAI,IAAI,IAAG,GAAI,IAAI,EAAE;MACvD,IAAI,CAAC3B,MAAM,CAACsB,MAAM,CAACM,kBAAiB,GAAI,CAAC,EAAE;MAC3C,IAAI,CAAC5B,MAAM,CAACsB,MAAM,CAACO,gBAAe,GAAI,IAAI,EAAC,GAAI,IAAI,EAAE;;MAErD;MACA,IAAI,CAAC7B,MAAM,CAACsB,MAAM,CAACQ,KAAI,GAAI,CACzB,MAAM;MAAE;MACR,MAAM;MAAE;MACR,UAAU;MAAE;MACZ,UAAU;MAAE;MACZ,QAAQ;MAAE;MACV,WAAW;MAAE;MACb,eAAe;MAAE;MACjB,WAAW;MAAE;MACb,WAAW;MAAE;MACb,MAAM;MAAE;MACR,MAAM;MAAE;MACR,SAAS;MAAE;MACX,OAAO;MAAE;MACT,UAAU;MAAE;MACZ,OAAO;MAAE;MACT,OAAO;MAAE;MACT,OAAO;MAAE;MACT,MAAM;MAAE;MACR,MAAM;MAAE;MACR,MAAM;MAAE;MACR,YAAY,CAAE;MAAA,CACf;MAED,IAAI,CAAC9B,MAAM,CAACsB,MAAM,CAACS,cAAa,GAAI;QAClCC,IAAI,EAAEA,CAACC,GAAG,EAAEjC,MAAM,EAAEkC,MAAM,KAAK;UAC7B;QAAA,CACD;QACDC,OAAO,EAAEA,CAACF,GAAG,EAAEjC,MAAM,EAAEkC,MAAM,KAAK;UAChC;QAAA,CACD;QACDE,OAAO,EAAEA,CAACH,GAAG,EAAEjC,MAAM,KAAK;UACxB;QAAA,CACD;QACDqC,KAAK,EAAEA,CAACJ,GAAG,EAAEjC,MAAM,KAAK;UACtB;QAAA,CACD;QACDsC,YAAY,EAAEA,CAACC,SAAS,EAAEL,MAAM,EAAElC,MAAM,KAAK;UAC3C;UACA;UACA;UACA;UACA;UACA;UACAc,OAAO,CAACC,GAAG,CAACmB,MAAM,CAAC;UACnB,IAAIM,GAAE,GAAIN,MAAM,CAACO,OAAO,CAACD,GAAG;UAC5BD,SAAS,CAACC,GAAG,CAAC;UACd;QACF;MACF,CAAC;;MAED;MACA,IAAI,CAACxC,MAAM,CAACsB,MAAM,CAACoB,iBAAgB,GAAI7C,IAAG,GAAI,oBAAoB,EAAE;MACpE,IAAI,CAACG,MAAM,CAACsB,MAAM,CAACqB,eAAc,GAAI,MAAM,EAAE;MAC7C,IAAI,CAAC3C,MAAM,CAACsB,MAAM,CAACsB,kBAAiB,GAAI,GAAE,GAAI,IAAG,GAAI,IAAI,EAAE;MAC3D,IAAI,CAAC5C,MAAM,CAACsB,MAAM,CAACuB,iBAAgB,GAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE;;MAErE;MACA,IAAI,CAAC7C,MAAM,CAACsB,MAAM,CAACwB,gBAAe,GAAI;QACpCd,IAAI,EAAEA,CAACC,GAAG,EAAEjC,MAAM,EAAEkC,MAAM,KAAK;UAC7B;UACApB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEmB,MAAM;QAC9B,CAAC;QACDC,OAAO,EAAEA,CAACF,GAAG,EAAEjC,MAAM,EAAEkC,MAAM,KAAK;UAChC;UACApB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEmB,MAAM;QAC9B,CAAC;QACDG,KAAK,EAAEA,CAACJ,GAAG,EAAEjC,MAAM,KAAK;UACtB;UACAc,OAAO,CAACC,GAAG,CAAC,QAAQ;QACtB,CAAC;QACDqB,OAAO,EAAEA,CAACH,GAAG,EAAEjC,MAAM,KAAK;UACxB;UACAc,OAAO,CAACC,GAAG,CAAC,QAAQ;QACtB,CAAC;QACDuB,YAAY,EAAEA,CAACS,WAAW,EAAEb,MAAM,KAAK;UACrCpB,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEmB,MAAM;UAC1B,IAAIM,GAAE,GAAIN,MAAM,CAACO,OAAO,CAACD,GAAE;UAC3B;UACAO,WAAW,CAACP,GAAG,EAAE,IAAI,EAAE;YACrBQ,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE;UACZ,CAAC;QACH;MACF;;MAEA;MACA,IAAI,CAAClD,MAAM,CAACsB,MAAM,CAAC6B,gBAAe,GAAI,UAAUC,SAAS,EAAE;QACzD;QACA,IAAIA,SAAS,CAACC,IAAG,GAAI,GAAE,GAAI,IAAG,GAAI,IAAI,EAAE;UACtC,OAAO,cAAa;QACtB;QACA,OAAO,IAAG;MACZ;MAEA,IAAI,CAACrD,MAAM,CAACsB,MAAM,CAACgC,QAAO,GAAKtC,IAAI,IAAK;QACtC,IAAI,CAACf,KAAI,GAAIe,IAAI,EAAE;QACnB,IAAI,CAACuC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAACtD,KAAK,CAAC,EAAE;MACpC,CAAC;MACD;MACA,IAAI,CAACD,MAAM,CAACwD,MAAM,CAAC,CAAC;MACpB,IAAI,CAACxD,MAAM,CAACW,GAAG,CAACK,IAAI,CAAC,IAAI,CAACb,UAAU;IAGtC;EACF;AACF,CAAC", "ignoreList": []}]}