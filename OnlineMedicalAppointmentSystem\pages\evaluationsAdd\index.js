const App = getApp();
Page({
  data: {
    score: 0,
    lname: "",
    rate: 5, // 评分, 1-5
    evaluation: null,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
        lname: wx.getStorageSync("lname"),
      });
    }

    // 检查是否已有评价
    this.checkEvaluation();
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {},

  // 检查是否已有评价
  checkEvaluation() {
    let param = {
      rid: this.data.globalOption.rid,
      loadmsg: "正在加载评价",
    };

    App.HttpService.getData(param, "/evaluations_List").then((res) => {
      if (res.data && res.data.length > 0) {
        this.setData({
          evaluation: res.data[0],
        });
      }
    });
  },

  // 改变评分
  changeRate(e) {
    const rate = e.currentTarget.dataset.rate;
    this.setData({
      rate: rate,
    });
  },

  // 设置评分
  setScore(e) {
    this.setData({
      score: e.currentTarget.dataset.score,
    });
  },

  // 提交评价
  submitForm(e) {
    if (this.data.rate === 0) {
      this.showModal("请选择评分");
      return;
    }

    const formData = e.detail.value;
    if (!formData.comment) {
      this.showModal("请输入评价内容");
      return;
    }

    formData.score = this.data.rate;
    formData.rid = this.data.globalOption.rid;
    formData.did = this.data.globalOption.did;
    formData.lname = wx.getStorageSync("lname");

    App.HttpService.saveData(formData, "/evaluations_Add").then((res) => {
      App.WxService.showToast({
        title: "评价成功！",
        icon: "success",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  resetForm: function () {
    console.log("form发生了reset事件");
  },
});
