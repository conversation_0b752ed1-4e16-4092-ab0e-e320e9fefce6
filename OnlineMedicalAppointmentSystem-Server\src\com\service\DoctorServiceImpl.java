package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.DoctorMapper;
import com.model.Doctor;
import com.util.PageBean;
@Service
public class DoctorServiceImpl implements DoctorService{
        
	@Autowired
	private DoctorMapper doctorMapper;

	//查询多条记录
	public List<Doctor> queryDoctorList(Doctor doctor,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(doctor, page);
		
		List<Doctor> getDoctor = doctorMapper.query(map);
		
		return getDoctor;
	}
	
	//得到记录总数
	@Override
	public int getCount(Doctor doctor) {
		Map<String, Object> map = getQueryMap(doctor, null);
		int count = doctorMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Doctor doctor,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(doctor!=null){
			map.put("did", doctor.getDid());
			map.put("daccount", doctor.getDaccount());
			map.put("password", doctor.getPassword());
			map.put("dname", doctor.getDname());
			map.put("sex", doctor.getSex());
			map.put("photo", doctor.getPhoto());
			map.put("jobs", doctor.getJobs());
			map.put("tel", doctor.getTel());
			map.put("shac", doctor.getShac());
			map.put("price", doctor.getPrice());
			map.put("pid", doctor.getPid());
			map.put("dmemo", doctor.getDmemo());
			map.put("addtime", doctor.getAddtime());
			map.put("sort", doctor.getSort());
			map.put("condition", doctor.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertDoctor(Doctor doctor) throws Exception {
		return doctorMapper.insertDoctor(doctor);
	}

	//根据ID删除
	public int deleteDoctor(int id) throws Exception {
		return doctorMapper.deleteDoctor(id);
	}

	//更新
	public int updateDoctor(Doctor doctor) throws Exception {
		return doctorMapper.updateDoctor(doctor);
	}
	
	//根据ID得到对应的记录
	public Doctor queryDoctorById(int id) throws Exception {
		Doctor po =  doctorMapper.queryDoctorById(id);
		return po;
	}
}

