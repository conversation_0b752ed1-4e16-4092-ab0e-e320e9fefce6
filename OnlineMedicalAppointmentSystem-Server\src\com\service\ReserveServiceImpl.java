package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ReserveMapper;
import com.model.Reserve;
import com.util.PageBean;
@Service
public class ReserveServiceImpl implements ReserveService{
        
	@Autowired
	private ReserveMapper reserveMapper;

	//查询多条记录
	public List<Reserve> queryReserveList(Reserve reserve,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(reserve, page);
		
		List<Reserve> getReserve = reserveMapper.query(map);
		
		return getReserve;
	}
	
	//得到记录总数
	@Override
	public int getCount(Reserve reserve) {
		Map<String, Object> map = getQueryMap(reserve, null);
		int count = reserveMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Reserve reserve,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(reserve!=null){
			map.put("rid", reserve.getRid());
			map.put("pid", reserve.getPid());
			map.put("did", reserve.getDid());
			map.put("plid", reserve.getPlid());
			map.put("rdate", reserve.getRdate());
			map.put("rtime", reserve.getRtime());
			map.put("pmoney", reserve.getPmoney());
			map.put("lname", reserve.getLname());
			map.put("peoid", reserve.getPeoid());
			map.put("addtime", reserve.getAddtime());
			map.put("flag", reserve.getFlag());
			map.put("results", reserve.getResults());
			map.put("sort", reserve.getSort());
			map.put("condition", reserve.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertReserve(Reserve reserve) throws Exception {
		return reserveMapper.insertReserve(reserve);
	}

	//根据ID删除
	public int deleteReserve(int id) throws Exception {
		return reserveMapper.deleteReserve(id);
	}

	//更新
	public int updateReserve(Reserve reserve) throws Exception {
		return reserveMapper.updateReserve(reserve);
	}
	
	//根据ID得到对应的记录
	public Reserve queryReserveById(int id) throws Exception {
		Reserve po =  reserveMapper.queryReserveById(id);
		return po;
	}
}

