<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 通知公告 </view>
    </diy-navbar>
    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="grid" bindtap="navigateTo" data-url="newsView" data-id="{{item.id}}">
            <view class="diy-col-24" style="font-size: 16px">{{item.title}}</view>
            <view class="diy-col-24">{{item.nmemo}}
                <text class="cu-tag bg-cyan round ">查看详情</text>
            </view>
            <view class="diy-col-24">
                <text class="diy-icon-time"></text>{{item.naddtime}}
            </view>
        </view>
    </block>


    <view class="clearfix"></view>
</view>