<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 评价详情 </view>
    </diy-navbar>

 <block wx:for="{{msgs1}}" wx:key="k">
    <view class="flex flex-wrap igy-col-24 flex2-clz">    
        
        <view class="grid col-2  diy-col-24 ">         
            <view>评价ID：<text>{{item.eid}}</text></view>
<view>挂号id：<text>{{item.rid}}</text></view>
<view>医生id：<text>{{item.did}}</text></view>
<view>用户名：<text>{{item.lname}}</text></view>
<view>评分：<text>{{item.score}}</text></view>
<view>评语：<text>{{item.comment}}</text></view>
<view>评价时间：<text>{{item.etime}}</text></view>

        </view>
    </view>
</block>


    <view class="clearfix"></view>
</view>






