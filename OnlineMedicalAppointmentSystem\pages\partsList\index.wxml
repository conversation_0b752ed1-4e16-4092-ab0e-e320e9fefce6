<view class="container">
    <diy-navbar bgColor="green" isBack="{{false}}">
        <view slot="content"> 科室 </view>
    </diy-navbar>
<block wx:for="{{msgs1}}" wx:key="k">
  <view class="grid" bindtap="navigateTo" data-url="partsView" data-id="{{item.pid}}">
        <view class="diy-col-24" style="font-size: 16px">{{item.pname}}</view>
        <view class="diy-col-24">{{item.pmemo}}
            <text class="cu-tag bg-cyan round ">查看详情</text>
        </view>
    </view>
</block>


    <view class="clearfix"></view>
</view>





