'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var et = {
    name: 'et',
    el: {
        colorpicker: {
            confirm: 'OK',
            clear: '<PERSON><PERSON>hje<PERSON>',
        },
        datepicker: {
            now: '<PERSON><PERSON><PERSON>',
            today: 'Täna',
            cancel: '<PERSON><PERSON><PERSON><PERSON>',
            clear: 'Tühjenda',
            confirm: 'OK',
            selectDate: '<PERSON><PERSON> kuupäev',
            selectTime: '<PERSON><PERSON> kellaaeg',
            startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
            startTime: '<PERSON>gusae<PERSON>',
            endDate: '<PERSON><PERSON><PERSON><PERSON>up<PERSON>ev',
            endTime: '<PERSON><PERSON><PERSON>aeg',
            prevYear: 'E<PERSON>mine aasta',
            nextYear: '<PERSON><PERSON><PERSON>mine aasta',
            prevMonth: '<PERSON><PERSON>mine kuu',
            nextMonth: '<PERSON><PERSON><PERSON><PERSON> kuu',
            year: '',
            month1: 'Jaanuar',
            month2: 'Veebruar',
            month3: 'Märts',
            month4: 'Aprill',
            month5: 'Mai',
            month6: '<PERSON><PERSON>',
            month7: '<PERSON><PERSON>',
            month8: 'August',
            month9: 'September',
            month10: 'Oktoober',
            month11: 'November',
            month12: 'Detsember',
            weeks: {
                sun: 'P',
                mon: 'E',
                tue: 'T',
                wed: 'K',
                thu: 'N',
                fri: 'R',
                sat: 'L',
            },
            months: {
                jan: 'Jaan',
                feb: 'Veeb',
                mar: 'Mär',
                apr: 'Apr',
                may: 'Mai',
                jun: 'Juun',
                jul: 'Juul',
                aug: 'Aug',
                sep: 'Sept',
                oct: 'Okt',
                nov: 'Nov',
                dec: 'Dets',
            },
        },
        select: {
            loading: 'Laadimine',
            noMatch: 'Sobivad andmed puuduvad',
            noData: 'Andmed puuduvad',
            placeholder: 'Vali',
        },
        cascader: {
            noMatch: 'Sobivad andmed puuduvad',
            loading: 'Laadimine',
            placeholder: 'Vali',
            noData: 'Andmed puuduvad',
        },
        pagination: {
            goto: 'Mine lehele',
            pagesize: '/page',
            total: 'Kokku {total}',
            pageClassifier: '',
        },
        messagebox: {
            title: 'Teade',
            confirm: 'OK',
            cancel: 'Tühista',
            error: 'Vigane sisend',
        },
        upload: {
            deleteTip: 'Vajuta "Kustuta", et eemaldada',
            delete: 'Kustuta',
            preview: 'Eelvaate',
            continue: 'Jätka',
        },
        table: {
            emptyText: 'Andmed puuduvad',
            confirmFilter: 'Kinnita',
            resetFilter: 'Taasta',
            clearFilter: 'Kõik',
            sumText: 'Summa',
        },
        tree: {
            emptyText: 'Andmed puuduvad',
        },
        transfer: {
            noMatch: 'Sobivad andmed puuduvad',
            noData: 'Andmed puuduvad',
            titles: ['Loend 1', 'Loend 2'],
            filterPlaceholder: 'Sisesta märksõna',
            noCheckedFormat: '{total} objekti',
            hasCheckedFormat: '{checked}/{total} valitud',
        },
        image: {
            error: 'FAILED',
        },
        pageHeader: {
            title: 'Back',
        },
        popconfirm: {
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
        },
    },
};

exports.default = et;
