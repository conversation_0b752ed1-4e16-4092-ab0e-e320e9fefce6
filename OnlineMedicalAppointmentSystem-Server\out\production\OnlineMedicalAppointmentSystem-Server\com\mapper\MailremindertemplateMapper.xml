<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.MailremindertemplateMapper">
	<select id="findMailremindertemplateList"  resultType="Mailremindertemplate">
		select * from mailremindertemplate 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Mailremindertemplate">
	    select  *  
        from mailremindertemplate a  	
		<where>
      		<if test="mid != null and mid !=0 ">
		    and a.mid = #{mid}
		</if>
		<if test="days != null and days !=0 ">
		    and a.days = #{days}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} mid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from mailremindertemplate a  
		<where>
      		<if test="mid != null and mid !=0 ">
		    and a.mid = #{mid}
		</if>
		<if test="days != null and days !=0 ">
		    and a.days = #{days}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryMailremindertemplateById" parameterType="int" resultType="Mailremindertemplate">
    select  *  
     from mailremindertemplate a  	 where a.mid=#{value}
  </select>
 
	<insert id="insertMailremindertemplate" useGeneratedKeys="true" keyProperty="mid" parameterType="Mailremindertemplate">
    insert into mailremindertemplate
    (days,content)
    values
    (#{days},#{content});
  </insert>
	
	<update id="updateMailremindertemplate" parameterType="Mailremindertemplate" >
    update mailremindertemplate 
    <set>
		<if test="days != null ">
		    days = #{days},
		</if>
		<if test="content != null and content != ''">
		    content = #{content},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="mid != null or mid != ''">
      mid=#{mid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteMailremindertemplate" parameterType="int">
    delete from  mailremindertemplate where mid=#{value}
  </delete>

	
	
</mapper>

 
