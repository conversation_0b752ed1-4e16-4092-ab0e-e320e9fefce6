package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/doctor")
public class DoctorController{
	
	@Resource
	private DoctorService doctorService;
	
	//医生列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Doctor>> list(@RequestBody Doctor doctor, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = doctorService.getCount(doctor);
		//获取当前页记录
		List<Doctor> doctorList = doctorService.queryDoctorList(doctor, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(doctorList, counts, page_count);
	}
        
	//添加医生
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Doctor doctor, HttpServletRequest req) throws Exception {
		try {
			//判断用户名是否存在
            Doctor doctor1 = new Doctor();
            doctor1.setDaccount(doctor.getDaccount());
            List<Doctor> doctorList = doctorService.queryDoctorList(doctor1, null);
            if (doctorList.size() > 0) {
                return Response.error(201, "账号已存在，请重新输入");
            }
            else
            {
                doctorService.insertDoctor(doctor); //添加
            }
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除医生
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			doctorService.deleteDoctor(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改医生
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Doctor doctor, HttpServletRequest req) throws Exception {
		try {
			doctorService.updateDoctor(doctor); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回医生详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Doctor doctor=doctorService.queryDoctorById(id); //根据ID查询
			return Response.success(doctor);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
	//登录
    @ResponseBody
    @PostMapping(value = "/login")
    @CrossOrigin
    public Response login(@RequestBody Doctor doctor, HttpServletRequest request) throws Exception {

        try {
            List<Doctor> doctorList = doctorService.queryDoctorList(doctor, null);   //查询

            //判断是否有数据
            if (doctorList.size() > 0) {
                return Response.success(doctorList.get(0));  //登录成功
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
        return Response.error(201, "用户名或密码错误");
    }
	//修改密码
    @ResponseBody
    @PostMapping(value = "/updatePwd")
    @CrossOrigin
    public Response updatePwd(@RequestBody Doctor doctor, HttpServletRequest req) throws Exception {
        try {
            Doctor doctor1 = doctorService.queryDoctorById(doctor.getDid()); //根据ID查询
            //判断原密码是否正确
            if (!doctor1.getPassword().equals(doctor.getBy1())) {
                return Response.error(201, "原密码错误");
            } else {
                doctor1.setPassword(doctor.getBy2());  //新密码
                doctorService.updateDoctor(doctor1); //修改新密码
            }

        } catch (Exception e) {
            return Response.error();
        }
        return Response.success();
    }

}

