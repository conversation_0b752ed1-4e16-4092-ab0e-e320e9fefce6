const App = getApp();
Page({
  data: {
    msgs1: [],
    url: App.Config.basePath,
  },

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //获取数据列表
  getMsgs1() {
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/board_List").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.data, //把从服务器端得到的值赋值给数组
      });
    });
  },

  //删除
  dele(e) {
    var that = this;
    const dataset = e.currentTarget.dataset;
    let param = {
      f: 1,
      bid: dataset.id,
    };
    App.WxService.showModal({
      title: "友情提示",
      content: "您确定要执行此操作吗？",
    }).then((data) => {
      if (data.confirm == 1) {
        App.HttpService.delData(param, "/board_Delete").then((data) => {
          App.WxService.showToast({
            title: "删除成功!",
            icon: "success",
            duration: 1500,
          });
          setTimeout(function () {
            that.getMsgs1();
          }, 1500);
        });
      }
    });
  },

  formSubmit1(e) {
    var that = this;
    //判断cmemo是否为空
    if (e.detail.value.bdetail == "") {
      App.WxService.showToast({
        title: "留言内容不能为空!",
        icon: "none",
        duration: 1500,
      });
      return;
    }
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      bdetail: e.detail.value.bdetail,
    };
    App.HttpService.saveData(param, "/board_Add").then((data) => {
      App.WxService.showToast({
        title: "留言成功!",
        icon: "success",
        duration: 1500,
      });
      setTimeout(function () {
        that.setData({
          bdetail: "",
        });
        that.getMsgs1();
      }, 1500);
    });
  },
});
