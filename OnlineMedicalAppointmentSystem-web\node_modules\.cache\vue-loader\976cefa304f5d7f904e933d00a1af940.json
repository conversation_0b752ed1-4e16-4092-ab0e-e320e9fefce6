{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue", "mtime": 1749196032807}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICAgICAgCiAgICAgICAgaW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwogICAgICAgIGV4cG9ydCBkZWZhdWx0IHsKICAgICAgICAgICAgbmFtZTogJ1VzZXJzRGV0YWlsJywKICAgICAgICAgICAgY29tcG9uZW50czogewogICAgICAgICAgICB9LAogICAgICAgICAgICBkYXRhKCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICBpZDogJycsCiAgICAgICAgICAgICAgICAgICAgZm9ybURhdGE6IHt9LCAvL+ihqOWNleaVsOaNriAgICAgICAgIAogICAgICAgIAogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgY3JlYXRlZCgpIHsKICAgICAgICAgICAgICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbAKICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YXMoKTsKICAgICAgICAgICAgfSwKICAgICAgICAKICAgICAgICAKICAgICAgICAgICAgbWV0aG9kczogewogICAgICAgIAogICAgICAgICAgICAgICAgLy/ojrflj5bliJfooajmlbDmja4KICAgICAgICAgICAgICAgIGdldERhdGFzKCkgewogICAgICAgICAgICAgICAgICAgIGxldCBwYXJhID0gewogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3VzZXJzL2dldD9pZD0iICsgdGhpcy5pZDsKICAgICAgICAgICAgICAgICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSwKICAgICAgICAKICAgICAgICAgICAgICAgIC8vIOi/lOWbngogICAgICAgICAgICAgICAgYmFjaygpIHsKICAgICAgICAgICAgICAgICAgICAvL+i/lOWbnuS4iuS4gOmhtQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICAgICAgICAgICAgICB9LAogICAgICAgIAogICAgICAgICAgICB9LAogICAgICAgIH0KCg=="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\users\\UsersDetail.vue"], "names": [], "mappings": ";;QA+BQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,EAAE,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEvB,CAAC;YACL,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;;;YAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC;gBACN,CAAC;;gBAED,CAAC,EAAE,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;;YAEL,CAAC;QACL", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/users/UsersDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\n<el-form-item label=\"用户名\">\n{{formData.lname}}</el-form-item>\n<el-form-item label=\"登录密码\">\n{{formData.upassword}}</el-form-item>\n<el-form-item label=\"姓名\">\n{{formData.uname}}</el-form-item>\n<el-form-item label=\"性别\">\n{{formData.usex}}</el-form-item>\n<el-form-item label=\"手机号码\">\n{{formData.uphone}}</el-form-item>\n<el-form-item label=\"家庭地址\">\n{{formData.address}}</el-form-item>\n<el-form-item label=\"照片\" prop=\"pic\">\n<img :src=\"'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' +formData.pic\" style=\"width: 150px;height: 150px\" />\n</el-form-item>\n<el-form-item label=\"注册时间\">\n{{formData.utime}}</el-form-item>\n<el-form-item>\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\n</el-form-item>\n</el-form>\n\n\n    </div>\n</template>\n\n<script>\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'UsersDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/users/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\n\n</script>\n<style scoped>\n</style>\n \n\n"]}]}