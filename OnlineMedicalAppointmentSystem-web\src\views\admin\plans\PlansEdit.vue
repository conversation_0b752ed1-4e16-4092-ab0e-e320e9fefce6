﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="医生" prop="did">
<el-select v-model="formData.did" placeholder="请选择"  size="small">
<el-option v-for="item in doctorList" :key="item.did" :label="item.dname" :value="item.did"></el-option>
</el-select>
</el-form-item>
<el-form-item label="星期" prop="weeks">
<el-select v-model="formData.weeks" placeholder="请选择"  size="small">
<el-option label="星期一" value="星期一"></el-option>
<el-option label="星期二" value="星期二"></el-option>
<el-option label="星期三" value="星期三"></el-option>
<el-option label="星期四" value="星期四"></el-option>
<el-option label="星期五" value="星期五"></el-option>
<el-option label="星期六" value="星期六"></el-option>
<el-option label="星期日" value="星期日"></el-option>
</el-select>
</el-form-item>
<el-form-item label="时间段" prop="ptime">
<el-select v-model="formData.ptime" placeholder="请选择"  size="small">
<el-option label="8:00-9:00" value="8:00-9:00"></el-option>
<el-option label="9:00-10:00" value="9:00-10:00"></el-option>
<el-option label="10:00-11:00" value="10:00-11:00"></el-option>
<el-option label="11:00-12:00" value="11:00-12:00"></el-option>
<el-option label="14:00-15:00" value="14:00-15:00"></el-option>
<el-option label="15:00-16:00" value="15:00-16:00"></el-option>
<el-option label="16:00-17:00" value="16:00-17:00"></el-option>
</el-select>
</el-form-item>
<el-form-item label="号数" prop="people">
<el-input v-model="formData.people" placeholder="号数"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'PlansEdit',
  components: {
    
  },  
    data() {
      return {
        id: '',
        isClear: false,
        uploadVisible: false,
        btnLoading: false, //保存按钮加载状态
        formData: {}, //表单数据
        doctorList: [], //医生列表
        listLoading: false, //列表加载状态
        did: '', //医生ID
        addrules: {
          did: [{ required: true, message: '请选择医生', trigger: 'onchange' }],
          weeks: [{ required: true, message: '请选择星期', trigger: 'onchange' }],
          ptime: [{ required: true, message: '请选择时间段', trigger: 'onchange' }],
          people: [{ required: true, message: '请输入号数', trigger: 'blur' },
],        },

      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
      this.getdoctorList();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/plans/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
                    this.did = this.formData.did;
        this.formData.did = this.formData.dname;

          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/plans/update";
              this.btnLoading = true;
                        this.formData.did = this.formData.did==this.formData.dname?this.did:this.formData.did;

              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/PlansManage",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/PlansManage",
          });
        },       
              
            
    getdoctorList() {
      let para = {
        sort: "a." // 添加必需的sort字段
      };
      this.listLoading = true;
      let url = base + "/doctor/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.doctorList = res.resdata;
        this.listLoading = false;
      }).catch((error) => {
        console.error('获取医生列表失败:', error);
        this.listLoading = false;
        this.$message.error('获取医生列表失败');
      });
    },
  
           
           
      },
}

</script>
<style scoped>
</style>
 

