const App = getApp();
Page({
  data: {
    msgs1: [],
    tabsDatas: [
      { text: `医生简介`, icon: `` },
      { text: `坐诊安排`, icon: `` },
      { text: `患者评价`, icon: `` },
    ],
    tabsIndex: 0,
    url: App.Config.fileBasePath,
    doctorTime: [],
    date: [],
    week: [],
    time: [
      "8:00-9:00",
      "9:00-10:00",
      "10:00-11:00",
      "11:00-12:00",
      "14:00-15:00",
      "15:00-16:00",
      "16:00-17:00",
    ],
    plansList: [],
    isFavorite: false, // 是否已收藏
    evaluations: [], // 存储评价列表
  },

  // 获取未来7天的日期和星期
  getNext7Days() {
    let dates = [];
    let weeks = [];
    let tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1); // 从明天开始

    for (let i = 0; i < 7; i++) {
      let date = new Date(tomorrow);
      date.setDate(tomorrow.getDate() + i);

      // 格式化日期为 MM-DD
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      dates.push(`${month}-${day}`);

      // 获取星期
      let weekDay = this.getWeekDay(date.getDay());
      weeks.push(weekDay);

      // 打印每一天的信息
      console.log("Generated date info:", {
        date: `${month}-${day}`,
        weekDay: weekDay,
        dayNum: date.getDay(),
        charCodes: weekDay.split("").map((c) => c.charCodeAt(0)),
      });
    }

    this.setData({
      date: dates,
      week: weeks,
    });
  },

  // 获取星期几
  getWeekDay(day) {
    const weekMap = {
      0: "星期日",
      1: "星期一",
      2: "星期二",
      3: "星期三",
      4: "星期四",
      5: "星期五",
      6: "星期六",
    };
    return weekMap[day];
  },

  // 获取医生排班信息
  getPlans() {
    return new Promise((resolve) => {
      let param = {
        f: 5,
        did: this.data.globalOption.id,
        loadmsg: `正在加载中`,
      };

      App.HttpService.getData(param, "/plans_List").then((data) => {
        if (data.data && data.data.length > 0) {
          console.log("原始排班数据:", data.data);

          // 构建排班表格数据
          const plansList = data.data;
          const timeSlots = this.data.time; // 使用预定义的时间段

          // 获取未来7天的日期和星期
          this.getNext7Days();

          // 构建排班表格
          const scheduleTable = timeSlots.map((timeSlot) => {
            // 为每个时间段创建一行
            const cells = this.data.week.map((weekDay, index) => {
              // 查找该时间段和星期的排班
              const plan = plansList.find(
                (p) => p.ptime === timeSlot && p.weeks === weekDay
              );

              // 调试信息
              if (plan) {
                console.log(`找到排班: ${weekDay} ${timeSlot}`, {
                  plid: plan.plid,
                  people: plan.people,
                  yylatenum: plan.yylatenum,
                  availableSlots: plan.people - (plan.yylatenum || 0),
                });
              }

              // 返回单元格数据
              return {
                hasSchedule: !!plan,
                // 使用 people - yylatenum 计算实际可用号数
                availableSlots: plan ? plan.people - (plan.yylatenum || 0) : 0,
                plid: plan ? plan.plid : null,
              };
            });

            return {
              timeSlot: timeSlot,
              cells: cells,
            };
          });

          this.setData({
            plansList: plansList,
            scheduleTable: scheduleTable,
          });

          console.log("构建的排班表格:", scheduleTable);
        } else {
          // 如果没有排班数据，仍然获取日期和星期
          this.getNext7Days();
        }
        resolve();
      });
    });
  },

  // 获取医生评价
  getEvaluations() {
    let param = {
      did: this.data.globalOption.id,
      loadmsg: "正在加载评价",
    };

    App.HttpService.getData(param, "/evaluations_List").then((res) => {
      if (res.data) {
        this.setData({
          evaluations: res.data,
        });
      }
    });
  },

  // 判断某个时间段是否有排班
  hasSchedule(weekDay, timeSlot) {
    if (!this.data.plansList || !this.data.plansList.length) {
      return 0;
    }

    const plan = this.data.plansList.find(
      (p) => p.ptime === timeSlot && p.weeks === weekDay
    );
    return plan ? 1 : 0;
  },

  // 获取某个时间段的可预约人数
  getAvailableSlots(weekDay, timeSlot) {
    if (!this.data.plansList || !this.data.plansList.length) {
      return 0;
    }

    const plan = this.data.plansList.find(
      (p) => p.ptime === timeSlot && p.weeks === weekDay
    );
    return plan ? plan.people : 0;
  },

  // 获取某个时间段的排班ID
  getPlanId(weekDay, timeSlot) {
    if (!this.data.plansList || !this.data.plansList.length) {
      return 0;
    }

    const plan = this.data.plansList.find(
      (p) => p.ptime === timeSlot && p.weeks === weekDay
    );
    return plan ? plan.plid : 0;
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });

      // 先获取排班信息
      this.getPlans().then(() => {
        this.getNext7Days();
        // 最后获取医生信息
        this.getMsgs1();
        // 获取医生评价
        this.getEvaluations();

        // 打印最终状态
        console.log("Final state:", {
          dates: this.data.date,
          weeks: this.data.week,
          plans: this.data.plansList,
        });
      });

      // 检查是否已收藏
      this.checkFavorite();
    }
  },

  //页面显示
  onShow() {
    // 每次显示页面时重新获取排班信息，确保数据最新
    if (this.data.globalOption) {
      this.getPlans();
    }
  },

  async init() {},

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 2,
      did: this.data.globalOption.id,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/doctor_List").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.data,
      });
    });
  },

  changeTabs(evt) {
    let { index } = evt.currentTarget.dataset;
    if (index == this.data.tabsIndex) return;
    this.setData({
      tabsIndex: index,
    });

    // 如果切换到评价选项卡，确保评价数据已加载
    if (index === 2 && this.data.evaluations.length === 0) {
      this.getEvaluations();
    }
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  // 预约
  yuyue(e) {
    var index = e.currentTarget.dataset.id;
    var timeIndex = e.currentTarget.dataset.timeIndex;
    var rdate = this.data.date[index];
    var rtime = this.data.scheduleTable[timeIndex].timeSlot;
    var did = this.data.globalOption.id;
    var week = this.data.week[index];

    // 检查是否有可用预约
    let cell = this.data.scheduleTable[timeIndex].cells[index];
    if (!cell.hasSchedule || cell.availableSlots <= 0) {
      this.showModal("该时间段未排班或已约满");
      return;
    }

    const lname = wx.getStorageSync("lname");
    if (!lname) {
      this.showModal("请先登录");
      return;
    }

    wx.navigateTo({
      url: `/pages/reserveAdd/index?rdate=${rdate}&rtime=${rtime}&did=${did}&week=${week}&pid=${this.data.msgs1[0].pid}&plid=${cell.plid}`,
    });
  },

  // 检查是否已收藏
  checkFavorite() {
    const lname = wx.getStorageSync("lname");
    if (!lname) {
      return;
    }

    let param = {
      lname: lname,
      did: this.data.globalOption.id,
    };

    App.HttpService.getData(param, "/favorites_List").then((data) => {
      if (data.data && data.data.length > 0) {
        this.setData({
          isFavorite: true,
        });
      }
    });
  },

  // 收藏医生
  favoriteDoctor() {
    const lname = wx.getStorageSync("lname");
    if (!lname) {
      this.showModal("请先登录");
      return;
    }

    if (this.data.isFavorite) {
      this.showModal("您已收藏过该医生");
      return;
    }

    let param = {
      lname: lname,
      did: this.data.globalOption.id,
      dname: this.data.msgs1[0].dname,
      loadmsg: `正在收藏中`,
    };

    App.HttpService.saveData(param, "/favorites_Add").then((data) => {
      this.showModal("收藏成功");
      this.setData({
        isFavorite: true,
      });
    });
  },

  // 在线问诊
  chatWithDoctor() {
    const lname = wx.getStorageSync("lname");
    if (!lname) {
      this.showModal("请先登录");
      return;
    }

    wx.navigateTo({
      url: `/pages/chatView/index?id=${this.data.globalOption.id}&flag=1&name=${this.data.msgs1[0].dname}`,
    });
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return "";
    // 假设日期格式为 yyyy-MM-dd HH:mm:ss
    const date = new Date(dateStr.replace(/-/g, "/"));
    return `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  },
});
