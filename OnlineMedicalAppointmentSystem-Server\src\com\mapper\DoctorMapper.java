package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Doctor;

public interface DoctorMapper {

	//返回所有记录
	public List<Doctor> findDoctorList();
	
	//查询多条记录
	public List<Doctor> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertDoctor(Doctor doctor);

	//根据ID删除
	public int deleteDoctor(int id);
	
	//更新
	public int updateDoctor(Doctor doctor);
	
	//根据ID得到对应的记录
	public Doctor queryDoctorById(int id);
	
}

