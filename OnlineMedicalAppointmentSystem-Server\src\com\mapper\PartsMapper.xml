<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.PartsMapper">
	<select id="findPartsList"  resultType="Parts">
		select * from parts 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Parts">
	    select  *  
        from parts a  	
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} pid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from parts a  
		<where>
      		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryPartsById" parameterType="int" resultType="Parts">
    select  *  
     from parts a  	 where a.pid=#{value}
  </select>
 
	<insert id="insertParts" useGeneratedKeys="true" keyProperty="pid" parameterType="Parts">
    insert into parts
    (pname,pmemo)
    values
    (#{pname},#{pmemo});
  </insert>
	
	<update id="updateParts" parameterType="Parts" >
    update parts 
    <set>
		<if test="pname != null and pname != ''">
		    pname = #{pname},
		</if>
		<if test="pmemo != null and pmemo != ''">
		    pmemo = #{pmemo},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="pid != null or pid != ''">
      pid=#{pid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteParts" parameterType="int">
    delete from  parts where pid=#{value}
  </delete>

	
	
</mapper>

 
