<component name="libraryTable">
  <library name="lib">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-io-2.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-logging-1.0.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/fckeditor-java-core-2.4.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/mysql-connector-java-5.0.5-bin.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-jdbc-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/connector.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/liuliu.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/FCKeditor-2.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/c3p0-0.9.1.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-collections-3.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/cos.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/sqljdbc.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/antlr-2.7.6rc1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-io-2.0.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jotm.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/log4j-1.2.14.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/persistence.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-webmvc-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jta.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/mssqlserver.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/httpcore-4.4.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/aliyun-java-sdk-core-3.3.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-fileupload-1.3.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jtds-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/slf4j-log4j12-1.7.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jstl-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/hibernate3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-pool-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-aspects-3.2.0.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/asm.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/poi-3.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-web-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/mybatis-3.2.7.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/mysql-connector-java-5.0.4-bin.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jcommon-1.0.14.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-lang.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-expression-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/dom4j-1.6.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/json.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/javassist-3.17.1-GA.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-oxm-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-attributes-api.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-core-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/proxool-0.8.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-tx-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/mybatis-spring-1.2.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/xapool.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-codec-1.9.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/asm-attrs.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-attributes-compiler.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/msutil.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/xwork-2.0.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/cglib-2.1.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-collections-3.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jxl.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jdo2-api.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-context-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-aop-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/xml-apis.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/asm-3.3.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/pager-taglib-2.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-dbcp-1.2.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-fileupload-1.2.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jspsmartupload.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/slf4j-simple-1.5.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/cglib-2.2.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/json-lib-2.4-jdk15.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jdbc2_0-stdext.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-logging-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/aspectjweaver-1.6.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-test-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/msbase.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-io-1.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/ezmorph-1.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/json_simple-1.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/aliyun-java-sdk-dysmsapi-1.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-lang-2.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/gson-2.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/ueditor-1.1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-context-support-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/ognl-2.6.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/java-core-2.4.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/spring-beans-4.3.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/oscache-2.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/dwr.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-logging.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/httpclient-4.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/slf4j-api-1.7.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/aopalliance-1.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/freemarker.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/slf4j-api-1.5.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-beanutils-1.7.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/oro-2.0.8.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/commons-dbcp.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/ehcache-1.2.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/jakarta-oro-2.0.8.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/javaee.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/c3p0-0.9.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/WebRoot/WEB-INF/lib/freemarker-2.3.8.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>