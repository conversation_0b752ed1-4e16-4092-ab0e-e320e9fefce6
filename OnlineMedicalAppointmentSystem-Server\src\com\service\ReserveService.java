package com.service;
import java.util.List;

import com.model.Reserve;
import com.util.PageBean;

public interface ReserveService{
	
	//查询多条记录
	public List<Reserve> queryReserveList(Reserve reserve,PageBean page) throws Exception;
 
	//添加
	public int insertReserve(Reserve reserve) throws Exception ;
	
	//根据ID删除
	public int deleteReserve(int id) throws Exception ;
	
	//更新
	public int updateReserve(Reserve reserve) throws Exception ;
	
	//根据ID查询单条数据
	public Reserve queryReserveById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Reserve reserve);

}

