@charset "UTF-8";

@font-face {
    font-family: 'diygwui';
    src: url("//at.alicdn.com/t/font_2852989_zynoab19va.woff2?t=1638777669423") format("woff2"), url("//at.alicdn.com/t/font_2852989_zynoab19va.woff?t=1638777669423") format("woff"), url("//at.alicdn.com/t/font_2852989_zynoab19va.ttf?t=1638777669423") format("truetype"), url("//at.alicdn.com/t/font_2852989_zynoab19va.svg?t=1638777669423#diygwui") format("svg");
}

.diygwui {
    font-family: "diygwui" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.diy-icon-star:before {
    content: "\e7df";
}

.diy-icon-starfill:before {
    content: "\e86a";
}

.diy-icon-writefill:before {
    content: "\e600";
}

.diy-icon-jiaonang:before {
    content: "\e76d";
}

.diy-icon-shop:before {
    content: "\e736";
}

.diy-icon-shopfill:before {
    content: "\e737";
}

.diy-icon-pullright:before {
    content: "\e738";
}

.diy-icon-settings:before {
    content: "\e739";
}

.diy-icon-similar:before {
    content: "\e73a";
}

.diy-icon-sort:before {
    content: "\e73b";
}

.diy-icon-sound:before {
    content: "\e73c";
}

.diy-icon-skinfill:before {
    content: "\e73d";
}

.diy-icon-skin:before {
    content: "\e73e";
}

.diy-icon-stop:before {
    content: "\e73f";
}

.diy-icon-squarecheckfill:before {
    content: "\e740";
}

.diy-icon-square:before {
    content: "\e741";
}

.diy-icon-tag:before {
    content: "\e742";
}

.diy-icon-add:before {
    content: "\e643";
}

.diy-icon-subscription:before {
    content: "\e743";
}

.diy-icon-activityfill:before {
    content: "\e644";
}

.diy-icon-squarecheck:before {
    content: "\e744";
}

.diy-icon-addressbook:before {
    content: "\e645";
}

.diy-icon-taoxiaopu:before {
    content: "\e745";
}

.diy-icon-album:before {
    content: "\e646";
}

.diy-icon-taxi:before {
    content: "\e746";
}

.diy-icon-all:before {
    content: "\e647";
}

.diy-icon-text:before {
    content: "\e747";
}

.diy-icon-appreciate:before {
    content: "\e648";
}

.diy-icon-sponsorfill:before {
    content: "\e748";
}

.diy-icon-activity:before {
    content: "\e649";
}

.diy-icon-time:before {
    content: "\e749";
}

.diy-icon-attentionfavor:before {
    content: "\e64a";
}

.diy-icon-tagfill:before {
    content: "\e74a";
}

.diy-icon-attention:before {
    content: "\e64b";
}

.diy-icon-timefill:before {
    content: "\e74b";
}

.diy-icon-apps:before {
    content: "\e64c";
}

.diy-icon-title:before {
    content: "\e74c";
}

.diy-icon-attentionfill:before {
    content: "\e64d";
}

.diy-icon-ticket:before {
    content: "\e74d";
}

.diy-icon-attentionfavorfill:before {
    content: "\e64e";
}

.diy-icon-triangledownfill:before {
    content: "\e74e";
}

.diy-icon-attentionforbidfill:before {
    content: "\e64f";
}

.diy-icon-triangleupfill:before {
    content: "\e74f";
}

.diy-icon-baby:before {
    content: "\e650";
}

.diy-icon-top:before {
    content: "\e750";
}

.diy-icon-backdelete:before {
    content: "\e651";
}

.diy-icon-unlock:before {
    content: "\e751";
}

.diy-icon-back_android:before {
    content: "\e652";
}

.diy-icon-upblock:before {
    content: "\e752";
}

.diy-icon-babyfill:before {
    content: "\e653";
}

.diy-icon-upload:before {
    content: "\e753";
}

.diy-icon-back:before {
    content: "\e654";
}

.diy-icon-titles:before {
    content: "\e754";
}

.diy-icon-brand:before {
    content: "\e655";
}

.diy-icon-unfold:before {
    content: "\e755";
}

.diy-icon-barcode:before {
    content: "\e656";
}

.diy-icon-usefull:before {
    content: "\e756";
}

.diy-icon-brandfill:before {
    content: "\e657";
}

.diy-icon-upstagefill:before {
    content: "\e757";
}

.diy-icon-backwardfill:before {
    content: "\e658";
}

.diy-icon-upstage:before {
    content: "\e758";
}

.diy-icon-btn:before {
    content: "\e659";
}

.diy-icon-vip:before {
    content: "\e759";
}

.diy-icon-cai:before {
    content: "\e65a";
}

.diy-icon-video:before {
    content: "\e75a";
}

.diy-icon-calendar:before {
    content: "\e65b";
}

.diy-icon-videofill:before {
    content: "\e75b";
}

.diy-icon-camerafill1:before {
    content: "\e65c";
}

.diy-icon-vipcard:before {
    content: "\e75c";
}

.diy-icon-bad:before {
    content: "\e65d";
}

.diy-icon-voice:before {
    content: "\e75d";
}

.diy-icon-cameraaddfill:before {
    content: "\e65e";
}

.diy-icon-warn:before {
    content: "\e75e";
}

.diy-icon-camera:before {
    content: "\e65f";
}

.diy-icon-warnfill:before {
    content: "\e75f";
}

.diy-icon-appreciatefill:before {
    content: "\e660";
}

.diy-icon-voicefill:before {
    content: "\e760";
}

.diy-icon-camerarotate:before {
    content: "\e661";
}

.diy-icon-weibo:before {
    content: "\e761";
}

.diy-icon-card:before {
    content: "\e662";
}

.diy-icon-usefullfill:before {
    content: "\e762";
}

.diy-icon-camerafill:before {
    content: "\e663";
}

.diy-icon-wefill:before {
    content: "\e763";
}

.diy-icon-attentionforbid:before {
    content: "\e664";
}

.diy-icon-weixin:before {
    content: "\e764";
}

.diy-icon-cardboard:before {
    content: "\e665";
}

.diy-icon-we:before {
    content: "\e765";
}

.diy-icon-cardboardfill:before {
    content: "\e666";
}

.diy-icon-wenzi:before {
    content: "\e766";
}

.diy-icon-cardboardforbid:before {
    content: "\e667";
}

.diy-icon-weblock:before {
    content: "\e767";
}

.diy-icon-choicenessfill:before {
    content: "\e668";
}

.diy-icon-wifi:before {
    content: "\e768";
}

.diy-icon-choiceness:before {
    content: "\e669";
}

.diy-icon-weunblock:before {
    content: "\e769";
}

.diy-icon-cartfill:before {
    content: "\e66a";
}

.diy-icon-write:before {
    content: "\e76a";
}

.diy-icon-cart:before {
    content: "\e66b";
}

.diy-icon-check:before {
    content: "\e66c";
}

.diy-icon-sponsor:before {
    content: "\e76c";
}

.diy-icon-cascades:before {
    content: "\e66d";
}

.diy-icon-cameraadd:before {
    content: "\e66e";
}

.diy-icon-circle:before {
    content: "\e66f";
}

.diy-icon-circlefill:before {
    content: "\e670";
}

.diy-icon-clothes:before {
    content: "\e671";
}

.diy-icon-colorlens:before {
    content: "\e672";
}

.diy-icon-clothesfill:before {
    content: "\e673";
}

.diy-icon-close:before {
    content: "\e674";
}

.diy-icon-commandfill:before {
    content: "\e675";
}

.diy-icon-comment:before {
    content: "\e676";
}

.diy-icon-command:before {
    content: "\e677";
}

.diy-icon-coin:before {
    content: "\e678";
}

.diy-icon-commentfill:before {
    content: "\e679";
}

.diy-icon-countdownfill:before {
    content: "\e67a";
}

.diy-icon-communityfill:before {
    content: "\e67b";
}

.diy-icon-community:before {
    content: "\e67c";
}

.diy-icon-copy:before {
    content: "\e67d";
}

.diy-icon-countdown:before {
    content: "\e67e";
}

.diy-icon-deletefill:before {
    content: "\e67f";
}

.diy-icon-creativefill:before {
    content: "\e680";
}

.diy-icon-crown:before {
    content: "\e681";
}

.diy-icon-crownfill:before {
    content: "\e682";
}

.diy-icon-deliver:before {
    content: "\e683";
}

.diy-icon-delete:before {
    content: "\e684";
}

.diy-icon-deliver_fill:before {
    content: "\e685";
}

.diy-icon-creative:before {
    content: "\e686";
}

.diy-icon-cut:before {
    content: "\e687";
}

.diy-icon-dianhua:before {
    content: "\e688";
}

.diy-icon-discoverfill:before {
    content: "\e689";
}

.diy-icon-discover:before {
    content: "\e68a";
}

.diy-icon-edit:before {
    content: "\e68b";
}

.diy-icon-emoji:before {
    content: "\e68c";
}

.diy-icon-emojifill:before {
    content: "\e68d";
}

.diy-icon-ellipse:before {
    content: "\e68e";
}

.diy-icon-down:before {
    content: "\e68f";
}

.diy-icon-evaluate_fill:before {
    content: "\e690";
}

.diy-icon-emojiflashfill:before {
    content: "\e691";
}

.diy-icon-explore:before {
    content: "\e692";
}

.diy-icon-exit:before {
    content: "\e693";
}

.diy-icon-female:before {
    content: "\e694";
}

.diy-icon-favorfill:before {
    content: "\e695";
}

.diy-icon-evaluate:before {
    content: "\e696";
}

.diy-icon-explorefill:before {
    content: "\e697";
}

.diy-icon-expressman:before {
    content: "\e698";
}

.diy-icon-flashlightclose:before {
    content: "\e699";
}

.diy-icon-file:before {
    content: "\e69a";
}

.diy-icon-flashbuyfill:before {
    content: "\e69b";
}

.diy-icon-filter:before {
    content: "\e69c";
}

.diy-icon-favor:before {
    content: "\e69d";
}

.diy-icon-flashlightopen:before {
    content: "\e69e";
}

.diy-icon-fold:before {
    content: "\e69f";
}

.diy-icon-font:before {
    content: "\e6a0";
}

.diy-icon-focus:before {
    content: "\e6a1";
}

.diy-icon-fork:before {
    content: "\e6a2";
}

.diy-icon-footprint:before {
    content: "\e6a3";
}

.diy-icon-forwardfill:before {
    content: "\e6a4";
}

.diy-icon-forward:before {
    content: "\e6a5";
}

.diy-icon-formfill:before {
    content: "\e6a6";
}

.diy-icon-friendadd:before {
    content: "\e6a7";
}

.diy-icon-friendaddfill:before {
    content: "\e6a8";
}

.diy-icon-friendfamous:before {
    content: "\e6a9";
}

.diy-icon-friendfill:before {
    content: "\e6aa";
}

.diy-icon-friend:before {
    content: "\e6ab";
}

.diy-icon-full:before {
    content: "\e6ac";
}

.diy-icon-form:before {
    content: "\e6ad";
}

.diy-icon-github:before {
    content: "\e6ae";
}

.diy-icon-goods:before {
    content: "\e6af";
}

.diy-icon-global:before {
    content: "\e6b0";
}

.diy-icon-goodsnewfill:before {
    content: "\e6b1";
}

.diy-icon-goodsnew:before {
    content: "\e6b2";
}

.diy-icon-game:before {
    content: "\e6b3";
}

.diy-icon-goodsfill:before {
    content: "\e6b4";
}

.diy-icon-group:before {
    content: "\e6b5";
}

.diy-icon-group_fill:before {
    content: "\e6b6";
}

.diy-icon-homefill:before {
    content: "\e6b7";
}

.diy-icon-goodsfavor:before {
    content: "\e6b8";
}

.diy-icon-home:before {
    content: "\e6b9";
}

.diy-icon-hot:before {
    content: "\e6ba";
}

.diy-icon-friendfavor:before {
    content: "\e6bb";
}

.diy-icon-icon:before {
    content: "\e6bc";
}

.diy-icon-hotfill:before {
    content: "\e6bd";
}

.diy-icon-icloading:before {
    content: "\e6be";
}

.diy-icon-keyboard:before {
    content: "\e6bf";
}

.diy-icon-light:before {
    content: "\e6c0";
}

.diy-icon-info:before {
    content: "\e6c1";
}

.diy-icon-infofill:before {
    content: "\e6c2";
}

.diy-icon-lightfill:before {
    content: "\e6c3";
}

.diy-icon-lightforbid:before {
    content: "\e6c4";
}

.diy-icon-like:before {
    content: "\e6c5";
}

.diy-icon-lightauto:before {
    content: "\e6c6";
}

.diy-icon-list:before {
    content: "\e6c7";
}

.diy-icon-loading1:before {
    content: "\e6c8";
}

.diy-icon-likefill:before {
    content: "\e6c9";
}

.diy-icon-loading:before {
    content: "\e6ca";
}

.diy-icon-link:before {
    content: "\e6cb";
}

.diy-icon-location:before {
    content: "\e6cc";
}

.diy-icon-locationfill:before {
    content: "\e6cd";
}

.diy-icon-magic:before {
    content: "\e6ce";
}

.diy-icon-lock:before {
    content: "\e6cf";
}

.diy-icon-mail:before {
    content: "\e6d0";
}

.diy-icon-mark:before {
    content: "\e6d1";
}

.diy-icon-medalfill:before {
    content: "\e6d2";
}

.diy-icon-markfill:before {
    content: "\e6d3";
}

.diy-icon-medal:before {
    content: "\e6d4";
}

.diy-icon-mobile:before {
    content: "\e6d5";
}

.diy-icon-male:before {
    content: "\e6d6";
}

.diy-icon-mobilefill:before {
    content: "\e6d7";
}

.diy-icon-more:before {
    content: "\e6d8";
}

.diy-icon-message:before {
    content: "\e6d9";
}

.diy-icon-loading2:before {
    content: "\e6da";
}

.diy-icon-move:before {
    content: "\e6db";
}

.diy-icon-musicfill:before {
    content: "\e6dc";
}

.diy-icon-musicforbidfill:before {
    content: "\e6dd";
}

.diy-icon-messagefill:before {
    content: "\e6de";
}

.diy-icon-moneybagfill:before {
    content: "\e6df";
}

.diy-icon-moreandroid:before {
    content: "\e6e0";
}

.diy-icon-moneybag:before {
    content: "\e6e1";
}

.diy-icon-news:before {
    content: "\e6e2";
}

.diy-icon-newfill:before {
    content: "\e6e3";
}

.diy-icon-my:before {
    content: "\e6e4";
}

.diy-icon-new:before {
    content: "\e6e5";
}

.diy-icon-myfill:before {
    content: "\e6e6";
}

.diy-icon-newshotfill:before {
    content: "\e6e7";
}

.diy-icon-newshot:before {
    content: "\e6e8";
}

.diy-icon-notice_forbid_fill:before {
    content: "\e6e9";
}

.diy-icon-newsfill:before {
    content: "\e6ea";
}

.diy-icon-notificationfill:before {
    content: "\e6eb";
}

.diy-icon-notice:before {
    content: "\e6ec";
}

.diy-icon-notification:before {
    content: "\e6ed";
}

.diy-icon-noticefill:before {
    content: "\e6ee";
}

.diy-icon-order:before {
    content: "\e6ef";
}

.diy-icon-paintfill:before {
    content: "\e6f0";
}

.diy-icon-notificationforbidfill:before {
    content: "\e6f1";
}

.diy-icon-peoplefill:before {
    content: "\e6f2";
}

.diy-icon-paint:before {
    content: "\e6f3";
}

.diy-icon-peoplelist:before {
    content: "\e6f4";
}

.diy-icon-pay:before {
    content: "\e6f5";
}

.diy-icon-phone:before {
    content: "\e6f6";
}

.diy-icon-picfill:before {
    content: "\e6f7";
}

.diy-icon-pick:before {
    content: "\e6f8";
}

.diy-icon-pic:before {
    content: "\e6f9";
}

.diy-icon-post:before {
    content: "\e6fa";
}

.diy-icon-play_forward_fill:before {
    content: "\e6fb";
}

.diy-icon-present:before {
    content: "\e6fc";
}

.diy-icon-people:before {
    content: "\e6fd";
}

.diy-icon-playfill:before {
    content: "\e6fe";
}

.diy-icon-profile:before {
    content: "\e6ff";
}

.diy-icon-profilefill:before {
    content: "\e700";
}

.diy-icon-pulldown:before {
    content: "\e701";
}

.diy-icon-presentfill:before {
    content: "\e702";
}

.diy-icon-pullleft:before {
    content: "\e703";
}

.diy-icon-pullup:before {
    content: "\e704";
}

.diy-icon-punch:before {
    content: "\e705";
}

.diy-icon-questionfill:before {
    content: "\e706";
}

.diy-icon-radiobox:before {
    content: "\e707";
}

.diy-icon-question:before {
    content: "\e708";
}

.diy-icon-rank:before {
    content: "\e709";
}

.diy-icon-qrcode:before {
    content: "\e70a";
}

.diy-icon-rankfill:before {
    content: "\e70b";
}

.diy-icon-radioboxfill:before {
    content: "\e70c";
}

.diy-icon-qr_code:before {
    content: "\e70d";
}

.diy-icon-recordfill:before {
    content: "\e70e";
}

.diy-icon-record:before {
    content: "\e70f";
}

.diy-icon-read:before {
    content: "\e710";
}

.diy-icon-recharge:before {
    content: "\e711";
}

.diy-icon-refresh:before {
    content: "\e712";
}

.diy-icon-redpacket:before {
    content: "\e713";
}

.diy-icon-rechargefill:before {
    content: "\e714";
}

.diy-icon-redpacket_fill:before {
    content: "\e715";
}

.diy-icon-refresharrow:before {
    content: "\e716";
}

.diy-icon-refund:before {
    content: "\e717";
}

.diy-icon-round:before {
    content: "\e718";
}

.diy-icon-repeal:before {
    content: "\e719";
}

.diy-icon-right:before {
    content: "\e71a";
}

.diy-icon-repair:before {
    content: "\e71b";
}

.diy-icon-remind:before {
    content: "\e71c";
}

.diy-icon-repairfill:before {
    content: "\e71d";
}

.diy-icon-roundadd:before {
    content: "\e71e";
}

.diy-icon-roundaddfill:before {
    content: "\e71f";
}

.diy-icon-roundcheckfill:before {
    content: "\e720";
}

.diy-icon-rounddown:before {
    content: "\e721";
}

.diy-icon-roundclose:before {
    content: "\e722";
}

.diy-icon-roundleftfill-copy:before {
    content: "\e723";
}

.diy-icon-roundcheck:before {
    content: "\e724";
}

.diy-icon-roundright:before {
    content: "\e725";
}

.diy-icon-safe:before {
    content: "\e726";
}

.diy-icon-roundrightfill:before {
    content: "\e727";
}

.diy-icon-roundclosefill:before {
    content: "\e728";
}

.diy-icon-same:before {
    content: "\e729";
}

.diy-icon-samefill:before {
    content: "\e72a";
}

.diy-icon-searchlist:before {
    content: "\e72b";
}

.diy-icon-search:before {
    content: "\e72c";
}

.diy-icon-scan:before {
    content: "\e72d";
}

.diy-icon-send:before {
    content: "\e72e";
}

.diy-icon-selectionfill:before {
    content: "\e72f";
}

.diy-icon-selection:before {
    content: "\e730";
}

.diy-icon-settingsfill:before {
    content: "\e731";
}

.diy-icon-service:before {
    content: "\e732";
}

.diy-icon-shake:before {
    content: "\e733";
}

.diy-icon-servicefill:before {
    content: "\e734";
}

.diy-icon-share:before {
    content: "\e735";
}

page {
    --red: #e54d42;
    --orange: #f37b1d;
    --yellow: #fbbd08;
    --olive: #8dc63f;
    --green: #39b54a;
    --cyan: #1cbbb4;
    --blue: #0081ff;
    --purple: #6739b6;
    --mauve: #9c26b0;
    --pink: #e03997;
    --brown: #a5673f;
    --grey: #8799a3;
    --black: #333333;
    --darkGray: #666666;
    --gray: #aaaaaa;
    --ghostWhite: #f1f1f1;
    --white: #ffffff;
    /* 浅色 */
    --redLight: #fadbd9;
    --orangeLight: #fde6d2;
    --yellowLight: #fef2ce;
    --oliveLight: #e8f4d9;
    --greenLight: #d7f0db;
    --cyanLight: #d2f1f0;
    --blueLight: #cce6ff;
    --purpleLight: #e1d7f0;
    --mauveLight: #ebd4ef;
    --pinkLight: #f9d7ea;
    --brownLight: #ede1d9;
    --greyLight: #e7ebed;
    /* 渐变色 */
    --redGradual: linear-gradient(45deg, #f43f3b, #ec008c);
    --orangeGradual: linear-gradient(45deg, #ff9700, #ed1c24);
    --greenGradual: linear-gradient(45deg, #39b54a, #8dc63f);
    --purpleGradual: linear-gradient(45deg, #9000ff, #5e00ff);
    --pinkGradual: linear-gradient(45deg, #ec008c, #6739b6);
    --blueGradual: linear-gradient(45deg, #0081ff, #1cbbb4);
    /* 阴影透明色 */
    --ShadowSize: 0px 0px 8rpx;
    --redShadow: rgba(204, 69, 59, 0.2);
    --orangeShadow: rgba(217, 109, 26, 0.2);
    --yellowShadow: rgba(224, 170, 7, 0.2);
    --oliveShadow: rgba(124, 173, 55, 0.2);
    --greenShadow: rgba(48, 156, 63, 0.3);
    --cyanShadow: rgba(28, 187, 180, 0.2);
    --blueShadow: rgba(0, 102, 204, 0.2);
    --purpleShadow: rgba(88, 48, 156, 0.2);
    --mauveShadow: rgba(133, 33, 150, 0.2);
    --pinkShadow: rgba(199, 50, 134, 0.2);
    --brownShadow: rgba(140, 88, 53, 0.2);
    --greyShadow: rgba(114, 130, 138, 0.2);
    --grayShadow: rgba(114, 130, 138, 0.2);
    --blackShadow: rgba(26, 26, 26, 0.16);
    --primary-font-size: 24rpx;
    --form-label-width: 5em;
}

::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 6px;
}

::-webkit-scrollbar-corner {
    display: none;
}

[class*=diy-] {
    font-size: var(--primary-font-size);
}

view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
    box-sizing: border-box;
}

.htmlcontent {
    white-space: pre-line;
    width: 100%;
}

.clearfix {
    box-sizing: border-box;
    clear: both;
}

.clearfix::before, .clearfix::after {
    clear: both;
    display: table;
}

.hidden {
    display: none;
}

.pointer {
    cursor: pointer;
}

.container {
    background-size: cover;
    background-position: center;
    min-height: 100vh;
}

.response {
    width: 100%;
}

.response > ._div {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
}

.diy-absolute {
    position: absolute !important;
    z-index: 1000;
}

.diy-autoview {
    position: relative;
}

.uni-tabbar-bottom, .uni-page-head {
    z-index: 999999;
}

.diy-top {
    position: fixed !important;
    z-index: 1000;
    left: 0px;
    top: 0px;
    width: 100%;
}

.not-border {
    border: 0 !important;
}

.not-border::after, .not-border::before {
    border: 0 !important;
}

.not-border.diy-tag {
    padding-left: 0;
}

.font-normal {
    font-weight: normal;
}

.font-bold {
    font-weight: bold;
}

.border {
    border: 0;
}

.width-auto {
    width: auto !important;
}

[class*=diy-icon-] {
    font-family: "diygwui" !important;
    font-size: 32rpx;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.solid,
.dashed {
    position: relative;
}

.solid::after,
.dashed::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
}

.solid::after,
.dashed::after {
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.solid-top,
.dashed-top {
    position: relative;
}

.solid-top::after,
.dashed-top::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
}

.solid-top::after,
.dashed-top::after {
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.solid-right,
.dashed-right {
    position: relative;
}

.solid-right::after,
.dashed-right::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
}

.solid-right::after,
.dashed-right::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.solid-bottom,
.dashed-bottom {
    position: relative;
}

.solid-bottom::after,
.dashed-bottom::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
}

.solid-bottom::after,
.dashed-bottom::after {
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.solid-left,
.dashed-left {
    position: relative;
}

.solid-left::after,
.dashed-left::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
}

.solid-left::after,
.dashed-left::after {
    border-left: 1rpx solid rgba(0, 0, 0, 0.1);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}

.margin {
    margin: 20rpx !important;
}

.margin-top {
    margin-top: 20rpx !important;
}

.margin-right {
    margin-right: 20rpx !important;
}

.margin-bottom {
    margin-bottom: 20rpx !important;
}

.margin-left {
    margin-left: 20rpx !important;
}

.padding {
    padding: 20rpx;
}

.padding-top {
    padding: 20rpx;
}

.padding-right {
    padding: 20rpx;
}

.padding-bottom {
    padding: 20rpx;
}

.padding-left {
    padding: 20rpx;
}

.margin-xs {
    margin: 10rpx !important;
}

.margin-top-xs {
    margin-top: 10rpx !important;
}

.margin-right-xs {
    margin-right: 10rpx !important;
}

.margin-bottom-xs {
    margin-bottom: 10rpx !important;
}

.margin-left-xs {
    margin-left: 10rpx !important;
}

.padding-xs {
    padding: 10rpx;
}

.padding-top-xs {
    padding-top: 10rpx;
}

.padding-right-xs {
    padding-right: 10rpx;
}

.padding-bottom-xs {
    padding-bottom: 10rpx;
}

.padding-left-xs {
    padding-left: 10rpx;
}

.margin-sm {
    margin: 20rpx !important;
}

.margin-top-sm {
    margin-top: 20rpx !important;
}

.margin-right-sm {
    margin-right: 20rpx !important;
}

.margin-bottom-sm {
    margin-bottom: 20rpx !important;
}

.margin-left-sm {
    margin-left: 20rpx !important;
}

.padding-sm {
    padding: 20rpx;
}

.padding-top-sm {
    padding-top: 20rpx;
}

.padding-right-sm {
    padding-right: 20rpx;
}

.padding-bottom-sm {
    padding-bottom: 20rpx;
}

.padding-left-sm {
    padding-left: 20rpx;
}

.margin-md {
    margin: 24rpx !important;
}

.margin-top-md {
    margin-top: 24rpx !important;
}

.margin-right-md {
    margin-right: 24rpx !important;
}

.margin-bottom-md {
    margin-bottom: 24rpx !important;
}

.margin-left-md {
    margin-left: 24rpx !important;
}

.padding-md {
    padding: 24rpx;
}

.padding-top-md {
    padding-top: 24rpx;
}

.padding-right-md {
    padding-right: 24rpx;
}

.padding-bottom-md {
    padding-bottom: 24rpx;
}

.padding-left-md {
    padding-left: 24rpx;
}

.margin-lg {
    margin: 40rpx !important;
}

.margin-top-lg {
    margin-top: 40rpx !important;
}

.margin-right-lg {
    margin-right: 40rpx !important;
}

.margin-bottom-lg {
    margin-bottom: 40rpx !important;
}

.margin-left-lg {
    margin-left: 40rpx !important;
}

.padding-lg {
    padding: 40rpx;
}

.padding-top-lg {
    padding-top: 40rpx;
}

.padding-right-lg {
    padding-right: 40rpx;
}

.padding-bottom-lg {
    padding-bottom: 40rpx;
}

.padding-left-lg {
    padding-left: 40rpx;
}

.margin-xl {
    margin: 60rpx !important;
}

.margin-top-xl {
    margin-top: 60rpx !important;
}

.margin-right-xl {
    margin-right: 60rpx !important;
}

.margin-bottom-xl {
    margin-bottom: 60rpx !important;
}

.margin-left-xl {
    margin-left: 60rpx !important;
}

.padding-xl {
    padding: 60rpx;
}

.padding-top-xl {
    padding-top: 60rpx;
}

.padding-right-xl {
    padding-right: 60rpx;
}

.padding-bottom-xl {
    padding-bottom: 60rpx;
}

.padding-left-xl {
    padding-left: 60rpx;
}

[class*=diy-col-] {
    float: left;
    box-sizing: border-box;
}

.diy-col-0 {
    width: auto;
}

.diy-col-1 {
    width: 4.1666666667% !important;
}

.diy-col-offset-1 {
    margin-left: 4.1666666667%;
}

.diy-col-pull-1 {
    position: relative;
    right: 4.1666666667%;
}

.diy-col-push-1 {
    position: relative;
    left: 4.1666666667%;
}

.diy-col-2 {
    width: 8.3333333333% !important;
}

.diy-col-offset-2 {
    margin-left: 8.3333333333%;
}

.diy-col-pull-2 {
    position: relative;
    right: 8.3333333333%;
}

.diy-col-push-2 {
    position: relative;
    left: 8.3333333333%;
}

.diy-col-3 {
    width: 12.5% !important;
}

.diy-col-offset-3 {
    margin-left: 12.5%;
}

.diy-col-pull-3 {
    position: relative;
    right: 12.5%;
}

.diy-col-push-3 {
    position: relative;
    left: 12.5%;
}

.diy-col-4 {
    width: 16.6666666667% !important;
}

.diy-col-offset-4 {
    margin-left: 16.6666666667%;
}

.diy-col-pull-4 {
    position: relative;
    right: 16.6666666667%;
}

.diy-col-push-4 {
    position: relative;
    left: 16.6666666667%;
}

.diy-col-5 {
    width: 20.8333333333% !important;
}

.diy-col-offset-5 {
    margin-left: 20.8333333333%;
}

.diy-col-pull-5 {
    position: relative;
    right: 20.8333333333%;
}

.diy-col-push-5 {
    position: relative;
    left: 20.8333333333%;
}

.diy-col-6 {
    width: 25% !important;
}

.diy-col-offset-6 {
    margin-left: 25%;
}

.diy-col-pull-6 {
    position: relative;
    right: 25%;
}

.diy-col-push-6 {
    position: relative;
    left: 25%;
}

.diy-col-7 {
    width: 29.1666666667% !important;
}

.diy-col-offset-7 {
    margin-left: 29.1666666667%;
}

.diy-col-pull-7 {
    position: relative;
    right: 29.1666666667%;
}

.diy-col-push-7 {
    position: relative;
    left: 29.1666666667%;
}

.diy-col-8 {
    width: 33.3333333333% !important;
}

.diy-col-offset-8 {
    margin-left: 33.3333333333%;
}

.diy-col-pull-8 {
    position: relative;
    right: 33.3333333333%;
}

.diy-col-push-8 {
    position: relative;
    left: 33.3333333333%;
}

.diy-col-9 {
    width: 37.5% !important;
}

.diy-col-offset-9 {
    margin-left: 37.5%;
}

.diy-col-pull-9 {
    position: relative;
    right: 37.5%;
}

.diy-col-push-9 {
    position: relative;
    left: 37.5%;
}

.diy-col-10 {
    width: 41.6666666667% !important;
}

.diy-col-offset-10 {
    margin-left: 41.6666666667%;
}

.diy-col-pull-10 {
    position: relative;
    right: 41.6666666667%;
}

.diy-col-push-10 {
    position: relative;
    left: 41.6666666667%;
}

.diy-col-11 {
    width: 45.8333333333% !important;
}

.diy-col-offset-11 {
    margin-left: 45.8333333333%;
}

.diy-col-pull-11 {
    position: relative;
    right: 45.8333333333%;
}

.diy-col-push-11 {
    position: relative;
    left: 45.8333333333%;
}

.diy-col-12 {
    width: 50% !important;
}

.diy-col-offset-12 {
    margin-left: 50%;
}

.diy-col-pull-12 {
    position: relative;
    right: 50%;
}

.diy-col-push-12 {
    position: relative;
    left: 50%;
}

.diy-col-13 {
    width: 54.1666666667% !important;
}

.diy-col-offset-13 {
    margin-left: 54.1666666667%;
}

.diy-col-pull-13 {
    position: relative;
    right: 54.1666666667%;
}

.diy-col-push-13 {
    position: relative;
    left: 54.1666666667%;
}

.diy-col-14 {
    width: 58.3333333333% !important;
}

.diy-col-offset-14 {
    margin-left: 58.3333333333%;
}

.diy-col-pull-14 {
    position: relative;
    right: 58.3333333333%;
}

.diy-col-push-14 {
    position: relative;
    left: 58.3333333333%;
}

.diy-col-15 {
    width: 62.5% !important;
}

.diy-col-offset-15 {
    margin-left: 62.5%;
}

.diy-col-pull-15 {
    position: relative;
    right: 62.5%;
}

.diy-col-push-15 {
    position: relative;
    left: 62.5%;
}

.diy-col-16 {
    width: 66.6666666667% !important;
}

.diy-col-offset-16 {
    margin-left: 66.6666666667%;
}

.diy-col-pull-16 {
    position: relative;
    right: 66.6666666667%;
}

.diy-col-push-16 {
    position: relative;
    left: 66.6666666667%;
}

.diy-col-17 {
    width: 70.8333333333% !important;
}

.diy-col-offset-17 {
    margin-left: 70.8333333333%;
}

.diy-col-pull-17 {
    position: relative;
    right: 70.8333333333%;
}

.diy-col-push-17 {
    position: relative;
    left: 70.8333333333%;
}

.diy-col-18 {
    width: 75% !important;
}

.diy-col-offset-18 {
    margin-left: 75%;
}

.diy-col-pull-18 {
    position: relative;
    right: 75%;
}

.diy-col-push-18 {
    position: relative;
    left: 75%;
}

.diy-col-19 {
    width: 79.1666666667% !important;
}

.diy-col-offset-19 {
    margin-left: 79.1666666667%;
}

.diy-col-pull-19 {
    position: relative;
    right: 79.1666666667%;
}

.diy-col-push-19 {
    position: relative;
    left: 79.1666666667%;
}

.diy-col-20 {
    width: 83.3333333333% !important;
}

.diy-col-offset-20 {
    margin-left: 83.3333333333%;
}

.diy-col-pull-20 {
    position: relative;
    right: 83.3333333333%;
}

.diy-col-push-20 {
    position: relative;
    left: 83.3333333333%;
}

.diy-col-21 {
    width: 87.5% !important;
}

.diy-col-offset-21 {
    margin-left: 87.5%;
}

.diy-col-pull-21 {
    position: relative;
    right: 87.5%;
}

.diy-col-push-21 {
    position: relative;
    left: 87.5%;
}

.diy-col-22 {
    width: 91.6666666667% !important;
}

.diy-col-offset-22 {
    margin-left: 91.6666666667%;
}

.diy-col-pull-22 {
    position: relative;
    right: 91.6666666667%;
}

.diy-col-push-22 {
    position: relative;
    left: 91.6666666667%;
}

.diy-col-23 {
    width: 95.8333333333% !important;
}

.diy-col-offset-23 {
    margin-left: 95.8333333333%;
}

.diy-col-pull-23 {
    position: relative;
    right: 95.8333333333%;
}

.diy-col-push-23 {
    position: relative;
    left: 95.8333333333%;
}

.diy-col-24 {
    width: 100% !important;
}

.diy-col-offset-24 {
    margin-left: 100%;
}

.diy-col-pull-24 {
    position: relative;
    right: 100%;
}

.diy-col-push-24 {
    position: relative;
    left: 100%;
}

/* -- 阴影 -- */
.diy-shadow {
    box-shadow: 0 1rpx 6px var(--blackShadow);
}

.diy-shadow[class*='white'] {
    --ShadowSize: 0 1rpx 6px;
}

.diy-shadow-lg {
    --ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}

.diy-shadow-warp {
    position: relative;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.diy-shadow-warp:before,
.diy-shadow-warp:after {
    position: absolute;
    content: "";
    top: 20rpx;
    bottom: 30rpx;
    left: 20rpx;
    width: 50%;
    box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
    -webkit-transform: rotate(-3deg);
    transform: rotate(-3deg);
    z-index: -1;
}

.diy-shadow-warp:after {
    right: 20rpx;
    left: auto;
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
}

.diy-shadow-blur {
    position: relative;
}

.diy-shadow-blur::before {
    content: "";
    display: block;
    background: inherit;
    -webkit-filter: blur(10rpx);
    filter: blur(10rpx);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 10rpx;
    left: 10rpx;
    z-index: -1;
    opacity: 0.3;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    border-radius: inherit;
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
}

/* ==================
          操作条
 ==================== */
.diy-bottom {
    position: fixed !important;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    z-index: 99;
    border-top: 1rpx solid #eee;
}

.diy-bar {
    display: flex;
    position: relative;
    align-items: center;
    min-height: 100rpx;
    justify-content: space-between;
}

.diy-bar .action {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    max-width: 100%;
}

.diy-bar .action.border-title {
    position: relative;
    top: -10rpx;
}

.diy-bar .action.border-title text[class*="bg-"]:last-child {
    position: absolute;
    bottom: -0.5rem;
    min-width: 2rem;
    height: 6rpx;
    left: 0;
}

.diy-bar .action.sub-title {
    position: relative;
    top: -0.2rem;
}

.diy-bar .action.sub-title text {
    position: relative;
    z-index: 1;
}

.diy-bar .action.sub-title text[class*="bg-"]:last-child {
    position: absolute;
    display: inline-block;
    bottom: -0.2rem;
    border-radius: 6rpx;
    width: 100%;
    height: 0.6rem;
    left: 0.6rem;
    opacity: 0.3;
    z-index: 0;
}

.diy-bar .action.sub-title text[class*="text-"]:last-child {
    position: absolute;
    display: inline-block;
    bottom: -0.7rem;
    left: 0.5rem;
    opacity: 0.2;
    z-index: 0;
    text-align: right;
    font-weight: 900;
    font-size: 36rpx;
}

.diy-bar.justify-center .action.border-title text:last-child,
.diy-bar.justify-center .action.sub-title text:last-child {
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
}

.diy-bar .action:first-child {
    margin-left: 30rpx;
    font-size: 30rpx;
}

.diy-bar .action text.text-cut {
    text-align: left;
    width: 100%;
}

.diy-bar .diy-avatar:first-child {
    margin-left: 20rpx;
}

.diy-bar .action:first-child > text[class*="diygwIcon-"] {
    margin-left: -0.3em;
    margin-right: 0.3em;
}

.diy-bar .action:last-child {
    margin-right: 30rpx;
}

.diy-bar .action > text[class*="diygwIcon-"],
.diy-bar .action > view[class*="diygwIcon-"] {
    font-size: 36rpx;
}

.diy-bar .action > text[class*="diygwIcon-"] + text[class*="diygwIcon-"] {
    margin-left: 0.5em;
}

.diy-bar .content {
    position: absolute;
    text-align: center;
    width: calc(100% - 340rpx);
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    height: 60rpx;
    font-size: 32rpx;
    line-height: 60rpx;
    cursor: none;
    pointer-events: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.diy-bar.ios .content {
    bottom: 14rpx;
    height: 60rpx;
    font-size: 32rpx;
    line-height: 60rpx;
}

.diy-bar.btn-group {
    justify-content: space-around;
}

.diy-bar.btn-group button {
    padding: 20rpx 32rpx;
}

.diy-bar.btn-group button {
    flex: 1;
    margin: 0 20rpx;
    max-width: 50%;
}

.diy-bar .search-form {
    background-color: #f5f5f5;
    line-height: 64rpx;
    height: 64rpx;
    font-size: 24rpx;
    color: var(--black);
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 30rpx;
}

.diy-bar .search-form + .action {
    margin-right: 30rpx;
}

.diy-bar .search-form input {
    flex: 1;
    padding-right: 30rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 16rpx;
    background-color: transparent;
}

.diy-bar .search-form [class*="diygwIcon-"] {
    margin: 0 0.5em 0 0.8em;
}

.diy-bar .search-form [class*="diygwIcon-"]::before {
    top: 0px;
}

.diy-bar.fixed, .nav.fixed {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1024;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diy-bar.foot {
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 1024;
    box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diy-bar.tabbar {
    padding: 0;
    height: calc(100rpx + env(safe-area-inset-bottom) / 2);
    padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.diy-tabbar-height {
    min-height: 100rpx;
    height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.diy-bar.tabbar.shadow {
    box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diy-bar.tabbar .action {
    font-size: 24rpx;
    position: relative;
    flex: 1;
    text-align: center;
    padding: 0;
    display: block;
    height: auto;
    line-height: 1;
    margin: 0;
    overflow: initial;
}

.diy-bar.tabbar.shop .action {
    width: 280px;
    flex: initial;
}

.diy-bar.tabbar .action.add-action {
    position: relative;
    z-index: 2;
    padding-top: 50rpx;
    background-color: inherit;
}

.diy-bar.tabbar .action.add-action [class*="diygwIcon-"] {
    position: absolute;
    width: 70rpx;
    z-index: 2;
    height: 70rpx;
    border-radius: 50%;
    line-height: 70rpx;
    font-size: 50rpx;
    top: -35rpx;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0;
}

.diy-bar.tabbar .action.add-action::after {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 100rpx;
    top: -50rpx;
    left: 0;
    right: 0;
    margin: auto;
    box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
    border-radius: 50rpx;
    background-color: inherit;
    z-index: 0;
}

.diy-bar.tabbar .action.add-action::before {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 30rpx;
    bottom: 30rpx;
    left: 0;
    right: 0;
    margin: auto;
    background-color: inherit;
    z-index: 1;
}

.diy-bar.tabbar .btn-group {
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 10rpx;
}

.diy-bar.tabbar button.action::after {
    border: 0;
}

.diy-bar.tabbar .action [class*="diygwIcon-"] {
    width: 100rpx;
    position: relative;
    display: block;
    height: auto;
    margin: 0 auto 10rpx;
    text-align: center;
    font-size: 40px;
}

.diy-bar.tabbar .action .diygwIcon-diy-image {
    margin: 0 auto;
}

.diy-bar.tabbar .action .diygwIcon-diy-image image {
    width: 50rpx;
    height: 50rpx;
    display: inline-block;
}

.diy-bar.tabbar .submit {
    align-items: center;
    display: flex;
    justify-content: center;
    text-align: center;
    position: relative;
    flex: 2;
    align-self: stretch;
}

.diy-bar.tabbar .submit:last-child {
    flex: 2.6;
}

.diy-bar.tabbar .submit + .submit {
    flex: 2;
}

.diy-bar.tabbar.border .action::before {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
    z-index: 3;
}

.diy-bar.tabbar.border .action:last-child:before {
    display: none;
}

.diy-bar.input {
    padding-right: 20rpx;
    background-color: var(--white);
}

.diy-bar.input input {
    overflow: initial;
    line-height: 64rpx;
    height: 64rpx;
    min-height: 64rpx;
    flex: 1;
    font-size: 30rpx;
    margin: 0 20rpx;
}

.diy-bar.input .action {
    margin-left: 20rpx;
}

.diy-bar.input .action [class*="diygwIcon-"] {
    font-size: 48rpx;
}

.diy-bar.input input + .action {
    margin-right: 20rpx;
    margin-left: 0px;
}

.diy-bar.input .action:first-child [class*="diygwIcon-"] {
    margin-left: 0px;
}

/* ==================
          按钮
 ==================== */
.diy-btn {
    position: relative;
    border: 0px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    overflow: visible;
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
}

.diy-btn::after {
    display: none;
}

.diy-btn:not([class*="bg-"]) {
    background-color: #f0f0f0;
}

.diy-btn[class*="line"] {
    background-color: transparent;
}

.diy-btn[class*="line"]::after {
    content: " ";
    display: block;
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1rpx solid currentColor;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 6px;
    z-index: 1;
    pointer-events: none;
}

.diy-btn[class*="lines"]::after {
    border: 6rpx solid currentColor;
}

.diy-btn[class*="bg-"]::after {
    display: none;
}

.diy-btn .icon {
    border-radius: 500px;
    padding: 0;
    margin-right: 6rpx;
}

.diy-btn.button-hover {
    -webkit-transform: translate(1rpx, 1rpx);
    transform: translate(1rpx, 1rpx);
}

.block {
    display: block;
}

.diy-btn.block {
    display: flex;
    width: 100%;
}

.diy-btn[disabled] {
    opacity: 0.6;
}

.diy-btn {
    padding: 0 20rpx;
    font-size: 14px;
    height: 64rpx;
}

.diy-btn.icon {
    width: 64rpx;
    height: 64rpx;
}

.diy-btn.xs {
    padding: 0 10rpx;
    font-size: 20rpx;
    height: 48rpx;
}

.diy-btn.icon.xs {
    width: 20rpx;
    height: 20rpx;
}

.diy-btn.sm {
    padding: 0 20rpx;
    font-size: 24rpx;
    height: 216rpx;
}

.diy-btn.icon.sm {
    width: 24rpx;
    height: 24rpx;
}

.diy-btn.md {
    padding: 0 24rpx;
    font-size: 14px;
    height: 64rpx;
}

.diy-btn.icon.md {
    width: 14px;
    height: 14px;
}

.diy-btn.lg {
    padding: 0 40rpx;
    font-size: 48rpx;
    height: 40px;
}

.diy-btn.icon.lg {
    width: 48rpx;
    height: 48rpx;
}

.diy-btn.xl {
    padding: 0 60rpx;
    font-size: 64rpx;
    height: 60px;
}

.diy-btn.icon.xl {
    width: 64rpx;
    height: 64rpx;
}

.radius, .radius[class*="line"]::after {
    border-radius: 2000px;
}

.radius-xs {
    border-radius: 6rpx;
}

.radius-xs[class*="line"]::after {
    border-radius: 6px;
}

.radius-sm {
    border-radius: 16rpx;
}

.radius-sm[class*="line"]::after {
    border-radius: 32rpx;
}

.radius-md {
    border-radius: 24rpx;
}

.radius-md[class*="line"]::after {
    border-radius: 48rpx;
}

.radius-lg {
    border-radius: 32rpx;
}

.radius-lg[class*="line"]::after {
    border-radius: 64rpx;
}

.radius-xl {
    border-radius: 200px;
}

.radius-xl[class*="line"]::after {
    border-radius: 400px;
}

.radius-left, .radius-left[class*="line"]::after {
    border-top-left-radius: 2000px !important;
    border-bottom-left-radius: 2000px !important;
}

.radius-right, .radius-right[class*="line"]::after {
    border-top-right-radius: 2000px !important;
    border-bottom-right-radius: 2000px !important;
}

.radius-top, .radius-top[class*="line"]::after {
    border-top-left-radius: 2000px !important;
    border-top-right-radius: 2000px !important;
}

.radius-bottom, .radius-bottom[class*="line"]::after {
    border-bottom-left-radius: 2000px !important;
    border-bottom-right-radius: 2000px !important;
}

/* ==================
          文本
 ==================== */
.text-red,
.line-red,
.lines-red {
    color: var(--red);
}

.line-red::after,
.lines-red::after {
    border-color: var(--red);
}

.text-shadow[class*="-red"] {
    text-shadow: var(--ShadowSize) var(--redShadow);
}

.text-orange,
.line-orange,
.lines-orange {
    color: var(--orange);
}

.line-orange::after,
.lines-orange::after {
    border-color: var(--orange);
}

.text-shadow[class*="-orange"] {
    text-shadow: var(--ShadowSize) var(--orangeShadow);
}

.text-yellow,
.line-yellow,
.lines-yellow {
    color: var(--yellow);
}

.line-yellow::after,
.lines-yellow::after {
    border-color: var(--yellow);
}

.text-shadow[class*="-yellow"] {
    text-shadow: var(--ShadowSize) var(--yellowShadow);
}

.text-olive,
.line-olive,
.lines-olive {
    color: var(--olive);
}

.line-olive::after,
.lines-olive::after {
    border-color: var(--olive);
}

.text-shadow[class*="-olive"] {
    text-shadow: var(--ShadowSize) var(--oliveShadow);
}

.text-green,
.line-green,
.lines-green {
    color: var(--green);
}

.line-green::after,
.lines-green::after {
    border-color: var(--green);
}

.text-shadow[class*="-green"] {
    text-shadow: var(--ShadowSize) var(--greenShadow);
}

.text-cyan,
.line-cyan,
.lines-cyan {
    color: var(--cyan);
}

.line-cyan::after,
.lines-cyan::after {
    border-color: var(--cyan);
}

.text-shadow[class*="-cyan"] {
    text-shadow: var(--ShadowSize) var(--cyanShadow);
}

.text-blue,
.line-blue,
.lines-blue {
    color: var(--blue);
}

.line-blue::after,
.lines-blue::after {
    border-color: var(--blue);
}

.text-shadow[class*="-blue"] {
    text-shadow: var(--ShadowSize) var(--blueShadow);
}

.text-purple,
.line-purple,
.lines-purple {
    color: var(--purple);
}

.line-purple::after,
.lines-purple::after {
    border-color: var(--purple);
}

.text-shadow[class*="-purple"] {
    text-shadow: var(--ShadowSize) var(--purpleShadow);
}

.text-mauve,
.line-mauve,
.lines-mauve {
    color: var(--mauve);
}

.line-mauve::after,
.lines-mauve::after {
    border-color: var(--mauve);
}

.text-shadow[class*="-mauve"] {
    text-shadow: var(--ShadowSize) var(--mauveShadow);
}

.text-pink,
.line-pink,
.lines-pink {
    color: var(--pink);
}

.line-pink::after,
.lines-pink::after {
    border-color: var(--pink);
}

.text-shadow[class*="-pink"] {
    text-shadow: var(--ShadowSize) var(--pinkShadow);
}

.text-brown,
.line-brown,
.lines-brown {
    color: var(--brown);
}

.line-brown::after,
.lines-brown::after {
    border-color: var(--brown);
}

.text-shadow[class*="-brown"] {
    text-shadow: var(--ShadowSize) var(--brownShadow);
}

.text-grey,
.line-grey,
.lines-grey {
    color: var(--grey);
}

.line-grey::after,
.lines-grey::after {
    border-color: var(--grey);
}

.text-shadow[class*="-grey"] {
    text-shadow: var(--ShadowSize) var(--greyShadow);
}

.text-black,
.line-black,
.lines-black {
    color: var(--black);
}

.line-black::after,
.lines-black::after {
    border-color: var(--black);
}

.text-shadow[class*="-black"] {
    text-shadow: var(--ShadowSize) var(--blackShadow);
}

.text-white,
.line-white,
.lines-white {
    color: var(--white);
}

.line-white::after,
.lines-white::after {
    border-color: var(--white);
}

.text-shadow[class*="-white"] {
    text-shadow: var(--ShadowSize) var(--whiteShadow);
}

.text-none,
.line-none,
.lines-none {
    color: var(--none);
}

.line-none::after,
.lines-none::after {
    border-color: var(--none);
}

.text-shadow[class*="-none"] {
    text-shadow: var(--ShadowSize) var(--noneShadow);
}

.diy-text {
    font-size: 14px;
}

.text-xs,
.line-xs,
.lines-xs {
    color: var(--xs);
}

.line-xs::after,
.lines-xs::after {
    border-color: var(--xs);
}

.text-shadow[class*="-xs"] {
    text-shadow: var(--ShadowSize) var(--xsShadow);
}

.diy-text-xs {
    font-size: 20rpx;
}

.text-sm,
.line-sm,
.lines-sm {
    color: var(--sm);
}

.line-sm::after,
.lines-sm::after {
    border-color: var(--sm);
}

.text-shadow[class*="-sm"] {
    text-shadow: var(--ShadowSize) var(--smShadow);
}

.diy-text-sm {
    font-size: 24rpx;
}

.text-md,
.line-md,
.lines-md {
    color: var(--md);
}

.line-md::after,
.lines-md::after {
    border-color: var(--md);
}

.text-shadow[class*="-md"] {
    text-shadow: var(--ShadowSize) var(--mdShadow);
}

.diy-text-md {
    font-size: 14px;
}

.text-lg,
.line-lg,
.lines-lg {
    color: var(--lg);
}

.line-lg::after,
.lines-lg::after {
    border-color: var(--lg);
}

.text-shadow[class*="-lg"] {
    text-shadow: var(--ShadowSize) var(--lgShadow);
}

.diy-text-lg {
    font-size: 48rpx;
}

.text-xl,
.line-xl,
.lines-xl {
    color: var(--xl);
}

.line-xl::after,
.lines-xl::after {
    border-color: var(--xl);
}

.text-shadow[class*="-xl"] {
    text-shadow: var(--ShadowSize) var(--xlShadow);
}

.diy-text-xl {
    font-size: 64rpx;
}

.text-cut {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.text-bold {
    font-weight: bold;
}

.text-center {
    text-align: center;
}

.text-content {
    line-height: 1.6;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/*  -- flex弹性布局 -- */
.flex {
    display: flex;
}

.basis-xs {
    flex-basis: 20%;
}

.basis-sm {
    flex-basis: 40%;
}

.basis-df {
    flex-basis: 50%;
}

.basis-lg {
    flex-basis: 60%;
}

.basis-xl {
    flex-basis: 80%;
}

.flex1, .flex-sub {
    flex: 1;
}

.flex-twice {
    flex: 2;
}

.flex-treble {
    flex: 3;
}

.flex-direction-column {
    flex-direction: column;
}

.flex-direction-column .flex1 {
    flex: inherit;
}

.flex-direction-row-reverse {
    flex-direction: row-reverse;
}

.flex-direction-column-reverse {
    flex-direction: column-reverse;
}

.flex-direction-column-reverse .flex1 {
    flex: inherit;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse;
}

.align-start, .items-start {
    align-items: flex-start;
}

.align-end, .items-end {
    align-items: flex-end;
}

.align-center, .items-center {
    align-items: center;
}

.align-stretch, .items-stretch {
    align-items: stretch;
}

.align-baseline, .items-baseline {
    align-items: items-baseline;
}

.self-start {
    align-self: flex-start;
}

.self-center {
    align-self: flex-center;
}

.self-end {
    align-self: flex-end;
}

.self-stretch {
    align-self: stretch;
}

.align-stretch {
    align-items: stretch;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.items-end {
    align-items: flex-end;
}

.scroll-view {
    overflow-x: auto;
    flex-wrap: nowrap;
    flex-direction: row !important;
}

.scroll-view [class*="diy-col-"] {
    flex-shrink: 0;
}

/* ==================
         表单
 ==================== */
.diy-form {
    flex-direction: column;
    width: 100%;
}

/* ==================
         表单
 ==================== */
.diy-form-item {
    width: 100%;
    padding: 12rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.diy-form-item:after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 24rpx;
    bottom: 0;
    left: 24rpx;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.diy-form-item.noborder::after {
    display: none;
}


.diy-form-item .title {
    text-align: left;
    width: var(--form-label-width);
    padding: 4px 0;
    font-weight: bold;
    word-wrap: break-word;
    margin-right: 24rpx;
    position: relative;
}

.diy-form-item .title.title-mb5 {
    margin-bottom: 10rpx;
}

.diy-form-item .input {
    flex: 1;
    display: flex;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 12rpx 0px;
    color: var(--black);
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    resize: none;
    position: relative;
}

.diy-form-item .input .diy-tag, .diy-form-item .input [class*="diy-icon-"] {
    margin-right: 10rpx;
    height: 48rpx;
    width: 48rpx;
}

.diy-form-item .input .icon-right {
    width: 50rpx;
}

.diy-form-item .input .icon {
    width: 50rpx;
    height: 50rpx;
    margin-right: 10rpx;
}

.diy-form-item .input.flex {
    padding-right: 0px;
    display: flex;
}

.diy-form-item .input.flex .flex1 {
    width: 100%;
}

.diy-form-item textarea {
    height: 4.6em;
    width: 100%;
    flex: 1;
    resize: none;
}

.diy-form-item text[class*="diygwIcon-"] {
    font-size: 36rpx;
    padding: 0;
    box-sizing: border-box;
}

.diy-form-item .align-start .title {
    height: 1em;
    margin-top: 32rpx;
    line-height: 1em;
}

.diy-form-item .input {
    padding: 12rpx 0;
}

.diy-form-item .input.solid {
    padding: 12rpx 0 12rpx 20rpx;
}

.diy-form-item .input.solid:after {
    border-radius: 16rpx;
}

.diy-form-item .input.solid.radius {
    overflow: hidden;
}

.diy-form-item .input.solid.radius:after {
    border-radius: 50px;
}

.diy-form-item.flex-direction-column {
    align-items: flex-start;
}

.diy-form-item.flex-direction-column .title {
    margin-right: 0px;
    margin-bottom: 0px;
}

.diy-form-item picker {
    flex: 1;
    padding-right: 40rpx;
    overflow: hidden;
    position: relative;
}

.diy-form-item picker .picker-item {
    height: 48rpx;
    line-height: 48rpx;
}

.diy-form-item picker::after {
    font-family: "diygwui";
    display: block;
    content: "\e71a";
    position: absolute;
    font-size: 34rpx;
    width: 30rpx;
    text-align: center;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}

.diy-form-item textarea[disabled],
.diy-form-item textarea[disabled] .placeholder {
    color: transparent;
}

.upload {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 160rpx;
    height: 160rpx;
    margin: 0 16rpx 16rpx 0;
    background: #f7f8fa;
    border-radius: 4rpx;
    overflow: hidden;
}

.upload .image {
    width: 160rpx;
    height: 160rpx;
}

.upload .diy-icon-close {
    position: absolute;
    right: 0;
    top: 0;
    display: none;
    width: 36rpx;
    font-size: 36rpx;
    height: 36rpx;
    color: #FFF;
    background: rgba(0, 0, 0, 0.88);
    border-radius: 0 0 0 12rpx;
}

.upload:hover .diy-icon-close {
    display: block;
}

/* ==================
          徽章
 ==================== */
.diy-tag {
    vertical-align: middle;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    font-family: Helvetica Neue, Helvetica, sans-serif;
    white-space: nowrap;
    min-width: 40px;
}

.diy-tag.badge:not([class*="bg"]):not([class*="line"]) {
    background-color: #dd514c;
    color: var(--white);
}

.diy-tag {
    font-size: 24rpx;
    padding: 0 20rpx;
    height: 64rpx;
}

.diy-capsule {
    padding: 0;
}

.diy-tag.xs {
    font-size: 20rpx;
    padding: 0 10rpx;
    height: 48rpx;
}

.diy-tag.sm {
    font-size: 24rpx;
    padding: 0 16rpx;
    height: 216rpx;
}

.diy-tag.md {
    font-size: 14px;
    padding: 0 24rpx;
    height: 64rpx;
}

.diy-tag.lg {
    font-size: 48rpx;
    padding: 0 32rpx;
    height: 40px;
}

.diy-tag.xl {
    font-size: 64rpx;
    padding: 0 48rpx;
    height: 60px;
}

.diy-tag.badge[class*="icon"] {
    min-width: 32rpx;
    min-height: 32rpx;
}

.diy-tag + .diy-tag {
    margin-left: 10;
}

.diy-capsule {
    display: inline-flex;
    vertical-align: middle;
}

.diy-capsule.diy-tag {
    height: auto;
}

.diy-capsule + .diy-capsule {
    margin-left: 20rpx;
}

.diy-capsule .diy-tag {
    margin: 0;
}

.diy-tag[class*="line-"]::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1rpx solid currentColor;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: inherit;
    z-index: 1;
    pointer-events: none;
}

.diy-tag[class*="lines-"]::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 2rpx solid currentColor;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: inherit;
    z-index: 1;
    pointer-events: none;
}

.diy-capsule .diy-tag[class*="line-"]:last-child::after {
    border-left: 0 solid transparent;
}

.diy-capsule .diy-tag[class*="line-"]:first-child::after {
    border-right: 0 solid transparent;
}

.diy-capsule .diy-tag:first-child {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

.diy-capsule .diy-tag:last-child::after,
.diy-capsule .diy-tag[class*="line-"] {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

.diy-tag.badge {
    border-radius: 200px;
    position: absolute;
    top: -20rpx;
    right: -20rpx;
    padding: 0 10rpx;
    height: 14px;
}

.diy-tag[class*="diy-icon-"] {
    min-width: 32rpx;
    min-height: 32rpx;
}

/* ==================
         开关
 ==================== */
.diy-checkbox, .diy-radio {
    -webkit-appearance: none;
    width: 0;
    height: 0;
    display: none;
    border: none;
    float: left;
}

.diy-checkbox-label, .diy-radio-label {
    display: flex;
    max-width: 100%;
    margin-bottom: 5px;
    margin-right: 5px;
    align-items: center;
}

.diy-icon-radio {
    border-radius: 100%;
    width: 18px;
    height: 18px;
    margin-right: 5px;
    border: 1px solid #b8b8b8;
    vertical-align: top;
}

.diy-icon-radio.checked {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDE0IDc5LjE1Njc5NywgMjAxNC8wOC8yMC0wOTo1MzowMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjA3QUE4OTlFQjJCODExRTVBRkM2RDBFMDhDRDA3MTJFIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjA3QUE4OTlGQjJCODExRTVBRkM2RDBFMDhDRDA3MTJFIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDdBQTg5OUNCMkI4MTFFNUFGQzZEMEUwOENEMDcxMkUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MDdBQTg5OURCMkI4MTFFNUFGQzZEMEUwOENEMDcxMkUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5LqQGPAAAA7ElEQVR42uyXTQrCMBCFG+tKRY/gz0naXlFc2HqUKvQk4hEUVDCVcQIBu5HMTFK6MA8+KCWZeW2SV6oAIBlSo2RgRQPRQDQw9pg7Rdb2+ozcRVVMEDHJkBrR8JW29zJuPc7gFNmCW6UdG9zAHugqQxvIga88pIGjwEBNqa0IX0Oz269Iytzfb2ThOh2UHFgKmid2zipEELUeWaFdAyhLMEFuwiWYIw/fN2AKNIKnb1zNOUlYCE5BETqIKkbzQ19RvCM0r/qK4m4qmmBqO03N9Ymaftwg+qUZskFeyAV5Soqo+F8QDUQDf2/gI8AAIiHXffupwGIAAAAASUVORK5CYII=);
    background-size: 100%;
    border-radius: 100%;
    background-color: #04be02;
    border: 1px solid #04be02;
}

.diy-icon-checkbox {
    box-sizing: border-box;
    width: 16px;
    height: 16px;
    border: 1px solid #b8b8b8;
    margin-right: 5px;
    vertical-align: top;
}

.diy-icon-checkbox.checked {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDE0IDc5LjE1Njc5NywgMjAxNC8wOC8yMC0wOTo1MzowMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkY0MTI1RkFBQjJCNzExRTU5NzE3RDMyNDM3NTgzRTE4IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkY0MTI1RkFCQjJCNzExRTU5NzE3RDMyNDM3NTgzRTE4Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RjQxMjVGQThCMkI3MTFFNTk3MTdEMzI0Mzc1ODNFMTgiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RjQxMjVGQTlCMkI3MTFFNTk3MTdEMzI0Mzc1ODNFMTgiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5UDJTUAAABUElEQVR42mL8//8/w0ACJoYBBqMOGHEOsB0oB/AC8UIgPgTEZvR2gDYQnwXiOChfH0UWVA7QEPsA8af/CDAJXQ0tLS8G4r9Ili8BYkZ6OaD9Pyo4AcQc2NRS22ImIJ6OZvltIBbFpYealjMD8WI0y78CsS4+fdS0fNl/TBBOSC8uCVBiyQViASItX4HF8j5iHI/LwIVQQ+4CsQEeA1iAeBUWyw9AzSHLAS1ohn0H4hQs6liBeDUWy58AsTix0YdNUBiIz2AxeD4Qc0HVsAHxOixqfgKxNSnpB5cEPxAfw2LBeWiq3vofO8gjNQHjk+QF4kNYLPmHw/Kl5OQgQgpAQb77P2FwDil6qOoABmgRuhWP5a+AWJbcMoRYhaBEtx5HorOjpBAjRTEo261Ec0AKpaUoOUUurJDqpEYxTm6Nl05sSUcIM472jEYdMOIdABBgAFfZNpPCdKCTAAAAAElFTkSuQmCC);
    background-size: 100%;
    background-color: #04be02;
    border: 1px solid #04be02;
}

.diy-switch-box {
    position: relative;
    width: 42px;
    height: 22px;
    border: 1px solid #DFDFDF;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    background-color: #DFDFDF;
    transition: background-color 0.1s, border 0.1s;
}

.diy-switch-box:before {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 20px;
    border-radius: 15px;
    background-color: #FDFDFD;
    transition: -webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
    transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
    transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1), -webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
}

.diy-switch-box:after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    border-radius: 15px;
    background-color: #FFFFFF;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.4);
    transition: -webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
    transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
    transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35), -webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}

.diy-switch-box.checked {
    border-color: #04BE02;
    background-color: #04BE02;
}

.diy-switch {
    position: absolute;
    left: -9999px;
}

.diy-switch-box.checked:before {
    -webkit-transform: scale(0);
    transform: scale(0);
}

.diy-switch-box.checked:after {
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
}

.check-red .diy-icon-radio.checked, .check-red .diy-icon-checkbox.checked, .check-red .diy-switch-box.checked {
    border-color: var(--red) !important;
    background-color: var(--red) !important;
}

.check-orange .diy-icon-radio.checked, .check-orange .diy-icon-checkbox.checked, .check-orange .diy-switch-box.checked {
    border-color: var(--orange) !important;
    background-color: var(--orange) !important;
}

.check-yellow .diy-icon-radio.checked, .check-yellow .diy-icon-checkbox.checked, .check-yellow .diy-switch-box.checked {
    border-color: var(--yellow) !important;
    background-color: var(--yellow) !important;
}

.check-olive .diy-icon-radio.checked, .check-olive .diy-icon-checkbox.checked, .check-olive .diy-switch-box.checked {
    border-color: var(--olive) !important;
    background-color: var(--olive) !important;
}

.check-green .diy-icon-radio.checked, .check-green .diy-icon-checkbox.checked, .check-green .diy-switch-box.checked {
    border-color: var(--green) !important;
    background-color: var(--green) !important;
}

.check-cyan .diy-icon-radio.checked, .check-cyan .diy-icon-checkbox.checked, .check-cyan .diy-switch-box.checked {
    border-color: var(--cyan) !important;
    background-color: var(--cyan) !important;
}

.check-blue .diy-icon-radio.checked, .check-blue .diy-icon-checkbox.checked, .check-blue .diy-switch-box.checked {
    border-color: var(--blue) !important;
    background-color: var(--blue) !important;
}

.check-purple .diy-icon-radio.checked, .check-purple .diy-icon-checkbox.checked, .check-purple .diy-switch-box.checked {
    border-color: var(--purple) !important;
    background-color: var(--purple) !important;
}

.check-mauve .diy-icon-radio.checked, .check-mauve .diy-icon-checkbox.checked, .check-mauve .diy-switch-box.checked {
    border-color: var(--mauve) !important;
    background-color: var(--mauve) !important;
}

.check-pink .diy-icon-radio.checked, .check-pink .diy-icon-checkbox.checked, .check-pink .diy-switch-box.checked {
    border-color: var(--pink) !important;
    background-color: var(--pink) !important;
}

.check-brown .diy-icon-radio.checked, .check-brown .diy-icon-checkbox.checked, .check-brown .diy-switch-box.checked {
    border-color: var(--brown) !important;
    background-color: var(--brown) !important;
}

.check-grey .diy-icon-radio.checked, .check-grey .diy-icon-checkbox.checked, .check-grey .diy-switch-box.checked {
    border-color: var(--grey) !important;
    background-color: var(--grey) !important;
}

.check-black .diy-icon-radio.checked, .check-black .diy-icon-checkbox.checked, .check-black .diy-switch-box.checked {
    border-color: var(--black) !important;
    background-color: var(--black) !important;
}

.check-white .diy-icon-radio.checked, .check-white .diy-icon-checkbox.checked, .check-white .diy-switch-box.checked {
    border-color: var(--white) !important;
    background-color: var(--white) !important;
}

/* grid布局 */
.diy-grid {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
}

.diy-grid [class*="bg-"] .diy-avatar {
    color: #fff;
}

.diy-grid-inner {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: column;
    text-align: center;
    padding: 20rpx;
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
}

.diy-grid-inner.border::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #ebedf0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    z-index: 1;
    border-width: 0 1rpx 1rpx 0;
}

.diy-grid-icon {
    width: 48px;
    height: 48px;
}

.diy-grid.col-1 .diy-grid-item {
    width: 100%;
}

.diy-grid.col-2 .diy-grid-item {
    width: 50%;
}

.diy-grid.col-3 .diy-grid-item {
    width: 33.33%;
}

.diy-grid.col-4 .diy-grid-item {
    width: 25%;
}

.diy-grid.col-5 .diy-grid-item {
    width: 20%;
}

.diy-grid .diy-avatar {
    background-color: transparent;
    font-size: 80rpx;
    color: #333;
}

.diy-grid .diy-avatar .diy-tag[class*="diy-icon-"] {
    font-size: 20rpx;
}

.diy-grid.diy-actions {
    background: inherit;
    min-height: 120rpx;
    align-items: center;
    padding: 6rpx 0;
}

.diy-grid.diy-actions button.diy-action {
    border: 0px;
    background: none;
    border-radius: 0;
    position: relative;
    border: 0px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    overflow: visible;
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
}

.diy-grid.diy-actions button.diy-action::after {
    display: none;
}

.diy-grid.diy-actions .diy-action {
    flex: 1;
    padding: 6rpx 0;
}

.diy-grid.diy-actions .diy-action[class*="bg-"] {
    flex: 2;
}

.diy-grid.diy-actions .diy-action[class*="bg-"] [class*="diy-icon-"] {
    color: inherit;
}

.diy-grid.diy-actions .diy-action.radius-right {
    margin-right: 10rpx;
}

.diy-grid.diy-actions .diy-action.radius-left {
    margin-left: 10rpx;
}

.diy-grid.diy-actions .diy-action.radius {
    margin-right: 10rpx;
    margin-left: 10rpx;
}

.diy-grid.diy-actions .diy-action .diy-grid-inner {
    padding: 0;
}

.diy-grid.diy-actions .diy-action .diy-avatar {
    width: 60rpx;
    height: 60rpx;
    font-size: 60rpx;
}

.diy-grid.diy-actions .diy-action .diy-grid-inner.border::after {
    border-width: 0 1rpx 0 0;
}

.diy-grid.diy-actions .diy-action .diy-grid-title.not-avatar {
    font-size: 16px;
    line-height: 80rpx;
}

.diy-grid.diy-actions .diy-action.addon {
    position: relative;
    z-index: 2;
    padding-top: 60rpx;
    background-color: inherit;
}

.diy-grid.diy-actions .diy-action.addon .diy-grid-title {
    z-index: 2;
}

.diy-grid.diy-actions .diy-action.addon .diy-grid-icon {
    position: absolute;
    width: 70rpx;
    z-index: 2;
    height: 70rpx;
    border-radius: 50%;
    line-height: 70rpx;
    font-size: 50rpx;
    top: -64rpx;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0;
}

.diy-grid.diy-actions .diy-action.addon:after {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 100rpx;
    top: -24rpx;
    left: 0;
    right: 0;
    margin: auto;
    box-shadow: 0 -4rpx 8rpx rgba(0, 0, 0, 0.08);
    border-radius: 50rpx;
    background-color: inherit;
    z-index: 0;
}

.diy-grid.diy-actions .diy-action.addon:before {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 50rpx;
    bottom: 50rpx;
    left: 0;
    right: 0;
    margin: auto;
    background-color: inherit;
    z-index: 1;
}

.diy-grid.diy-actions .diy-action:last-child .diy-grid-inner.border::after, .diy-grid.diy-actions .diy-action:last-child .diy-grid-inner.border::solid-right {
    border-width: 0;
}

/* ==================
         卡片
 ==================== */
.diy-card {
    overflow: hidden;
    margin: 16rpx;
    border-radius: 10rpx;
}

/* ==================
         轮播
 ==================== */
swiper .a-swiper-dot {
    display: inline-block;
    width: 16rpx;
    height: 16rpx;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    vertical-align: middle;
}

swiper[class*=-dot] .wx-swiper-dots,
swiper[class*=-dot] .a-swiper-dots,
swiper[class*=-dot] .uni-swiper-dots {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.swiper-indicator_rect [class*=-dot] {
    width: 32rpx !important;
    height: 14rpx !important;
    border-radius: 0;
}

.swiper-indicator_rect_radius [class*=-dot-active] {
    width: 32rpx !important;
    border-radius: 8rpx;
}

swiper.square-dot .wx-swiper-dot,
swiper.square-dot .a-swiper-dot,
swiper.square-dot .uni-swiper-dot {
    background-color: #ffffff;
    opacity: 0.4;
    width: 10rpx;
    height: 10rpx;
    border-radius: 20rpx;
    margin: 0 8rpx !important;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.square-dot .a-swiper-dot.a-swiper-dot-active,
swiper.square-dot .uni-swiper-dot.uni-swiper-dot-active {
    opacity: 1;
    width: 30rpx;
}

swiper.round-dot .wx-swiper-dot,
swiper.round-dot .a-swiper-dot,
swiper.round-dot .uni-swiper-dot {
    width: 10rpx;
    height: 10rpx;
    position: relative;
    margin: 4rpx 8rpx !important;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active::after,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active::after {
    content: "";
    position: absolute;
    width: 10rpx;
    height: 10rpx;
    top: 0rpx;
    left: 0rpx;
    right: 0;
    bottom: 0;
    margin: auto;
    background-color: #ffffff;
    border-radius: 20rpx;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active {
    width: 18rpx;
    height: 18rpx;
}

.swiper {
    width: 100%;
}

.swiper image,
.swiper video,
.swiper-item image,
.swiper-item video {
    width: 100%;
    display: block;
    height: 100%;
    margin: 0;
    pointer-events: none;
}

.diy-swiper-item-title {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: rgba(0, 0, 0, 0.281);
    text-align: left;
    font-size: 28rpx;
}

.diy-swiper-item-title.not-mask {
    position: relative;
    background: inherit;
    line-height: 80rpx;
    padding-left: 5rpx;
}

.diy-swiper-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow: hidden;
}

.diy-swiper-item-wrap {
    width: 100%;
    height: 100%;
    flex: 1;
    transition: all 0.5s;
    overflow: hidden;
    box-sizing: content-box;
    position: relative;
}

.diy-swiper-image {
    width: 100%;
    height: 100%;
    will-change: transform;
    display: block;
}

.indicator-left-top [class*=-swiper-dots] {
    left: 80rpx;
    top: 20rpx;
    bottom: inherit;
}

.indicator-left-center [class*=-swiper-dots] {
    width: 100% !important;
    left: 0;
    transform: inherit !important;
    top: 20rpx;
    bottom: inherit;
    justify-content: center;
    position: absolute;
    display: flex;
    padding-right: 50rpx;
    flex-direction: row;
    z-index: 1;
}

.indicator-right-top [class*=-swiper-dots] {
    width: calc(100% - 60rpx) !important;
    left: 0;
    transform: inherit !important;
    top: 20rpx;
    bottom: inherit;
    justify-content: flex-end;
    position: absolute;
    display: flex;
    padding-right: 50rpx;
    flex-direction: row;
    z-index: 1;
}

.indicator-left-bottom [class*=-swiper-dots] {
    left: 80rpx;
}

.indicator-right-bottom [class*=-swiper-dots] {
    width: calc(100% - 60rpx) !important;
    left: 0;
    transform: inherit !important;
    justify-content: flex-end;
    position: absolute;
    display: flex;
    padding-right: 50rpx;
    flex-direction: row;
    z-index: 1;
}

/* ==================
          头像
 ==================== */
.diy-avatar {
    font-variant: small-caps;
    display: inline-flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    background-color: #ccc;
    white-space: nowrap;
    position: relative;
    width: 96rpx;
    height: 96rpx;
    background-size: cover;
    background-position: center;
    vertical-align: middle;
    font-size: 32rpx;
}

.diy-avatar [class*="diy-icon-"] {
    font-size: 56rpx;
}

.diy-avatar.bg-none {
    background-color: inherit;
}

.diy-avatar .diy-avatar-img {
    width: 100%;
    height: 100%;
}

.diy-avatar.none {
    width: 64rpx;
    height: 64rpx;
    font-size: 32rpx;
}

.diy-avatar.none [class*="diy-icon-"] {
    font-size: 32rpx;
}

.diy-avatar.xs {
    width: 48rpx;
    height: 48rpx;
    font-size: 24rpx;
}

.diy-avatar.xs [class*="diy-icon-"] {
    font-size: 24rpx;
}

.diy-avatar.sm {
    width: 64rpx;
    height: 64rpx;
    font-size: 32rpx;
}

.diy-avatar.sm [class*="diy-icon-"] {
    font-size: 32rpx;
}

.diy-avatar.md {
    width: 96rpx;
    height: 96rpx;
    font-size: 48rpx;
}

.diy-avatar.md [class*="diy-icon-"] {
    font-size: 48rpx;
}

.diy-avatar.lg {
    width: 128rpx;
    height: 128rpx;
    font-size: 72rpx;
}

.diy-avatar.lg [class*="diy-icon-"] {
    font-size: 72rpx;
}

.diy-avatar.xl {
    width: 200rpx;
    height: 200rpx;
    font-size: 100rpx;
}

.diy-avatar.xl [class*="diy-icon-"] {
    font-size: 100rpx;
}

.diy-avatar .avatar-text {
    font-size: 0.4em;
}

.diy-avatar-group {
    direction: rtl;
    unicode-bidi: bidi-override;
    padding: 0 10rpx 0 40rpx;
    display: inline-block;
}

.diy-avatar-group .diy-avatar {
    margin-left: -30rpx;
    border: 4rpx solid var(--ghostWhite);
    vertical-align: middle;
}

.diy-avatar-group .diy-avatar.sm {
    margin-left: -20rpx;
    border: 1rpx solid var(--ghostWhite);
}

/* ==================
          背景
 ==================== */
.bg-gradual-red, .gradual-red {
    background-image: var(--redGradual) !important;
    color: var(--white) !important;
}

.bg-gradual-orange, .gradual-orange {
    background-image: var(--orangeGradual) !important;
    color: var(--white) !important;
}

.bg-gradual-green, .gradual-green {
    background-image: var(--greenGradual) !important;
    color: var(--white) !important;
}

.bg-gradual-purple, .gradual-purple {
    background-image: var(--purpleGradual) !important;
    color: var(--white) !important;
}

.bg-gradual-pink, .gradual-pink {
    background-image: var(--pinkGradual) !important;
    color: var(--white) !important;
}

.bg-gradual-blue, .gradual-blue {
    background-image: var(--blueGradual) !important;
    color: var(--white) !important;
}

.bg-red, .red {
    background-color: var(--red) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-red"] {
    box-shadow: var(--ShadowSize) var(--redShadow);
}

.bg-red.light, .red.light {
    color: var(--red);
    background-color: var(--redLight);
}

.bg-orange, .orange {
    background-color: var(--orange) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-orange"] {
    box-shadow: var(--ShadowSize) var(--orangeShadow);
}

.bg-orange.light, .orange.light {
    color: var(--orange);
    background-color: var(--orangeLight);
}

.bg-yellow, .yellow {
    background-color: var(--yellow) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-yellow"] {
    box-shadow: var(--ShadowSize) var(--yellowShadow);
}

.bg-yellow.light, .yellow.light {
    color: var(--yellow);
    background-color: var(--yellowLight);
}

.bg-olive, .olive {
    background-color: var(--olive) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-olive"] {
    box-shadow: var(--ShadowSize) var(--oliveShadow);
}

.bg-olive.light, .olive.light {
    color: var(--olive);
    background-color: var(--oliveLight);
}

.bg-green, .green {
    background-color: var(--green) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-green"] {
    box-shadow: var(--ShadowSize) var(--greenShadow);
}

.bg-green.light, .green.light {
    color: var(--green);
    background-color: var(--greenLight);
}

.bg-cyan, .cyan {
    background-color: var(--cyan) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-cyan"] {
    box-shadow: var(--ShadowSize) var(--cyanShadow);
}

.bg-cyan.light, .cyan.light {
    color: var(--cyan);
    background-color: var(--cyanLight);
}

.bg-blue, .blue {
    background-color: var(--blue) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-blue"] {
    box-shadow: var(--ShadowSize) var(--blueShadow);
}

.bg-blue.light, .blue.light {
    color: var(--blue);
    background-color: var(--blueLight);
}

.bg-purple, .purple {
    background-color: var(--purple) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-purple"] {
    box-shadow: var(--ShadowSize) var(--purpleShadow);
}

.bg-purple.light, .purple.light {
    color: var(--purple);
    background-color: var(--purpleLight);
}

.bg-mauve, .mauve {
    background-color: var(--mauve) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-mauve"] {
    box-shadow: var(--ShadowSize) var(--mauveShadow);
}

.bg-mauve.light, .mauve.light {
    color: var(--mauve);
    background-color: var(--mauveLight);
}

.bg-pink, .pink {
    background-color: var(--pink) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-pink"] {
    box-shadow: var(--ShadowSize) var(--pinkShadow);
}

.bg-pink.light, .pink.light {
    color: var(--pink);
    background-color: var(--pinkLight);
}

.bg-brown, .brown {
    background-color: var(--brown) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-brown"] {
    box-shadow: var(--ShadowSize) var(--brownShadow);
}

.bg-brown.light, .brown.light {
    color: var(--brown);
    background-color: var(--brownLight);
}

.bg-grey, .grey {
    background-color: var(--grey) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-grey"] {
    box-shadow: var(--ShadowSize) var(--greyShadow);
}

.bg-grey.light, .grey.light {
    color: var(--grey);
    background-color: var(--greyLight);
}

.bg-black, .black {
    background-color: var(--black) !important;
    color: var(--white) !important;
}

.diy-shadow[class*="-black"] {
    box-shadow: var(--ShadowSize) var(--blackShadow);
}

.bg-black.light, .black.light {
    color: var(--black);
    background-color: var(--blackLight);
}

.bg-white, .white {
    background-color: var(--white) !important;
    color: var(--black) !important;
}

.diy-shadow[class*="-white"] {
    box-shadow: var(--ShadowSize) var(--whiteShadow);
}

.bg-white.light, .white.light {
    color: var(--white);
    background-color: var(--whiteLight);
}

[class*="-line"] {
    background-color: transparent !important;
}

[class*="-line"]::after {
    content: " ";
    display: block;
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1rpx solid currentColor;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 6px;
    z-index: 1;
    pointer-events: none;
}

[class*="-lines"]::after {
    border: 6rpx solid currentColor;
}

.diy-pzx {
    width: 100%;
    margin: 10rpx 0;
}

.bg-shadeTop {
    background-image: linear-gradient(black, rgba(0, 0, 0, 0.01));
    color: var(--white);
}

.bg-shadeBottom {
    background-image: linear-gradient(rgba(0, 0, 0, 0.01), black);
    color: var(--white);
}

.bg-img {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.bg-mask {
    background-color: var(--black);
    position: relative;
}

.bg-mask::after {
    content: "";
    border-radius: inherit;
    width: 100%;
    height: 100%;
    display: block;
    background-color: rgba(0, 0, 0, 0.4);
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
}

.bg-mask view,
.bg-mask cover-view {
    z-index: 5;
    position: relative;
}

.bg-video {
    position: relative;
}

.bg-video video {
    display: block;
    height: 100%;
    width: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    z-index: 0;
    pointer-events: none;
}

/* ==================
         标题栏
 ==================== */
.diy-title {
    display: flex;
    position: relative;
    align-items: center;
    min-height: 90rpx;
    flex: 1;
    padding: 0 20rpx;
    justify-content: space-between;
}

.diy-title [class*=diy-icon-] {
    font-size: inherit;
}

.diy-title .title {
    font-size: 36rpx;
}

.diy-title .title-bar-icon {
    font-size: 36rpx;
}

.diy-title .back-text {
    font-size: 24rpx;
}

.diy-title .more {
    font-size: 24rpx;
}

.diy-navbar .diy-title {
    padding-right: 200rpx;
}

.diy-floatbar, .right-top, .right-bottom, .left-top, .left-bottom {
    position: fixed;
    z-index: 100;
    white-space: nowrap;
    align-items: center;
    display: flex;
    justify-content: space-around;
}

.diy-floatbar .diy-grid.diy-actions, .right-top .diy-grid.diy-actions, .right-bottom .diy-grid.diy-actions, .left-top .diy-grid.diy-actions, .left-bottom .diy-grid.diy-actions {
    flex-direction: column;
}

.diy-floatbar .diy-grid.diy-actions .diy-action, .right-top .diy-grid.diy-actions .diy-action, .right-bottom .diy-grid.diy-actions .diy-action, .left-top .diy-grid.diy-actions .diy-action, .left-bottom .diy-grid.diy-actions .diy-action {
    padding: 10rpx;
}

.diy-floatbar.inline .diy-grid-inner, .right-top.inline .diy-grid-inner, .right-bottom.inline .diy-grid-inner, .left-top.inline .diy-grid-inner, .left-bottom.inline .diy-grid-inner {
    flex-direction: row !important;
}

.diy-floatbar.inline .diy-grid-inner .diy-grid-title, .right-top.inline .diy-grid-inner .diy-grid-title, .right-bottom.inline .diy-grid-inner .diy-grid-title, .left-top.inline .diy-grid-inner .diy-grid-title, .left-bottom.inline .diy-grid-inner .diy-grid-title {
    margin-left: 10rpx;
}

.right-top, .right-bottom {
    right: 0px;
}

.left-top, .left-bottom {
    left: 0px;
}

.left-top {
    right: 0px;
}

/* ==================
         模态窗口
 ==================== */
.diy-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1110;
    opacity: 0;
    outline: 0;
    text-align: center;
    -webkit-transform: scale(1.185);
    transform: scale(1.185);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
    background: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out 0s;
    pointer-events: none;
}

.diy-modal .diy-bar .content {
    width: calc(100% - 80rpx);
}

.diy-modal .diy-dialog-content {
    float: none !important;
    text-align: left;
}

.basic .basis-xs {
    width: 20%;
}

.basic .basis-sm {
    width: 40%;
}

.basic .basis-df {
    width: 50%;
}

.basic .basis-lg {
    width: 85%;
}

.basic .basis-xl {
    width: 80%;
}

.diy-modal::before {
    content: "\200B";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.diy-modal.show {
    opacity: 1;
    transition-duration: 0.3s;
    -webkit-transform: scale(1);
    transform: scale(1);
    overflow-x: hidden;
    overflow-y: auto;
    pointer-events: auto;
}

.diy-dialog {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin-left: auto;
    margin-right: auto;
    width: 340px;
    max-width: 100%;
    background-color: #f8f8f8;
    border-radius: 10rpx;
    overflow: hidden;
}

.diy-modal.bottom-modal::before {
    vertical-align: bottom;
}

.diy-modal.bottom-modal .diy-dialog {
    width: 100%;
    border-radius: 0;
}

.diy-modal.bottom-modal {
    margin-bottom: -500px;
}

.diy-modal.bottom-modal.show {
    margin-bottom: 0;
}

.diy-modal.drawer-left-modal, .diy-modal.drawer-right-modal {
    -webkit-transform: scale(1);
    transform: scale(1);
    display: flex;
}

.diy-modal.drawer-right-modal {
    justify-content: flex-end;
}

.diy-modal.drawer-left-modal .diy-dialog, .diy-modal.drawer-right-modal .diy-dialog {
    height: 100%;
    min-width: 100px;
    border-radius: 0;
    margin: initial;
    transition-duration: 0.3s;
}

.diy-modal.drawer-left-modal .diy-dialog {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
}

.diy-modal.drawer-right-modal .diy-dialog {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
}

.diy-modal.drawer-right-modal.show .diy-dialog, .diy-modal.drawer-left-modal.show .diy-dialog {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
}

.diy-modal .diy-dialog > .diy-bar:first-child .action {
    min-width: 80rpx;
    margin-right: 0;
    min-height: 50rpx;
}

/* ==================
          列表
 ==================== */
.diy-list {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
}

.diy-list.scroll-view {
    overflow-x: auto;
    flex-wrap: nowrap !important;
    flex-direction: row !important;
}

.diy-list.scroll-view .uni-scroll-view {
    width: 100%;
}

.diy-list.scroll-view .diy-item {
    flex-shrink: 0;
}

.diy-list.scroll-view .diy-item.solid-bottom::after {
    border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.diy-list .diy-item {
    position: relative;
    display: flex;
    padding: 20rpx 20rpx;
}

.diy-list .diy-item .diy-avatar {
    flex-shrink: 0;
    font-size: 66rpx;
}

.diy-list .diy-item.col-100 {
    width: 100%;
}

.diy-list .diy-item.col-100.diy-card {
    width: calc(100% - 32rpx);
}

.diy-list .diy-item.col-50 {
    width: 50%;
}

.diy-list .diy-item.col-50.diy-card {
    width: calc(50% - 32rpx);
}

.diy-list .diy-item.col-50.solid-bottom:nth-child(2n+1)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.col-33 {
    width: 33.33%;
}

.diy-list .diy-item.col-33.diy-card {
    width: calc(33.33% - 32rpx);
}

.diy-list .diy-item.col-33.solid-bottom:nth-child(3n+1)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.col-33.solid-bottom:nth-child(3n+2)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.col-25 {
    width: 25%;
}

.diy-list .diy-item.col-25.diy-card {
    width: calc(25% - 32rpx);
}

.diy-list .diy-item.col-25.solid-bottom:nth-child(4n+1)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.col-25.solid-bottom:nth-child(4n+2)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.col-25.solid-bottom:nth-child(4n+3)::after {
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.diy-list .diy-item.arrow {
    padding-right: 80rpx !important;
}

.diy-list .diy-item.arrow:before {
    position: absolute;
    top: 0;
    right: 30rpx;
    bottom: 0;
    display: block;
    margin: auto;
    width: 30rpx;
    height: 30rpx;
    color: #8799a3;
    content: "\e71a";
    text-align: center;
    font-size: 17px;
    font-family: diygwui;
    line-height: 30rpx;
}

.diy-list .diy-item.arrow[class*="bg-"]:before {
    color: #fff;
}

.diy-list .diy-item .content {
    flex: 1;
    padding: 4rpx 0 4rpx 12rpx;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}

.diy-list .diy-item .content .title {
    font-size: 28rpx;
}

.diy-list .diy-item .content .remark {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
    word-wrap: break-word;
}

.diy-list .diy-item.flex-direction-column-reverse, .diy-list .diy-item.flex-direction-column {
    align-items: center;
}

.diy-list .diy-item.flex-direction-column-reverse .content, .diy-list .diy-item.flex-direction-column .content {
    padding: 4rpx 0;
    text-align: center;
}

.diy-list .diy-avatar {
    background-color: inherit;

}


/* ==================
          操作条
 ==================== */

.cu-bar {
    display: flex;
    position: relative;
    align-items: center;
    min-height: 100rpx;
    justify-content: space-between;
}

.cu-bar .action {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    max-width: 100%;
}

.cu-bar .action.border-title {
    position: relative;
    top: -10rpx;
}

.cu-bar .action.border-title text[class*="bg-"]:last-child {
    position: absolute;
    bottom: -0.5rem;
    min-width: 2rem;
    height: 6rpx;
    left: 0;
}

.cu-bar .action.sub-title {
    position: relative;
    top: -0.2rem;
}

.cu-bar .action.sub-title text {
    position: relative;
    z-index: 1;
}

.cu-bar .action.sub-title text[class*="bg-"]:last-child {
    position: absolute;
    display: inline-block;
    bottom: -0.2rem;
    border-radius: 6rpx;
    width: 100%;
    height: 0.6rem;
    left: 0.6rem;
    opacity: 0.3;
    z-index: 0;
}

.cu-bar .action.sub-title text[class*="text-"]:last-child {
    position: absolute;
    display: inline-block;
    bottom: -0.7rem;
    left: 0.5rem;
    opacity: 0.2;
    z-index: 0;
    text-align: right;
    font-weight: 900;
    font-size: 36rpx;
}

.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
}

.cu-bar .action:first-child {
    margin-left: 30rpx;
    font-size: 30rpx;
}

.cu-bar .action text.text-cut {
    text-align: left;
    width: 100%;
}

.cu-bar .cu-avatar:first-child {
    margin-left: 20rpx;
}

.cu-bar .action:first-child>text[class*="cuIcon-"] {
    margin-left: -0.3em;
    margin-right: 0.3em;
}

.cu-bar .action:last-child {
    margin-right: 30rpx;
}

.cu-bar .action>text[class*="cuIcon-"],
.cu-bar .action>view[class*="cuIcon-"] {
    font-size: 36rpx;
}

.cu-bar .action>text[class*="cuIcon-"]+text[class*="cuIcon-"] {
    margin-left: 0.5em;
}

.cu-bar .content {
    position: absolute;
    text-align: center;
    width: calc(100% - 340rpx);
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    height: 60rpx;
    font-size: 32rpx;
    line-height: 60rpx;
    cursor: none;
    pointer-events: none;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.cu-bar.ios .content {
    bottom: 7px;
    height: 30px;
    font-size: 32rpx;
    line-height: 30px;
}

.cu-bar.btn-group {
    justify-content: space-around;
}

.cu-bar.btn-group button {
    padding: 20rpx 32rpx;
}

.cu-bar.btn-group button {
    flex: 1;
    margin: 0 20rpx;
    max-width: 50%;
}

.cu-bar .search-form {
    background-color: #f5f5f5;
    line-height: 64rpx;
    height: 64rpx;
    font-size: 24rpx;
    color: var(--black);
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 30rpx;
}

.cu-bar .search-form+.action {
    margin-right: 30rpx;
}

.cu-bar .search-form input {
    flex: 1;
    padding-right: 30rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 26rpx;
    background-color: transparent;
}

.cu-bar .search-form [class*="cuIcon-"] {
    margin: 0 0.5em 0 0.8em;
}

.cu-bar .search-form [class*="cuIcon-"]::before {
    top: 0rpx;
}

.cu-bar.fixed,
.nav.fixed {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1024;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.foot {
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 1024;
    box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar {
    padding: 0;
    height: calc(100rpx + env(safe-area-inset-bottom) / 2);
    padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.cu-tabbar-height {
    min-height: 100rpx;
    height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.cu-bar.tabbar.shadow {
    box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar .action {
    font-size: 22rpx;
    position: relative;
    flex: 1;
    text-align: center;
    padding: 0;
    display: block;
    height: auto;
    line-height: 1;
    margin: 0;
    overflow: initial;
}

.cu-bar.tabbar.shop .action {
    width: 140rpx;
    flex: initial;
}

.cu-bar.tabbar .action.add-action {
    position: relative;
    z-index: 2;
    padding-top: 50rpx;
    background-color: inherit;
}

.cu-bar.tabbar .action.add-action [class*="cuIcon-"] {
    position: absolute;
    width: 70rpx;
    z-index: 2;
    height: 70rpx;
    border-radius: 50%;
    line-height: 70rpx;
    font-size: 50rpx;
    top: -35rpx;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0;
}

.cu-bar.tabbar .action.add-action::after {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 100rpx;
    top: -50rpx;
    left: 0;
    right: 0;
    margin: auto;
    box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
    border-radius: 50rpx;
    background-color: inherit;
    z-index: 0;
}

.cu-bar.tabbar .action.add-action::before {
    content: "";
    position: absolute;
    width: 100rpx;
    height: 30rpx;
    bottom: 30rpx;
    left: 0;
    right: 0;
    margin: auto;
    background-color: inherit;
    z-index: 1;
}

.cu-bar.tabbar .btn-group {
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 10rpx;
}

.cu-bar.tabbar button.action::after {
    border: 0;
}

.cu-bar.tabbar .action [class*="cuIcon-"] {
    width: 100rpx;
    position: relative;
    display: block;
    height: auto;
    margin: 0 auto 10rpx;
    text-align: center;
    font-size: 40rpx;
}

.cu-bar.tabbar .action .cuIcon-cu-image {
    margin: 0 auto;
}

.cu-bar.tabbar .action .cuIcon-cu-image image {
    width: 50rpx;
    height: 50rpx;
    display: inline-block;
}

.cu-bar.tabbar .submit {
    align-items: center;
    display: flex;
    justify-content: center;
    text-align: center;
    position: relative;
    flex: 2;
    align-self: stretch;
}

.cu-bar.tabbar .submit:last-child {
    flex: 2.6;
}

.cu-bar.tabbar .submit+.submit {
    flex: 2;
}

.cu-bar.tabbar.border .action::before {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    transform: scale(0.5);
    transform-origin: 0 0;
    border-right: 1rpx solid rgba(0, 0, 0, 0.1);
    z-index: 3;
}

.cu-bar.tabbar.border .action:last-child:before {
    display: none;
}

.cu-bar.input {
    padding-right: 20rpx;
    background-color: var(--white);
}

.cu-bar.input input {
    overflow: initial;
    line-height: 64rpx;
    height: 64rpx;
    min-height: 64rpx;
    flex: 1;
    font-size: 30rpx;
    margin: 0 20rpx;
}

.cu-bar.input .action {
    margin-left: 20rpx;
}

.cu-bar.input .action [class*="cuIcon-"] {
    font-size: 48rpx;
}

.cu-bar.input input+.action {
    margin-right: 20rpx;
    margin-left: 0rpx;
}

.cu-bar.input .action:first-child [class*="cuIcon-"] {
    margin-left: 0rpx;
}
.cu-custom {
    display: block;
    position: relative;
}

.cu-custom .cu-bar .content {
    width: calc(100% - 440rpx);
}


.cu-custom .cu-bar .content image {
    height: 60rpx;
    width: 240rpx;
}

.cu-custom .cu-bar {
    min-height: 0px;
    padding-right: 220rpx;
    box-shadow: 0rpx 0rpx 0rpx;
    z-index: 9999;
}

.cu-custom .cu-bar .border-custom {
    position: relative;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 1000rpx;
    height: 30px;
}

.cu-custom .cu-bar .border-custom::after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: inherit;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 1rpx solid var(--white);
    opacity: 0.5;
}

.cu-custom .cu-bar .border-custom::before {
    content: " ";
    width: 1rpx;
    height: 110%;
    position: absolute;
    top: 22.5%;
    left: 0;
    right: 0;
    margin: auto;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    opacity: 0.6;
    background-color: var(--white);
}

.cu-custom .cu-bar .border-custom text {
    display: block;
    flex: 1;
    margin: auto !important;
    text-align: center;
    font-size: 34rpx;
}

.diy-list.small .diy-avatar {
    width: 48rpx !important;
    height: 48rpx !important;
    font-size: 48px !important;
}

.diy-list.small .content {
    flex-direction: row !important;
    align-items: center;
}

.diy-list .solid-small-bottom::after {
    width: calc(200% - 80rpx);
    left: 20rpx;
}

.diy-list [class*="bg-"] .diy-avatar {
    color: #fff;
}

.diy-list .uni-scroll-view {
    width: 100%;
}

.diy-list.scroll-view, .diy-list .uni-scroll-view-content {
    flex-wrap: nowrap !important;
    flex-direction: row !important;
}

.diy-list.scroll-view::-webkit-scrollbar, .diy-list .uni-scroll-view-content::-webkit-scrollbar {
    display: none;
}

.diy-list.scroll-view .diy-grid-item, .diy-list .uni-scroll-view-content .diy-grid-item {
    flex-shrink: 0;
}

/* ==================
         时间轴
 ==================== */
.diy-timeline {
    display: block;
    background-color: var(--white);
    width: 100%;
}

.diy-timeline .diy-time {
    width: 100%;
    text-align: left;
    padding: 20rpx 0;
    font-size: 26rpx;
    font-weight: bold;
    color: #888;
    display: block;
}

.diy-timeline > .diy-item {
    padding: 30rpx 30rpx 30rpx 60px;
    position: relative;
    display: block;
    z-index: 0;
    font-size: 12px;
}

.diy-timeline > .diy-item:not([class*="diy-icon-"])::before {
    content: "\e763";
}

.diy-timeline > .diy-item::after {
    content: "";
    display: block;
    position: absolute;
    width: 1px;
    background-color: #ddd;
    left: 60rpx;
    height: 100%;
    top: 0;
    z-index: 8;
}

.diy-timeline > .diy-item::before {
    font-size: 36rpx;
    display: block;
    position: absolute;
    top: 36rpx;
    z-index: 9;
    background-color: var(--white);
    width: 50rpx;
    height: 50rpx;
    text-align: center;
    border: none;
    line-height: 50rpx;
    left: 36rpx;
}

.diy-timeline > .diy-item > .content {
    padding: 30rpx;
    border-radius: 8rpx;
    display: block;
    line-height: 1.6;
}

.diy-timeline > .diy-item > .content:not([class*="bg-"]) {
    background-color: var(--ghostWhite);
    color: var(--black);
}

.diy-timeline > .diy-item > .content + .content {
    margin-top: 20rpx;
}

.diy-timeline > .diy-item:not([class*="text-"]) {
    color: #ccc;
}

/* ==================
          步骤条
 ==================== */
.diy-steps {
    display: flex;
    width: 100%;
}

.diy-steps .diy-step-item {
    flex: 1;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
}

.diy-steps .diy-step-item:not([class*="text-"]) {
    color: var(--grey);
}

.diy-steps .diy-step-item [class*="diy-icon-"] {
    display: block;
    font-size: 24px;
    line-height: 40px;
}

.diy-steps .diy-step-item::before, .diy-steps .diy-step-item::after {
    content: "";
    display: block;
    position: absolute;
    height: 0px;
    width: calc(100% - 40px);
    border-bottom: 1px solid #ccc;
    left: calc(0px - (100% - 40px) / 2);
    top: 20px;
    z-index: 0;
}

.diy-steps .diy-step-item::after {
    border-bottom: 1px solid currentColor;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
}

.diy-steps .diy-step-item:first-child::before, .diy-steps .diy-step-item:first-child::after {
    display: none;
}

.diy-steps .diy-step-item .num {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    line-height: 30px;
    margin: 10px auto;
    font-size: 24px;
    border: 1px solid currentColor;
    position: relative;
    overflow: hidden;
}

.diy-steps .diy-step-item.small .num {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
}

.diy-steps .diy-step-item.small [class*="diy-icon-"] {
    font-size: 16px;
}

.diy-steps .diy-step-item.middle .num {
    width: 40px;
    height: 40px;
    line-height: 40px;
}

.diy-steps .diy-step-item.middle [class*="diy-icon-"] {
    font-size: 30px;
}

.diy-steps .diy-step-item.large .num {
    width: 50px;
    height: 50px;
    line-height: 50px;
}

.diy-steps .diy-step-item.large [class*="diy-icon-"] {
    font-size: 40px;
}

.diy-steps .diy-step-item .num::before, .diy-steps .diy-step-item .num::after {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    transition: all 0.3s ease-in-out 0s;
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
}

.diy-steps .diy-step-item .num::after {
    content: attr(data-index);
    color: #333;
    transition: all 0.3s ease-in-out 0s;
}

.diy-steps.steps-arrow .diy-step-item::before,
.diy-steps.steps-arrow .diy-step-item::after {
    content: "\e71a";
    font-family: "diygwui";
    height: 15px;
    border-bottom-width: 0px;
    line-height: 15px;
    top: 0;
    bottom: 0;
    margin: auto;
    color: #ccc;
}

.diy-steps.steps-arrow .diy-step-item::after {
    display: none;
}

.diy-steps.steps-numbers .diy-step-item::before, .diy-steps.steps-numbers .diy-step-item::after {
    top: 25px;
}

.diy-steps.steps-numbers .diy-step-item.small::before, .diy-steps.steps-numbers .diy-step-item.small::after {
    top: 20px;
}

.diy-steps.steps-numbers .diy-step-item.middle::before, .diy-steps.steps-numbers .diy-step-item.middle::after {
    top: 30px;
    left: calc(0px - (100% - 50px) / 2);
    width: calc(100% - 50px);
}

.diy-steps.steps-numbers .diy-step-item.large::before, .diy-steps.steps-numbers .diy-step-item.large::after {
    top: 40px;
    left: calc(0px - (100% - 60px) / 2);
    width: calc(100% - 60px);
}

.diy-steps.steps-top .diy-step-item {
    flex-direction: column-reverse;
}

.diy-steps.steps-top .diy-step-item::before, .diy-steps.steps-top .diy-step-item::after {
    bottom: 20px;
    top: initial;
}

.diy-steps .diy-step-item[class*="text-"]::after, .diy-steps .diy-step-item[class*="text-"]::before {
    width: calc(100% - 40px);
    color: currentColor;
}

.diy-steps .diy-step-item[class*="text-"] .num {
    background-color: currentColor;
}

.diy-steps .diy-step-item[class*="text-"] .num::before {
    color: var(--white);
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
}

.diy-steps .diy-step-item[class*="text-"] .num::after {
    display: none;
}

scroll-view.diy-steps {
    display: flex;
    white-space: nowrap;
    flex-direction: row;
}

scroll-view.diy-steps .uni-scroll-view-content {
    flex-direction: row;
    display: flex;
}

scroll-view.diy-steps .diy-step-item {
    display: flex;
    min-width: 120px;
    flex-shrink: 0;
}

/* ==================
         导航栏
 ==================== */
.diy-tabs {
    white-space: nowrap;
    width: 100%;
    display: flex;
}

.diy-tabs.not-border .diy-tab-item.cur {
    border: 0px !important;
}

.diy-tabs.not-border .diy-tab-item.cur::after {
    display: none;
}

.diy-tabs.not-border[class*='solid-']::after, .diy-tabs.not-border[class*='solid-']::before {
    display: none;
}

.diy-tabs.small-border .diy-tab-item.cur {
    border: 0px;
}

.diy-tabs.small-border .diy-tab-item.cur::after {
    content: "";
    width: 40rpx;
    height: 4rpx;
    position: absolute;
    background-color: currentColor;
    left: calc(50% - 10px);
    bottom: 0;
    margin: auto;
}

.diy-tabs .diy-tab-item {
    height: 45px;
    display: inline-block;
    line-height: 45px;
    font-size: 14px;
    margin: 0 10rpx;
    padding: 0 10px;
    position: relative;
}

.diy-tabs .diy-tab-item.cur {
    border-bottom: 4rpx solid;
}

.diy-tabs.scroll-view, .diy-tabsscroll-view {
    white-space: nowrap;
    overflow-x: auto;
}

.diy-tabs.scale-title .diy-tab-item.cur {
    font-size: 16px;
}

.flex-direction-column-reverse .diy-tab-item.cur {
    border-top: 4rpx solid;
    border-bottom: 0px;
}

.flex-direction-column-reverse .small-border .diy-tab-item.cur {
    border: 0px;
}

.flex-direction-column-reverse .small-border .diy-tab-item.cur::after {
    top: 0;
    bottom: inherit;
}

.flex-direction-row .diy-tabs {
    flex-direction: column;
    width: 240rpx;
}

.flex-direction-row .diy-tabs .diy-tab-item {
    margin: 0;
}

.flex-direction-row .diy-tabs .diy-tab-item.cur {
    border-bottom: 0px solid;
}

.flex-direction-row .diy-tabs .diy-tab-item.cur::after {
    content: "";
    width: 8rpx;
    height: 30rpx;
    border-radius: 6rpx 0 0 6rpx;
    position: absolute;
    background-color: currentColor;
    top: 0;
    right: 0px;
    bottom: 0;
    margin: auto;
}

.flex-direction-row-reverse .diy-tabs {
    flex-direction: column;
    width: 240rpx;
    position: relative;
}

.flex-direction-row-reverse .diy-tabs .diy-tab-item {
    margin: 0;
}

.flex-direction-row-reverse .diy-tabs .diy-tab-item.cur {
    border-bottom: 0px solid;
}

.flex-direction-row-reverse .diy-tabs .diy-tab-item.cur::after {
    content: "";
    width: 8rpx;
    height: 30rpx;
    border-radius: 0 10rpx 10rpx 0;
    position: absolute;
    background-color: currentColor;
    top: 0;
    left: 0px;
    bottom: 0;
    margin: auto;
}

/* ==================
         消息组件
 ==================== */
.diy-notice-bar {
    padding: 18rpx 24rpx;
    overflow: hidden;
    width: 100%;
}

.diy-direction-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.diy-left-icon {
    align-items: center;
}

.diy-notice-box {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    margin-left: 12rpx;
}

.diy-right-icon {
    margin-left: 12rpx;
    display: inline-flex;
    align-items: center;
}

.diy-notice-content {
    -webkit-animation: diy-loop-animation 10s linear infinite both;
    animation: diy-loop-animation 10s linear infinite both;
    text-align: right;
    padding-left: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.diy-notice-img {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    object-fit: contain;
}

.diy-notice-text {
    font-size: 28rpx;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    white-space: nowrap;
}

@-webkit-keyframes diy-loop-animation {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    100% {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes diy-loop-animation {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    100% {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

.diy-notice-swiper {
    height: 40rpx;
}

.diy-notice-swiper {
    height: 40rpx;
    align-items: center;
    justify-content: center;
}

.diy-notice-item + .diy-notice-item {
    margin-left: 20rpx;
}

.diy-notice-swiper-item {
    display: block;
    height: 40rpx;
    width: 100%;
    line-height: 40rpx;
    text-align: center;
}

/* ==================
         进度条
 ==================== */
@-webkit-keyframes progress-stripes {
    from {
        background-position: 72rpx 0;
    }
    to {
        background-position: 0 0;
    }
}

@keyframes progress-stripes {
    from {
        background-position: 72rpx 0;
    }
    to {
        background-position: 0 0;
    }
}

.diy-progress {
    overflow: hidden;
    height: 28rpx;
    background-color: #ebeef5;
    display: inline-flex;
    align-items: center;
    width: 100%;
}

.diy-progress .diy-progress-content {
    line-height: 1;
    width: 0;
    height: 100%;
    align-items: center;
    display: flex;
    justify-items: flex-end;
    justify-content: space-around;
    font-size: 10px;
    color: var(--white);
    transition: width 0.6s ease;
}

.diy-progress.striped .diy-progress-content {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 72rpx 72rpx;
}

.diy-progress.striped.active .diy-progress-content {
    -webkit-animation: progress-stripes 2s linear infinite;
    animation: progress-stripes 2s linear infinite;
}

.load-progress {
    pointer-events: none;
    top: 0;
    position: fixed;
    width: 100%;
    left: 0;
    z-index: 2000;
}

.load-progress.hide {
    display: none;
}

.load-progress .load-progress-bar {
    position: relative;
    width: 100%;
    height: 4rpx;
    overflow: hidden;
    transition: all 200ms ease 0s;
}

.load-progress .load-progress-spinner {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    z-index: 2000;
    display: block;
}

.load-progress .load-progress-spinner::after {
    content: "";
    display: block;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border: solid 4rpx transparent;
    border-top-color: inherit;
    border-left-color: inherit;
    border-radius: 50%;
    -webkit-animation: load-progress-spinner 0.4s linear infinite;
    animation: load-progress-spinner 0.4s linear infinite;
}

@-webkit-keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.login-container {
    height: 100%;
    padding: 10px 30px;
    background: #fff;
}

.app-info {
    position: relative;
    padding: 20px;
    text-align: center;
}

.app-info:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid #E5E5E5;
    color: #E5E5E5;
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.app-info .app-logo {
    display: block;
    width: 64px;
    height: 64px;
    margin: 10px auto;
    border-radius: 4px;
}

.app-info .app-name {
    font-weight: bold;
    font-size: 18px;
    color: #000;
}

.alert {
    margin: 20px 0 30px;
}

.alert .alert-title {
    font-weight: 400;
    font-size: 16px;
    color: #000;
    margin-bottom: 10px;
}

.alert-desc {
    display: block;
    list-style: disc inside none;
}

.alert .alert-text {
    display: list-item;
    text-align: -webkit-match-parent;
    font-size: 14px;
    color: #999;
}

.logged {
    margin-top: 100px;
}

.logged .logged-icon {
    display: block;
    width: 64px;
    height: 64px;
    margin: 20px auto;
}

.logged .logged-text {
    font-size: 14px;
    color: #000;
    text-align: center;
    margin: 10px 0;
}

/* ==================
         搜索条
 ==================== */
.diy-search {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 10rpx;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    color: var(--black);
    line-height: 48rpx;
    text-align: left;
    background-color: transparent;
    resize: none;
    position: relative;
}
.diy-search .flex .diy-tag, .diy-search .flex [class*=diy-icon-] {
    margin-right: 10rpx;
    height: 48rpx;
}
.diy-search .flex1 {
    background: transparent;
}
.diy-search .icon-right {
    width: 50rpx;
}
.diy-search .icon {
    width: 50rpx;
    height: 50rpx;
    margin-right: 10rpx;
}
.diy-search .right-icon {
    width: 64rpx;
    height: 64rpx;
}
.search.flex {
    padding-right: 0px;
    display: flex;
}
.search.flex .flex1 {
    width: 100%;
    background: transparent;
}
.diy-sticky-100 {
    width: 100% !important;
    background-color: #fff;
}