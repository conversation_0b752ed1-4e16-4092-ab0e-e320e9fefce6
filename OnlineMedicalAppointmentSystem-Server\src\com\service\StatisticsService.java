package com.service;

public interface StatisticsService {
    
    /**
     * 获取患者总数
     */
    int getPatientCount() throws Exception;
    
    /**
     * 获取医生总数
     */
    int getDoctorCount() throws Exception;
    
    /**
     * 获取今日挂号总数
     */
    int getTodayAppointmentCount() throws Exception;
    
    /**
     * 获取总挂号数
     */
    int getTotalAppointmentCount() throws Exception;
    
    /**
     * 获取指定医生今日挂号数
     */
    int getDoctorTodayAppointmentCount(Integer doctorId) throws Exception;
    
    /**
     * 获取指定医生总挂号数
     */
    int getDoctorTotalAppointmentCount(Integer doctorId) throws Exception;
}
