@import 'mixins/mixins';
@import 'mixins/var';
@import 'common/var';
@import 'common/transition';
@import 'checkbox';

@include b(tree) {
  @include set-component-css-var('tree', $--tree);
}

@include b(tree) {
  position: relative;
  cursor: default;
  background: var(--el-color-white);
  color: var(--el-tree-font-color);

  @include e(empty-block) {
    position: relative;
    min-height: 60px;
    text-align: center;
    width: 100%;
    height: 100%;
  }

  @include e(empty-text) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--el-text-color-secondary);
    font-size: var(--el-font-size-base);
  }

  @include e(drop-indicator) {
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--el-color-primary);
  }
}

@include b(tree-node) {
  white-space: nowrap;
  outline: none;
  &:focus {
    /* focus */
    > .#{$namespace}-tree-node__content {
      background-color: var(--el-tree-node-hover-background-color);
    }
  }

  @include when(drop-inner) {
    > .#{$namespace}-tree-node__content .#{$namespace}-tree-node__label {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }

  @include e(content) {
    display: flex;
    align-items: center;
    height: 26px;
    cursor: pointer;

    & > .#{$namespace}-tree-node__expand-icon {
      padding: 6px;
    }
    & > label.#{$namespace}-checkbox {
      margin-right: 8px;
    }
    &:hover {
      background-color: var(--el-tree-node-hover-background-color);
    }

    .#{$namespace}-tree.is-dragging & {
      cursor: move;

      & * {
        pointer-events: none;
      }
    }

    .#{$namespace}-tree.is-dragging.is-drop-not-allow & {
      cursor: not-allowed;
    }
  }

  @include e(expand-icon) {
    cursor: pointer;
    color: var(--el-tree-expand-icon-color);
    font-size: 12px;

    transform: rotate(0deg);
    transition: transform var(--el-transition-duration) ease-in-out;

    &.expanded {
      transform: rotate(90deg);
    }

    &.is-leaf {
      color: transparent;
      cursor: default;
    }
  }

  @include e(label) {
    font-size: var(--el-font-size-base);
  }

  @include e(loading-icon) {
    margin-right: 8px;
    font-size: var(--el-font-size-base);
    color: var(--el-tree-expand-icon-color);
  }

  & > .#{$namespace}-tree-node__children {
    overflow: hidden;
    background-color: transparent;
  }

  &.is-expanded > .#{$namespace}-tree-node__children {
    display: block;
  }
}

.#{$namespace}-tree--highlight-current
  .#{$namespace}-tree-node.is-current
  > .#{$namespace}-tree-node__content {
  background-color: var(--el-color-primary-light-9);
}
