<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans-4.0.xsd 
		http://www.springframework.org/schema/mvc 
		http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd 
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context-4.0.xsd 
		http://www.springframework.org/schema/aop 
		http://www.springframework.org/schema/aop/spring-aop-4.0.xsd 
		http://www.springframework.org/schema/tx 
		http://www.springframework.org/schema/tx/spring-tx-4.0.xsd ">
		<!-- 加载db.properties文件中的内容,db.properties文件中的key要有一定的特殊规则 -->
		<context:property-placeholder location="classpath:db.properties"/>
		<!-- 配置数据源,使用dbcp连接池 -->
		<bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
			<property name="driverClassName" value="${jdbc.driver}"/>
			<property name="url" value="${jdbc.url}"/>
			<property name="username" value="${jdbc.username}"/>
			<property name="password" value="${jdbc.password}"/>
			<property name="maxActive" value="30"/>
			<property name="maxIdle" value="5"/>
			<property name="maxWait" value="60000" />
		</bean>
		<!-- 配置SqlSessionFactory -->
		<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
			<!-- 数据源 -->
			<property name="dataSource" ref="dataSource"/>
			<!-- 加载mybatis的全局配置文件 -->
			<property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml" />
		</bean>
		
		<!-- 配置Mapper扫描器 -->
		<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
			<!-- 扫描包路径,如果需要扫描多个包中间用半角逗号隔开 -->
			<property name="basePackage" value="com.mapper"/>
			<!-- 这边不能使用ref="sqlSessionFactory"原因是因为上面加载配置文件导致这边引用会报错 -->
			<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
		</bean>
		
				<bean name="multipartResolver"  
    class="org.springframework.web.multipart.commons.CommonsMultipartResolver">  
    <property name="defaultEncoding" value="UTF-8"></property>  
    </bean>
</beans>