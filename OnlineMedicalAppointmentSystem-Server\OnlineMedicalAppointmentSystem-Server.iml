<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5PbmxpbmVNZWRpY2FsQXBwb2ludG1lbnRTeXN0ZW0tU2VydmVyPC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iSTovcHJvZHVjdDQvMDA0NTJIb3NwaXRhbFJlZ2lzdHJhdGlvbkJvb2tpbmcvT25saW5lTWVkaWNhbEFwcG9pbnRtZW50U3lzdGVtLVNlcnZlci9vdXQvcHJvZHVjdGlvbi9PbmxpbmVNZWRpY2FsQXBwb2ludG1lbnRTeXN0ZW0tU2VydmVyIj48L2Rpcj48L2NsYXNzcGF0aD48d2ViPjxsaW5rIHRhcmdldD0iLyI+PGRpciBuYW1lPSJJOi9wcm9kdWN0NC8wMDQ1Mkhvc3BpdGFsUmVnaXN0cmF0aW9uQm9va2luZy9PbmxpbmVNZWRpY2FsQXBwb2ludG1lbnRTeXN0ZW0tU2VydmVyL1dlYlJvb3QiPjwvZGlyPjwvbGluaz48L3dlYj48L2FwcGxpY2F0aW9uPg==" />
          </map>
        </option>
        <option name="version" value="5" />
      </configuration>
    </facet>
    <facet type="web" name="Web">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/WebRoot/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/WebRoot" relative="/" />
        </webroots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="lib" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Tomcat 9.0.54" level="application_server_libraries" />
  </component>
</module>