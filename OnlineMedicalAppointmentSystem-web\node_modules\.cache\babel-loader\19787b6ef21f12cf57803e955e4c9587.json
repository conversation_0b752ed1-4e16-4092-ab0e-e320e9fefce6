{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "data", "year", "Date", "getFullYear", "loginModel", "username", "password", "radio", "loginModel2", "mounted", "created", "methods", "login", "that", "$message", "message", "type", "loading", "role", "url", "aname", "loginpassword", "post", "then", "res", "code", "console", "log", "JSON", "stringify", "resdata", "sessionStorage", "setItem", "$router", "push", "msg", "daccount"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>  \r\n  <div class=\"body\">  \r\n    <div class=\"login-container\">  \r\n           <h2 style=\"text-align: center; font-size: 24px; margin-bottom: 24px;\">医院挂号预约系统</h2>\r\n            <form>  \r\n               <div>  \r\n                   <label for=\"uaccount\">账号</label>  \r\n                   <input type=\"text\" id=\"uaccount\" placeholder=\"请输入账号\" required v-model=\"loginModel.username\">  \r\n               </div>  \r\n               <div>  \r\n                   <label for=\"password\">密码</label>  \r\n                   <input type=\"password\" id=\"password\" placeholder=\"请输入密码\" required v-model=\"loginModel.password\">  \r\n               </div>  \r\n                   <div>  \r\n                   <label>身份</label>  \r\n                   <div class=\"role-selection\">  \r\n                              <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\r\n      <el-radio label=\"医生\" v-model=\"loginModel.radio\">医生</el-radio>\r\n \r\n                   </div>  \r\n               </div> \r\n    \r\n               <button type=\"button\" @click=\"login\">登录</button>  \r\n           </form>  \r\n            \r\n \r\n<!--           <p>还没有账户？ <a href=\"#\">注册</a></p> --> \r\n       </div>  \r\n       <div class=\"bubble\" style=\"width: 60px; height: 60px; left: 20%; animation-delay: 0s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 40px; height: 40px; left: 50%; animation-delay: 2s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 80px; height: 80px; left: 80%; animation-delay: 4s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 30px; height: 30px; left: 30%; animation-delay: 1s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 50px; height: 50px; left: 70%; animation-delay: 3s;\"></div>   \r\n   </div>  \r\n</template>\r\n<script>\nimport request, { base } from \"../../utils/http\";\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      year: new Date().getFullYear(),\n      loginModel: {\n        username: \"\",\n        password: \"\",\n        radio: \"管理员\",\n      },\n      loginModel2: {},\n     \n    };\n  },\n  mounted() {},\n  created() {\n    \n  },\n  methods: {\n    login() {\n      let that = this;  \n\n      if (that.loginModel.username == \"\") {\n        that.$message({\n          message: \"请输入账号\",\n          type: \"warning\",\n        });\n        return;\n      }\n      if (that.loginModel.password == \"\") {\n        that.$message({\n          message: \"请输入密码\",\n          type: \"warning\",\n        });\n        return;\n      }   \n      \n      this.loading = true;\n     var role = that.loginModel.radio; //获取身份\r\nif (role == '管理员') {\r\n      let url = base + \"/admin/login\";\n      this.loginModel2.aname = this.loginModel.username;\n      this.loginModel2.loginpassword = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\n          sessionStorage.setItem(\"role\", \"管理员\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\r\n          }\r\nelse if (role == '医生') {\r\n      let url = base + \"/doctor/login\";\n      this.loginModel2.daccount = this.loginModel.username;\n      this.loginModel2.password = this.loginModel.password;\n      request.post(url, this.loginModel2).then((res) => {\n        this.loading = false;\n        if (res.code == 200) {\n          console.log(JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\n          sessionStorage.setItem(\"userLname\", res.resdata.daccount);\n          sessionStorage.setItem(\"role\", \"医生\");\n          this.$router.push(\"/main\");\n        } else {\n          this.$message({\n            message: res.msg,\n            type: \"error\",\n          });\n        }\n      });\r\n          }\r\n    \n     \n    },\n    \n    \n  },\n};\n</script>\r\n   \r\n<style scoped>  \r\n@import \"../assets/css/body.css\";\r\n/* 全局样式重置 */  \r\n* {  \r\n    margin: 0;  \r\n    padding: 0;  \r\n    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  \r\n}  \r\n\r\nbody {  \r\n    width: 100%;  \r\n    height: 100%;  \r\n    overflow: hidden; /* 确保没有滚动条 */  \r\n}  \r\n\r\n.body {  \r\n    background: linear-gradient(to bottom right, #009688, #8BC34A);  \r\n    display: flex;  \r\n    align-items: center;  \r\n    justify-content: center;  \r\n    height: 100vh;  \r\n}  \r\n\r\n.login-container {  \r\n    background: white;  \r\n    border-radius: 8px;  \r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  \r\n    padding: 32px;  \r\n    position: relative;  \r\n    z-index: 10;  \r\n    transition: transform 0.3s ease;  \r\n    width: 400px;  \r\n}  \r\n\r\n.login-container:hover {  \r\n    transform: scale(1.05);  \r\n}  \r\n\r\n.bubble {  \r\n    position: absolute;  \r\n    bottom: -100px;  \r\n    border-radius: 50%;  \r\n    background: rgba(255, 255, 255, 0.6);  \r\n    animation: rise 10s infinite;  \r\n}  \r\n\r\n@keyframes rise {  \r\n    0% {  \r\n        transform: translateY(0);  \r\n        opacity: 1;  \r\n    }  \r\n    100% {  \r\n        transform: translateY(-600px);  \r\n        opacity: 0;  \r\n    }  \r\n}  \r\n\r\ninput {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    margin: 10px 0;  \r\n    border: 1px solid #ccc;  \r\n    border-radius: 4px;  \r\n}  \r\n\r\nbutton {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    background-color: #8BC34A;  \r\n    color: white;  \r\n    border: none;  \r\n    border-radius: 4px;  \r\n    cursor: pointer;  \r\n    transition: background-color 0.2s;  \r\n}  \r\n\r\nbutton:hover {  \r\n    background-color: #81d522;  \r\n}  \r\n\r\np {  \r\n    text-align: center;  \r\n    color: #666;  \r\n}  \r\n\r\na {  \r\n    color: #8BC34A;  \r\n    text-decoration: none;  \r\n}  \r\n\r\na:hover {  \r\n    text-decoration: underline;  \r\n}  \r\n\r\n.role-selection {  \r\n    display: flex;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n\r\n.role-selection input {  \r\n    margin-right: 5px;  \r\n}  \r\n\r\ninput[type=\"radio\"] {  \r\n    display: flex;  \r\n    width: 50px;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n</style>\r\n\r\n"], "mappings": ";AAoCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAChD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE,CAAC;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG,CAAC,CAAC;EACZC,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAIC,IAAG,GAAI,IAAI;MAEf,IAAIA,IAAI,CAACT,UAAU,CAACC,QAAO,IAAK,EAAE,EAAE;QAClCQ,IAAI,CAACC,QAAQ,CAAC;UACZC,OAAO,EAAE,OAAO;UAChBC,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MACA,IAAIH,IAAI,CAACT,UAAU,CAACE,QAAO,IAAK,EAAE,EAAE;QAClCO,IAAI,CAACC,QAAQ,CAAC;UACZC,OAAO,EAAE,OAAO;UAChBC,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,OAAM,GAAI,IAAI;MACpB,IAAIC,IAAG,GAAIL,IAAI,CAACT,UAAU,CAACG,KAAK,EAAE;MACvC,IAAIW,IAAG,IAAK,KAAK,EAAE;QACb,IAAIC,GAAE,GAAIrB,IAAG,GAAI,cAAc;QAC/B,IAAI,CAACU,WAAW,CAACY,KAAI,GAAI,IAAI,CAAChB,UAAU,CAACC,QAAQ;QACjD,IAAI,CAACG,WAAW,CAACa,aAAY,GAAI,IAAI,CAACjB,UAAU,CAACE,QAAQ;QACzDT,OAAO,CAACyB,IAAI,CAACH,GAAG,EAAE,IAAI,CAACX,WAAW,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACV,KAAK,CAAC;YACtDW,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;YACrC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACpB,QAAQ,CAAC;cACZC,OAAO,EAAES,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE,OACL,IAAIE,IAAG,IAAK,IAAI,EAAE;QACjB,IAAIC,GAAE,GAAIrB,IAAG,GAAI,eAAe;QAChC,IAAI,CAACU,WAAW,CAAC4B,QAAO,GAAI,IAAI,CAAChC,UAAU,CAACC,QAAQ;QACpD,IAAI,CAACG,WAAW,CAACF,QAAO,GAAI,IAAI,CAACF,UAAU,CAACE,QAAQ;QACpDT,OAAO,CAACyB,IAAI,CAACH,GAAG,EAAE,IAAI,CAACX,WAAW,CAAC,CAACe,IAAI,CAAEC,GAAG,IAAK;UAChD,IAAI,CAACP,OAAM,GAAI,KAAK;UACpB,IAAIO,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;YACnBC,OAAO,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YACxCC,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEJ,IAAI,CAACC,SAAS,CAACL,GAAG,CAACM,OAAO,CAAC,CAAC;YAC3DC,cAAc,CAACC,OAAO,CAAC,WAAW,EAAER,GAAG,CAACM,OAAO,CAACM,QAAQ,CAAC;YACzDL,cAAc,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YACpC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC5B,OAAO;YACL,IAAI,CAACpB,QAAQ,CAAC;cACZC,OAAO,EAAES,GAAG,CAACW,GAAG;cAChBnB,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACE;IAGN;EAGF;AACF,CAAC", "ignoreList": []}]}