package com.model;
import java.util.List;

/**
* (doctor)医生实体类
*/
public class Doctor extends ComData{
	
	private static final long serialVersionUID = 172712112368238L;
	private Integer did;    //医生ID
	private String daccount;    //账号
	private String password;    //登录密码
	private String dname;    //姓名
	private String sex;    //性别
	private String photo;    //照片
	private String jobs;    //职称
	private String tel;    //联系方式
	private String shac;    //擅长领域
	private Object price;    //挂号费
	private Integer pid;    //科室
	private String pname;
	private String dmemo;    //医生简介
	private String addtime;    //添加时间

	public Integer getDid() {
		return did;
	}

	public void setDid(Integer did) {
		this.did = did;
	}

	public String getDaccount() {
		return daccount;
	}

	public void setDaccount(String daccount) {
		this.daccount = daccount;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getDname() {
		return dname;
	}

	public void setDname(String dname) {
		this.dname = dname;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public String getJobs() {
		return jobs;
	}

	public void setJobs(String jobs) {
		this.jobs = jobs;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getShac() {
		return shac;
	}

	public void setShac(String shac) {
		this.shac = shac;
	}

	public Object getPrice() {
		return price;
	}

	public void setPrice(Object price) {
		this.price = price;
	}

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public String getDmemo() {
		return dmemo;
	}

	public void setDmemo(String dmemo) {
		this.dmemo = dmemo;
	}

	public String getAddtime() {
		return addtime;
	}

	public void setAddtime(String addtime) {
		this.addtime = addtime;
	}

}

