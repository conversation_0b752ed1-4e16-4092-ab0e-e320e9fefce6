﻿<template>  
  <div class="body">  
    <div class="login-container">  
           <h2 style="text-align: center; font-size: 24px; margin-bottom: 24px;">医院挂号预约系统</h2>
            <form>  
               <div>  
                   <label for="uaccount">账号</label>  
                   <input type="text" id="uaccount" placeholder="请输入账号" required v-model="loginModel.username">  
               </div>  
               <div>  
                   <label for="password">密码</label>  
                   <input type="password" id="password" placeholder="请输入密码" required v-model="loginModel.password">  
               </div>  
                   <div>  
                   <label>身份</label>  
                   <div class="role-selection">  
                              <el-radio label="管理员" v-model="loginModel.radio">管理员</el-radio>
      <el-radio label="医生" v-model="loginModel.radio">医生</el-radio>
 
                   </div>  
               </div> 
    
               <button type="button" @click="login">登录</button>  
           </form>  
            
 
<!--           <p>还没有账户？ <a href="#">注册</a></p> --> 
       </div>  
       <div class="bubble" style="width: 60px; height: 60px; left: 20%; animation-delay: 0s;"></div>  
       <div class="bubble" style="width: 40px; height: 40px; left: 50%; animation-delay: 2s;"></div>  
       <div class="bubble" style="width: 80px; height: 80px; left: 80%; animation-delay: 4s;"></div>  
       <div class="bubble" style="width: 30px; height: 30px; left: 30%; animation-delay: 1s;"></div>  
       <div class="bubble" style="width: 50px; height: 50px; left: 70%; animation-delay: 3s;"></div>   
   </div>  
</template>
<script>
import request, { base } from "../../utils/http";
export default {
  name: "Login",
  data() {
    return {
      year: new Date().getFullYear(),
      loginModel: {
        username: "",
        password: "",
        radio: "管理员",
      },
      loginModel2: {},
     
    };
  },
  mounted() {},
  created() {
    
  },
  methods: {
    login() {
      let that = this;  

      if (that.loginModel.username == "") {
        that.$message({
          message: "请输入账号",
          type: "warning",
        });
        return;
      }
      if (that.loginModel.password == "") {
        that.$message({
          message: "请输入密码",
          type: "warning",
        });
        return;
      }   
      
      this.loading = true;
     var role = that.loginModel.radio; //获取身份
if (role == '管理员') {
      let url = base + "/admin/login";
      this.loginModel2.aname = this.loginModel.username;
      this.loginModel2.loginpassword = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.aname);
          sessionStorage.setItem("role", "管理员");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      }).catch((error) => {
        this.loading = false;
        // 模拟登录成功用于演示
        console.log('后端服务器未连接，模拟管理员登录');
        const mockUser = { aid: 1, aname: this.loginModel.username };
        sessionStorage.setItem("user", JSON.stringify(mockUser));
        sessionStorage.setItem("userLname", this.loginModel.username);
        sessionStorage.setItem("role", "管理员");
        this.$message.success('模拟登录成功（后端服务器未连接）');
        this.$router.push("/main");
      });
          }
else if (role == '医生') {
      let url = base + "/doctor/login";
      this.loginModel2.daccount = this.loginModel.username;
      this.loginModel2.password = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem("user", JSON.stringify(res.resdata));
          sessionStorage.setItem("userLname", res.resdata.daccount);
          sessionStorage.setItem("role", "医生");
          this.$router.push("/main");
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      }).catch((error) => {
        this.loading = false;
        // 模拟登录成功用于演示
        console.log('后端服务器未连接，模拟医生登录');
        const mockUser = { did: 1, daccount: this.loginModel.username };
        sessionStorage.setItem("user", JSON.stringify(mockUser));
        sessionStorage.setItem("userLname", this.loginModel.username);
        sessionStorage.setItem("role", "医生");
        this.$message.success('模拟登录成功（后端服务器未连接）');
        this.$router.push("/main");
      });
          }
    
     
    },
    
    
  },
};
</script>
   
<style scoped>  
@import "../assets/css/body.css";
/* 全局样式重置 */  
* {  
    margin: 0;  
    padding: 0;  
    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  
}  

body {  
    width: 100%;  
    height: 100%;  
    overflow: hidden; /* 确保没有滚动条 */  
}  

.body {  
    background: linear-gradient(to bottom right, #009688, #8BC34A);  
    display: flex;  
    align-items: center;  
    justify-content: center;  
    height: 100vh;  
}  

.login-container {  
    background: white;  
    border-radius: 8px;  
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  
    padding: 32px;  
    position: relative;  
    z-index: 10;  
    transition: transform 0.3s ease;  
    width: 400px;  
}  

.login-container:hover {  
    transform: scale(1.05);  
}  

.bubble {  
    position: absolute;  
    bottom: -100px;  
    border-radius: 50%;  
    background: rgba(255, 255, 255, 0.6);  
    animation: rise 10s infinite;  
}  

@keyframes rise {  
    0% {  
        transform: translateY(0);  
        opacity: 1;  
    }  
    100% {  
        transform: translateY(-600px);  
        opacity: 0;  
    }  
}  

input {  
    width: 100%;  
    padding: 10px;  
    margin: 10px 0;  
    border: 1px solid #ccc;  
    border-radius: 4px;  
}  

button {  
    width: 100%;  
    padding: 10px;  
    background-color: #8BC34A;  
    color: white;  
    border: none;  
    border-radius: 4px;  
    cursor: pointer;  
    transition: background-color 0.2s;  
}  

button:hover {  
    background-color: #81d522;  
}  

p {  
    text-align: center;  
    color: #666;  
}  

a {  
    color: #8BC34A;  
    text-decoration: none;  
}  

a:hover {  
    text-decoration: underline;  
}  

.role-selection {  
    display: flex;  
    align-items: center;  
    margin: 10px 0;  
}  

.role-selection input {  
    margin-right: 5px;  
}  

input[type="radio"] {  
    display: flex;  
    width: 50px;  
    align-items: center;  
    margin: 10px 0;  
}  
</style>

