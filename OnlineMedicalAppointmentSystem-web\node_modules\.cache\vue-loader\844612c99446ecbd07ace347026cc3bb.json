{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsEdit.vue?vue&type=template&id=686eca91", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsEdit.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgPGRpdiBzdHlsZT0id2lkdGg6IDEwMCU7bGluZS1oZWlnaHQ6IDMwcHg7dGV4dC1hbGlnbjogbGVmdDsiPg0KICAgICAgIDxlbC1mb3JtIDptb2RlbD0iZm9ybURhdGEiIGxhYmVsLXdpZHRoPSIyMCUiIHJlZj0iZm9ybURhdGFSZWYiIDpydWxlcz0iYWRkcnVsZXMiICBhbGlnbj0ibGVmdCI+DQo8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnp5HlrqTlkI3np7AiIHByb3A9InBuYW1lIj4NCjxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtRGF0YS5wbmFtZSIgcGxhY2Vob2xkZXI9IuenkeWupOWQjeensCIgIHN0eWxlPSJ3aWR0aDo1MCU7IiA+PC9lbC1pbnB1dD4NCjwvZWwtZm9ybS1pdGVtPg0KPGVsLWZvcm0taXRlbSBsYWJlbD0i56eR5a6k5LuL57uNIiBwcm9wPSJwbWVtbyI+DQo8V2FuZ0VkaXRvciAgcmVmPSJ3YW5nRWRpdG9yUmVmIiB2LW1vZGVsPSJmb3JtRGF0YS5wbWVtbyIgOmNvbmZpZz0iZWRpdG9yQ29uZmlnIiAgIDppc0NsZWFyPSJpc0NsZWFyIiBAY2hhbmdlPSJlZGl0b3JDaGFuZ2UiPjwvV2FuZ0VkaXRvcj4NCjwvZWwtZm9ybS1pdGVtPg0KPGVsLWZvcm0taXRlbT4NCjxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgc2l6ZT0ic21hbGwiIEBjbGljaz0ic2F2ZSIgOmxvYWRpbmc9ImJ0bkxvYWRpbmciIGljb249ImVsLWljb24tdXBsb2FkIj7mj5Ag5LqkPC9lbC1idXR0b24+DQo8ZWwtYnV0dG9uIHR5cGU9ImluZm8iIHNpemU9InNtYWxsIiBAY2xpY2s9ImdvQmFjayIgaWNvbj0iZWwtaWNvbi1iYWNrIj7ov5Qg5ZuePC9lbC1idXR0b24+DQo8L2VsLWZvcm0taXRlbT4NCjwvZWwtZm9ybT4NCg0KDQogICAgPC9kaXY+DQo="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsEdit.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAGL,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/parts/PartsEdit.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"科室名称\" prop=\"pname\">\r\n<el-input v-model=\"formData.pname\" placeholder=\"科室名称\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item label=\"科室介绍\" prop=\"pmemo\">\r\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.pmemo\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\nimport <PERSON>Editor from \"../../../components/WangEditor\";\nexport default {\n  name: 'PartsEdit',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        addrules: {\r\n          pname: [{ required: true, message: '请输入科室名称', trigger: 'blur' },\r\n],        },\r\n\n      };\n    },\n    created() {\r\n    this.id = this.$route.query.id;\r\n      this.getDatas();\r\n    },\r\n\r\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/parts/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.pmemo);\n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/parts/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                  this.$router.push({\n                    path: \"/PartsManage\",\n                  });\n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n        \n       // 返回\n        goBack() {\n          this.$router.push({\n            path: \"/PartsManage\",\n          });\n        },       \n              \n          \n           \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.pmemo = val;\n    },\r\n   \n      },\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}