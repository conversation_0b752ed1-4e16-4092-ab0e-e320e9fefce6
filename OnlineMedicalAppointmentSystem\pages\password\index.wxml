<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 修改密码 </view>
    </diy-navbar>
    <form bindsubmit="submitForm" bindreset="resetForm" class="flex diy-form diy-col-24 justify-center">
        <view class="diy-form-item diy-col-24">
            <view class="title"> 原密码： </view>
            <view class="input">
                <input class="flex1" name="pass1" password type="text" placeholder="请输入原密码" />
            </view>
        </view>
        <view class="diy-form-item diy-col-24">
            <view class="title"> 新密码： </view>
            <view class="input">
                <input class="flex1" name="pass2" password type="text" placeholder="请输入新密码" />
            </view>
        </view>
        <view class="diy-form-item diy-col-24">
            <view class="title"> 确认密码： </view>
            <view class="input">
                <input class="flex1" name="pass3" password type="text" placeholder="请输入确认密码" />
            </view>
        </view>
        <view class="flex diy-col-24 justify-center">
            <button style="" form-type="submit" class="diy-btn green flex1 margin-xs">确认修改</button>

            <button style="" form-type="reset" class="diy-btn green flex1 margin-xs">重置</button>
        </view>

    </form>
    <view class="clearfix"></view>
</view>

