package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/plans")
public class PlansController {

	@Resource
	private PlansService plansService;

	// 排班列表
	@RequestMapping(value = "/list")
	@CrossOrigin
	public Response<List<Plans>> list(@RequestBody Plans plans, @RequestParam Integer currentPage,
			HttpServletRequest req) throws Exception {
		int pageSize = Integer.parseInt(req.getParameter("pageSize")); // 每页显示记录数
		int offset = (currentPage - 1) * pageSize; // 当前页开始记录
		int counts = 0; // 总记录数
		PageBean page = new PageBean(offset, pageSize); // 分页对象
		// 查询记录总数
		counts = plansService.getCount(plans);
		// 获取当前页记录
		List<Plans> plansList = plansService.queryPlansList(plans, page);

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM
				: counts / PageBean.PAGE_IETM + 1; // 总页数
		return Response.success(plansList, counts, page_count);
	}

	// 添加排班
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Plans plans, HttpServletRequest req) throws Exception {
		try {
			plansService.insertPlans(plans); // 添加

		} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}

	// 删除排班
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			plansService.deletePlans(id); // 删除
		} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}

	// 修改排班
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Plans plans, HttpServletRequest req) throws Exception {
		try {
			plansService.updatePlans(plans); // 修改
		} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}

	// 返回排班详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Plans plans = plansService.queryPlansById(id); // 根据ID查询
			return Response.success(plans);
		} catch (Exception e) {
			return Response.error();
		}

	}

}
