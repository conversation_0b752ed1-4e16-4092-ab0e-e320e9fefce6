package com.service;
import java.util.List;

import com.model.Parts;
import com.util.PageBean;

public interface PartsService{
	
	//查询多条记录
	public List<Parts> queryPartsList(Parts parts,PageBean page) throws Exception;
 
	//添加
	public int insertParts(Parts parts) throws Exception ;
	
	//根据ID删除
	public int deleteParts(int id) throws Exception ;
	
	//更新
	public int updateParts(Parts parts) throws Exception ;
	
	//根据ID查询单条数据
	public Parts queryPartsById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Parts parts);

}

