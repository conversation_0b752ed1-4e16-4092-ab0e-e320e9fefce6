<view class="container" style="padding-bottom:120px;">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 医生详情 </view>
    </diy-navbar>
    <view class="flex diy-col-24">
        <view class="padding bg-white diy-col-24" wx:for="{{msgs1}}" wx:key="k">

            <view class="text-center ">
                <image src="{{url}}{{item.photo}}" mode="aspectFit"></image>
            </view>

            <view class=" text-center  text-lg " style="line-height: 40px;">
                {{item.dname}}
            </view>
            <view class=" text-left " style="line-height: 25px;">
                性别： {{item.sex}}
            </view>
            <view class=" text-left " style="line-height: 25px;">
                职称： {{item.jobs}}
            </view>
            <view class=" text-left " style="line-height: 25px;">
                联系方式： {{item.tel}}
            </view>
            <view class=" text-left " style="line-height: 25px;">
                擅长领域： {{item.shac}}
            </view>
            <view class=" text-left " style="line-height: 25px;">
                挂号费用：
                <text class="text-red text-price text-lg">{{item.price}}</text>
            </view>



        </view>
    </view>
    <view class="flex diy-col-24 flex-direction-column">
        <view class="diy-tabs text-center solid-bottom justify-start scale-title small-border">
            <view class="diy-tab-item  {{index==tabsIndex?'bg-green radius-xs':''}}" wx:for="{{tabsDatas}}"
                wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}" catchtap="changeTabs">
                <text wx:if="{{item.icon}}" class="{{item.icon}}"></text> {{item.text}}
            </view>
        </view>
        <view class="">
            <view wx:if="{{tabsIndex==0}}" class="flex-sub">

                <view style="line-height: 23px;" class="padding-sm">
                    <rich-text nodes="{{msgs1[0].dmemo}}"></rich-text>
                </view>
            </view>
            <view wx:if="{{tabsIndex==1}}" class="flex-sub">
                <view class="schedule-container">
                    <!-- 日期表头 -->
                    <view class="schedule-header">
                        <view class="time-column">时间段</view>
                        <view class="date-column" wx:for="{{date}}" wx:key="index">
                            {{item}}
                            <view class="week-day">{{week[index]}}</view>
                        </view>
                    </view>

                    <!-- 排班表格 -->
                    <view class="schedule-body">
                        <view class="schedule-row" wx:for="{{scheduleTable}}" wx:key="index" wx:for-item="row"
                            wx:for-index="timeIndex">
                            <!-- 时间段 -->
                            <view class="time-cell">{{row.timeSlot}}</view>

                            <!-- 每天的排班情况 -->
                            <view class="schedule-cell {{cell.hasSchedule ? 'has-schedule' : ''}}"
                                wx:for="{{row.cells}}" wx:key="cellIndex" wx:for-item="cell" wx:for-index="cellIndex"
                                bindtap="yuyue" data-id="{{cellIndex}}" data-time-index="{{timeIndex}}">
                                <block wx:if="{{cell.hasSchedule}}">
                                    <view class="status">可预约</view>
                                    <view class="remaining">剩余:{{cell.availableSlots}}</view>
                                </block>
                                <block wx:else>
                                    <view class="status unavailable">未排班</view>
                                </block>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 患者评价选项卡 -->
            <view wx:if="{{tabsIndex==2}}" class="flex-sub">
                <view class="evaluations-container">
                    <view wx:if="{{evaluations.length === 0}}" class="no-evaluations">
                        暂无评价
                    </view>
                    <view wx:else class="evaluation-list">
                        <view class="evaluation-item" wx:for="{{evaluations}}" wx:key="eid">
                            <view class="evaluation-header">
                                <view class="user-avatar">
                                    <image src="{{url}}{{item.by1}}" mode="aspectFill"></image>
                                </view>
                                <view class="user-info">
                                    <view class="user-name">{{item.lname}}</view>
                                    <view class="star-rating">
                                        <text wx:for="{{[1,2,3,4,5]}}" class="diy-icon-starfill" wx:key="*this"
                                            wx:for-item="i" style="color: {{item.score >= i ? 'red' : '#eee'}};">
                                        </text>
                                    </view>
                                    <view class="evaluation-time">{{item.etime}}</view>
                                </view>
                            </view>
                            <view class="evaluation-content">
                                {{item.comment}}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="footer-buttons">
        <button class="footer-btn {{isFavorite ? 'btn-favorited' : 'btn-favorite'}}" bindtap="favoriteDoctor">
            <text class="cuIcon-favor{{isFavorite ? 'fill' : ''}}"></text>
            {{isFavorite ? '已收藏' : '收藏'}}
        </button>
        <button class="footer-btn btn-chat" bindtap="chatWithDoctor">
            <text class="cuIcon-comment"></text>
            在线问诊
        </button>
    </view>

    <view class="clearfix"></view>
</view>