package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.MailremindertemplateMapper;
import com.model.Mailremindertemplate;
import com.util.PageBean;
@Service
public class MailremindertemplateServiceImpl implements MailremindertemplateService{
        
	@Autowired
	private MailremindertemplateMapper mailremindertemplateMapper;

	//查询多条记录
	public List<Mailremindertemplate> queryMailremindertemplateList(Mailremindertemplate mailremindertemplate,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(mailremindertemplate, page);
		
		List<Mailremindertemplate> getMailremindertemplate = mailremindertemplateMapper.query(map);
		
		return getMailremindertemplate;
	}
	
	//得到记录总数
	@Override
	public int getCount(Mailremindertemplate mailremindertemplate) {
		Map<String, Object> map = getQueryMap(mailremindertemplate, null);
		int count = mailremindertemplateMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Mailremindertemplate mailremindertemplate,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(mailremindertemplate!=null){
			map.put("mid", mailremindertemplate.getMid());
			map.put("days", mailremindertemplate.getDays());
			map.put("content", mailremindertemplate.getContent());
			map.put("sort", mailremindertemplate.getSort());
			map.put("condition", mailremindertemplate.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertMailremindertemplate(Mailremindertemplate mailremindertemplate) throws Exception {
		return mailremindertemplateMapper.insertMailremindertemplate(mailremindertemplate);
	}

	//根据ID删除
	public int deleteMailremindertemplate(int id) throws Exception {
		return mailremindertemplateMapper.deleteMailremindertemplate(id);
	}

	//更新
	public int updateMailremindertemplate(Mailremindertemplate mailremindertemplate) throws Exception {
		return mailremindertemplateMapper.updateMailremindertemplate(mailremindertemplate);
	}
	
	//根据ID得到对应的记录
	public Mailremindertemplate queryMailremindertemplateById(int id) throws Exception {
		Mailremindertemplate po =  mailremindertemplateMapper.queryMailremindertemplateById(id);
		return po;
	}
}

