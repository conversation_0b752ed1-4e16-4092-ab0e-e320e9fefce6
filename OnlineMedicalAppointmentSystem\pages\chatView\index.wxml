<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 与{{globalOption.name}}的对话 </view>
    </diy-navbar>

    <view class="container">
        <view class="cu-chat" id="chat">
            <block wx:for="{{msgs1}}" wx:key="key">
                <!-- 用户发送的消息 -->
                <view class="cu-item self" wx:if="{{item.flag == 1}}">
                    <view class="main">
                        <view class="content bg-green shadow">
                            <text>{{item.content}}</text>
                        </view>
                    </view>
                    <view class="cu-avatar radius" style="background-image:url({{url}}{{item.by1}})"></view>
                    <view class="date">{{item.sendtime}}</view>
                </view>

                <!-- 医生发送的消息 -->
                <view class="cu-item" wx:else>
                    <view class="cu-avatar radius" style="background-image:url({{url}}{{item.by2}})"></view>
                    <view class="main">
                        <view class="content shadow">
                            <text>{{item.content}}</text>
                        </view>
                    </view>
                    <view class="date">{{item.sendtime}}</view>
                </view>
            </block>
        </view>

        <view class="cu-bar foot input" style="bottom:0px">
            <input class="solid-bottom" name="cmemo" value="{{cmemo}}" placeholder="请输入聊天内容" bindinput="getInputValue"
                maxlength="300" cursor-spacing="10"></input>
            <button class="cu-btn bg-gradual-green shadow" type="button" catch:tap="liuyan">发送</button>
        </view>

        <view class="clearfix"></view>
    </view>
</view>