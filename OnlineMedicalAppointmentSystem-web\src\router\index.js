﻿import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false
    }
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: "/home",
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true, title: '首页'
        }

      },

      {
        path: '/plansAdd',
        name: 'PlansAdd',
        component: () => import('../views/admin/plans/PlansAdd'),
        meta: { requiresAuth: true, title: '排班添加' }
      },
      {
        path: '/plansEdit',
        name: 'PlansEdit',
        component: () => import('../views/admin/plans/PlansEdit'),
        meta: { requiresAuth: true, title: '排班修改' }
      },
      {
        path: '/plansManage',
        name: 'PlansManage',
        component: () => import('../views/admin/plans/PlansManage'),
        meta: { requiresAuth: true, title: '排班管理' }
      },
      {
        path: '/chatinfoManage',
        name: 'ChatinfoManage',
        component: () => import('../views/admin/chatinfo/ChatinfoManage'),
        meta: { requiresAuth: true, title: '聊天管理' }
      },
      {
        path: '/doctorAdd',
        name: 'DoctorAdd',
        component: () => import('../views/admin/doctor/DoctorAdd'),
        meta: { requiresAuth: true, title: '医生添加' }
      },
      {
        path: '/doctorEdit',
        name: 'DoctorEdit',
        component: () => import('../views/admin/doctor/DoctorEdit'),
        meta: { requiresAuth: true, title: '医生修改' }
      },
      {
        path: '/doctorManage',
        name: 'DoctorManage',
        component: () => import('../views/admin/doctor/DoctorManage'),
        meta: { requiresAuth: true, title: '医生管理' }
      },
      {
        path: '/doctorDetail',
        name: 'DoctorDetail',
        component: () => import('../views/admin/doctor/DoctorDetail'),
        meta: { requiresAuth: true, title: '医生详情' }
      },
      {
        path: '/doctorInfo',
        name: 'DoctorInfo',
        component: () => import('../views/admin/doctor/DoctorInfo'),
        meta: { requiresAuth: true, title: '修改个人信息' }
      },
      {
        path: '/partsAdd',
        name: 'PartsAdd',
        component: () => import('../views/admin/parts/PartsAdd'),
        meta: { requiresAuth: true, title: '科室添加' }
      },
      {
        path: '/partsEdit',
        name: 'PartsEdit',
        component: () => import('../views/admin/parts/PartsEdit'),
        meta: { requiresAuth: true, title: '科室修改' }
      },
      {
        path: '/partsManage',
        name: 'PartsManage',
        component: () => import('../views/admin/parts/PartsManage'),
        meta: { requiresAuth: true, title: '科室管理' }
      },
      {
        path: '/partsDetail',
        name: 'PartsDetail',
        component: () => import('../views/admin/parts/PartsDetail'),
        meta: { requiresAuth: true, title: '科室详情' }
      },
      {
        path: '/usersEdit',
        name: 'UsersEdit',
        component: () => import('../views/admin/users/UsersEdit'),
        meta: { requiresAuth: true, title: '患者修改' }
      },
      {
        path: '/usersManage',
        name: 'UsersManage',
        component: () => import('../views/admin/users/UsersManage'),
        meta: { requiresAuth: true, title: '患者管理' }
      },
      {
        path: '/usersDetail',
        name: 'UsersDetail',
        component: () => import('../views/admin/users/UsersDetail'),
        meta: { requiresAuth: true, title: '患者详情' }
      },
      {
        path: '/reserveEdit',
        name: 'ReserveEdit',
        component: () => import('../views/admin/reserve/ReserveEdit'),
        meta: { requiresAuth: true, title: '预约挂号修改' }
      },
      {
        path: '/reserveManage',
        name: 'ReserveManage',
        component: () => import('../views/admin/reserve/ReserveManage'),
        meta: { requiresAuth: true, title: '预约挂号管理' }
      },
      {
        path: '/reserveDetail',
        name: 'ReserveDetail',
        component: () => import('../views/admin/reserve/ReserveDetail'),
        meta: { requiresAuth: true, title: '预约挂号详情' }
      },
      {
        path: '/mailremindertemplateEdit',
        name: 'MailremindertemplateEdit',
        component: () => import('../views/admin/mailremindertemplate/MailremindertemplateEdit'),
        meta: { requiresAuth: true, title: '邮件模板设置' }
      },

      {
        path: '/password',
        name: 'Password',
        component: () => import('../views/admin/system/Password'),
        meta: {
          requireAuth: true, title: '修改密码'
        }
      },
    ]
  },


]



const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})



router.beforeEach((to, from, next) => {
  if (to.path == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + "  to.meta.requireAuth");

  if (to.meta.requireAuth) {
    if (!currentUser && to.path != '/login') {
      next({ path: '/' });
    } else {
      next();
    }
  } else {

    next();
  }
})

export default router


