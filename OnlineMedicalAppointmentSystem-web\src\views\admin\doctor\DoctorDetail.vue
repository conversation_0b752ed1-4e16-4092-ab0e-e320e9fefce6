﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="医生ID">
{{formData.did}}</el-form-item>
<el-form-item label="账号">
{{formData.daccount}}</el-form-item>
<el-form-item label="登录密码">
{{formData.password}}</el-form-item>
<el-form-item label="姓名">
{{formData.dname}}</el-form-item>
<el-form-item label="性别">
{{formData.sex}}</el-form-item>
<el-form-item label="照片" prop="photo">
<img :src="'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' +formData.photo" style="width: 150px;height: 150px" />
</el-form-item>
<el-form-item label="职称">
{{formData.jobs}}</el-form-item>
<el-form-item label="联系方式">
{{formData.tel}}</el-form-item>
<el-form-item label="擅长领域">
{{formData.shac}}</el-form-item>
<el-form-item label="挂号费">
{{formData.price}}</el-form-item>
<el-form-item label="科室">
{{formData.pname}}</el-form-item>
<el-form-item label="医生简介">
{{formData.dmemo}}</el-form-item>
<el-form-item label="添加时间">
{{formData.addtime}}</el-form-item>
<el-form-item>
<el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
        
        import request, { base } from "../../../../utils/http";
        export default {
            name: 'DoctorDetail',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据         
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/doctor/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>
<style scoped>
</style>
 

