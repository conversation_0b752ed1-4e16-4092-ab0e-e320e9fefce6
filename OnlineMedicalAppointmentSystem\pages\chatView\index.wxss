.container {
  padding-bottom: 100rpx;
}

.cu-chat {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.cu-item {
  margin-bottom: 30rpx;
}

.cu-avatar {
  width: 80rpx;
  height: 80rpx;
  background-size: cover;
  background-position: center;
}



.self .content {
  background-color: #07c160;
  color: #fff;
}

.date {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.cu-bar.foot {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.cu-bar.foot input {
  flex: 1;
  padding: 20rpx;
  background-color: #f1f1f1;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.cu-btn {
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}
