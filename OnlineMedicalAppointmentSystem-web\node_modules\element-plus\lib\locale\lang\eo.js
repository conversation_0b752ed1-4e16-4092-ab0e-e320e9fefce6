'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var eo = {
    name: 'eo',
    el: {
        colorpicker: {
            confirm: '<PERSON>',
            clear: '<PERSON><PERSON>ni<PERSON>',
        },
        datepicker: {
            now: 'Nun',
            today: 'Ho<PERSON><PERSON>',
            cancel: '<PERSON>uli<PERSON>',
            clear: '<PERSON><PERSON><PERSON><PERSON>',
            confirm: '<PERSON>',
            selectDate: '<PERSON><PERSON><PERSON> daton',
            selectTime: '<PERSON><PERSON><PERSON> horon',
            startDate: 'Komen<PERSON> Da<PERSON>',
            startTime: '<PERSON><PERSON><PERSON> Horo',
            endDate: 'Fina Dato',
            endTime: '<PERSON>a Horo',
            prevYear: 'Antaŭa Jaro',
            nextYear: 'Sekva <PERSON>',
            prevMonth: 'Antaŭa Monato',
            nextMonth: 'Sek<PERSON>',
            year: 'J<PERSON>',
            month1: 'Januaro',
            month2: 'Februaro',
            month3: 'Marto',
            month4: 'Aprilo',
            month5: 'Majo',
            month6: 'Jun<PERSON>',
            month7: '<PERSON>',
            month8: 'A<PERSON>gus<PERSON>',
            month9: 'Septembro',
            month10: 'Ok<PERSON><PERSON>',
            month11: 'Novembro',
            month12: 'Decembro',
            week: 'Se<PERSON><PERSON><PERSON>',
            weeks: {
                sun: 'Dim',
                mon: 'Lun',
                tue: 'Mar',
                wed: 'Mer',
                thu: 'Ĵaŭ',
                fri: 'Ven',
                sat: 'Sab',
            },
            months: {
                jan: 'Jan',
                feb: 'Feb',
                mar: 'Mar',
                apr: 'Apr',
                may: 'Maj',
                jun: 'Jun',
                jul: 'Jul',
                aug: 'Aŭg',
                sep: 'Sep',
                oct: 'Okt',
                nov: 'Nov',
                dec: 'Dec',
            },
        },
        select: {
            loading: 'Ŝarĝante',
            noMatch: 'Neniuj kongruaj datumoj',
            noData: 'Neniuj datumoj',
            placeholder: 'Bonvolu elekti',
        },
        cascader: {
            noMatch: 'Neniuj kongruaj datumoj',
            loading: 'Ŝarĝante',
            placeholder: 'Bonvolu elekti',
            noData: 'Neniuj datumoj',
        },
        pagination: {
            goto: 'Iru al',
            pagesize: '/ paĝo',
            total: 'Entute {total}',
            pageClassifier: '',
        },
        messagebox: {
            title: 'Mesaĝo',
            confirm: 'Bone',
            cancel: 'Nuligi',
            error: 'Nevalida Enigo!',
        },
        upload: {
            deleteTip: 'Premu "Delete" por forigi',
            delete: 'Forigi',
            preview: 'Antaŭrigardi',
            continue: 'Daŭrigi',
        },
        table: {
            emptyText: 'Neniuj datumoj',
            confirmFilter: 'Konfirmi',
            resetFilter: 'Restarigi',
            clearFilter: 'Ĉiuj',
            sumText: 'Sumo',
        },
        tree: {
            emptyText: 'Neniuj datumoj',
        },
        transfer: {
            noMatch: 'Neniuj kongruaj datumoj',
            noData: 'Neniuj datumoj',
            titles: ['Listo 1', 'Listo 2'],
            filterPlaceholder: 'Enigu ŝlosilvorton',
            noCheckedFormat: '{total} elementoj',
            hasCheckedFormat: '{checked}/{total} elektitaj',
        },
        image: {
            error: 'MALSUKCESIS',
        },
        pageHeader: {
            title: 'Reen',
        },
        popconfirm: {
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
        },
    },
};

exports.default = eo;
