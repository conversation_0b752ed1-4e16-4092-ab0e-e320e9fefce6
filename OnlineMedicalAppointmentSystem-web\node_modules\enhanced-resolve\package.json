{"name": "enhanced-resolve", "version": "4.5.0", "author": "<PERSON> @sokra", "description": "Offers a async require.resolve function. It's highly configurable.", "files": ["lib", "LICENSE"], "dependencies": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^5.9.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "husky": "^1.2.0", "istanbul": "^0.4.1", "lint-staged": "^8.1.0", "mocha": "^2.3.4", "prettier": "^1.15.2", "should": "^8.0.2"}, "engines": {"node": ">=6.9.0"}, "main": "lib/node.js", "homepage": "http://github.com/webpack/enhanced-resolve", "scripts": {"lint": "eslint lib test", "pretty": "prettier --loglevel warn --write \"{lib,test}/**/*.{js,json}\"", "pretest": "yarn lint", "test": "mocha --full-trace --check-leaks", "precover": "yarn lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --cache"]}, "repository": {"type": "git", "url": "git://github.com/webpack/enhanced-resolve.git"}}