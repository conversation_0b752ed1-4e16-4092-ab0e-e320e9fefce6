{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsDetail.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BhcnRzRGV0YWlsJywKICBjb21wb25lbnRzOiB7fSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWQ6ICcnLAogICAgICBmb3JtRGF0YToge30gLy/ooajljZXmlbDmja4gICAgICAgICAKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOyAvL+iOt+WPluWPguaVsAogICAgdGhpcy5nZXREYXRhcygpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3BhcnRzL2dldD9pZD0iICsgdGhpcy5pZDsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5yZXNkYXRhKSk7CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDov5Tlm54KICAgIGJhY2soKSB7CiAgICAgIC8v6L+U5Zue5LiK5LiA6aG1CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\parts\\PartsDetail.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"科室ID\">\r\n{{formData.pid}}</el-form-item>\r\n<el-form-item label=\"科室名称\">\r\n{{formData.pname}}</el-form-item>\r\n<el-form-item label=\"科室介绍\" prop=\"pmemo\">\r\n<div v-html=\"formData.pmemo\"></div>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n        \n        import request, { base } from \"../../../../utils/http\";\n        export default {\n            name: 'PartsDetail',\n            components: {\n            },\n            data() {\n                return {\n                    id: '',\n                    formData: {}, //表单数据         \n        \n                };\n            },\n            created() {\n                this.id = this.$route.query.id; //获取参数\n                this.getDatas();\n            },\n        \n        \n            methods: {\n        \n                //获取列表数据\n                getDatas() {\n                    let para = {\n                    };\n                    this.listLoading = true;\n                    let url = base + \"/parts/get?id=\" + this.id;\n                    request.post(url, para).then((res) => {\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\n                        this.listLoading = false;\n                    });\n                },\n        \n                // 返回\n                back() {\n                    //返回上一页\n                    this.$router.go(-1);\n                },\n        \n            },\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": "AAqBQ,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACXC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAElB,CAAC;EACL,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACnB,CAAC;EAGDC,OAAO,EAAE;IAEL;IACAD,QAAQA,CAAA,EAAG;MACP,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,gBAAe,GAAI,IAAI,CAACI,EAAE;MAC3CL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;EAEJ;AACJ", "ignoreList": []}]}