package com.service;
import java.util.List;

import com.model.Doctor;
import com.util.PageBean;

public interface DoctorService{
	
	//查询多条记录
	public List<Doctor> queryDoctorList(Doctor doctor,PageBean page) throws Exception;
 
	//添加
	public int insertDoctor(Doctor doctor) throws Exception ;
	
	//根据ID删除
	public int deleteDoctor(int id) throws Exception ;
	
	//更新
	public int updateDoctor(Doctor doctor) throws Exception ;
	
	//根据ID查询单条数据
	public Doctor queryDoctorById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Doctor doctor);

}

