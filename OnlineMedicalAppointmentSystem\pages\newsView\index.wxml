<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 通知公告详情 </view>
    </diy-navbar>
    <view class="flex diy-col-24">
        <view class="padding bg-white diy-col-24" wx:for="{{msgs1}}" wx:key="k">
            <view class=" text-center  text-lg " style="line-height: 40px;">
                {{item.title}}
            </view>
            <view class="text-center ">
                发布时间：<text class="text-red">{{item.naddtime}}</text>
            </view>
            <view style="line-height: 23px;">
                <rich-text nodes="{{item.nmemo}}"></rich-text>
            </view>
        </view>
    </view>


    <view class="clearfix"></view>
</view>