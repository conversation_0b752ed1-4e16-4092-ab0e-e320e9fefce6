import { mount as _mount } from '@vue/test-utils'

export const mount = (opt: any) =>
  _mount<any>(opt, {
    attachTo: 'body',
  })

export function getTestData() {
  return [
    {
      id: 1,
      name: 'Toy Story',
      release: '1995-11-22',
      director: '<PERSON>',
      runtime: 80,
    },
    {
      id: 2,
      name: "A Bug's Life",
      release: '1998-11-25',
      director: '<PERSON>',
      runtime: 95,
    },
    {
      id: 3,
      name: 'Toy Story 2',
      release: '1999-11-24',
      director: '<PERSON>',
      runtime: 92,
    },
    {
      id: 4,
      name: 'Monsters, Inc.',
      release: '2001-11-2',
      director: '<PERSON>',
      runtime: 92,
    },
    {
      id: 5,
      name: '<PERSON> Nemo',
      release: '2003-5-30',
      director: '<PERSON>',
      runtime: 100,
    },
  ]
}

// https://stackoverflow.com/a/59864054/2198656
describe('test util does not need jest testing', () => {
  it.skip('too lazy to change jest match config', () => {
    // nothing!
  })
})
