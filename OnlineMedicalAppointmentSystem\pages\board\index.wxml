<view class="container">
    <diy-navbar bgColor="green" isBack="{{false}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 咨询 </view>
    </diy-navbar>
    <view class="cu-chat  " style="padding-bottom:60px">
        <block wx:for="{{msgs1}}" wx:key="k">

            <view class="cu-item">
                <view class="cu-avatar radius" style="background-image:url({{url}}{{item.by1}});"></view>
                <view class="main">
                    <view class="content bg-green shadow">
                        <text>{{item.bdetail}}</text>
                    </view>
                    <text class="diy-icon-backdelete margin-left-sm text-red " style="font-size:28px;"
                        data-id="{{item.bid}}" catch:tap="dele"></text>
                </view>
                <view class="date ">{{item.addtime}}</view>
            </view>


            <view class="cu-item self" wx:if="{{item.answer!=null}}">
                <view class="main">
                    <view class="action">
                        <text class="cuIcon-locationfill text-orange text-xxl"></text>
                    </view>
                    <view class="content bg-red shadow">
                        {{item.answer}}
                    </view>
                </view>
                <view class="cu-avatar radius bg-red light" style="font-size:12px">管理员</view>
                <view class="date">{{item.users}}</view>
            </view>

        </block>
    </view>



    <form bindsubmit="formSubmit1">
        <view class="cu-bar foot ">

            <input class="solid-bottom padding-sm" name="bdetail" value="{{bdetail}}" maxlength="500"
                style="width:80%;border:1px;height: 40px;" placeholder="请输入咨询内容"></input>

            <button class="cu-btn bg-green shadow" form-type="submit">发送</button>
        </view>
    </form>


    <view class="clearfix"></view>
</view>