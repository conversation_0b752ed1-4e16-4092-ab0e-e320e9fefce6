.grid{
    line-height:25px;
    padding:5px;
    border-bottom:1px dashed gray;
}
.grid{
    line-height:25px;
    padding:5px;
    border-bottom:1px dashed gray;
}
.rows{
    line-height:25px;
    padding:6px;
    border-bottom:1px dashed gray;
}


.grid-clz {
    margin-left: 5px;
    box-shadow: 0px 1px 3px rgba(31, 31, 31, 0.16);
    overflow: hidden;
    width: calc(100% - 5px - 5px) !important;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.grid-item-clz {
    font-size: 12px;
}
.grid-icon-clz {
    width: 50px;
    height: 50px;
    font-size: calc(50px - 4px) !important;
}
.container10021 {
    padding-left: 0px;
    padding-right: 0px;

    font-size: 12px;
}
.container10021 {
}

/* 推荐医生样式 */
.doctor-section {
    padding: 20rpx;
    margin-top: 20rpx;
    background: #fff;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    padding: 20rpx 0;
    border-bottom: 1px solid #eee;
}

.doctor-list {
    margin-top: 20rpx;
}

.doctor-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;
}

.doctor-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    margin-right: 20rpx;
}

.doctor-info {
    flex: 1;
}

.doctor-name {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
}

.doctor-title {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 6rpx;
}

.doctor-dept {
    font-size: 26rpx;
    color: #999;
}

.doctor-action {
    text-align: right;
}

.price {
    font-size: 28rpx;
    color: #ff4d4f;
    margin-bottom: 10rpx;
    display: block;
}

.book-btn {
    font-size: 24rpx;
    color: #fff;
    background: #39b54a;
    padding: 6rpx 20rpx;
    border-radius: 24rpx;
    display: inline-block;
}
