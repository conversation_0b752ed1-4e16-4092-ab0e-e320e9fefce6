

/* ==================
        初始化
 ==================== */

view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
	box-sizing: border-box;
}

.round {
	border-radius: 5000rpx;
}


/* ==================
          图片
 ==================== */

image {
	max-width: 100%;
	display: inline-block;
	position: relative;
	z-index: 0;
}

image.loading::before {
	content: "";
	background-color: #f5f5f5;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -2;
}

image.loading::after {
	content: "\e7f1";
	font-family: "cuIcon";
	position: absolute;
	top: 0;
	left: 0;
	width: 32rpx;
	height: 32rpx;
	line-height: 32rpx;
	right: 0;
	bottom: 0;
	z-index: -1;
	font-size: 32rpx;
	margin: auto;
	color: #ccc;
	-webkit-animation: cuIcon-spin 2s infinite linear;
	animation: cuIcon-spin 2s infinite linear;
	display: block;
}

.response {
	width: 100%;
}

/* ==================
         开关
 ==================== */

switch,
checkbox,
radio {
	position: relative;
}

switch::after,
switch::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: var(--white) !important;
	top: 0%;
	left: 0rpx;
	font-size: 26rpx;
	line-height: 26px;
	width: 50%;
	text-align: center;
	pointer-events: none;
	transform: scale(0, 0);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
	bottom: 0;
	height: 26px;
	margin: auto;
}

switch::before {
	content: "\e646";
	right: 0;
	transform: scale(1, 1);
	left: auto;
}

switch[checked]::after,
switch.checked::after {
	transform: scale(1, 1);
}

switch[checked]::before,
switch.checked::before {
	transform: scale(0, 0);
}

switch[checked]::before {
	transform: scale(0, 0);
}

radio::before,
checkbox::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: var(--white) !important;
	top: 50%;
	margin-top: -8px;
	right: 5px;
	font-size: 32rpx;
	line-height: 16px;
	pointer-events: none;
	transform: scale(1, 1);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
}

radio .wx-radio-input,
checkbox .wx-checkbox-input {
	margin: 0;
	width: 24px;
	height: 24px;
}

checkbox.round .wx-checkbox-input {
	border-radius: 100rpx;
}

switch .wx-switch-input {
	border: none;
	padding: 0 24px;
	width: 48px;
	height: 26px;
	margin: 0;
	border-radius: 100rpx;
}

switch .wx-switch-input:not([class*="bg-"]) {
	background: var(--grey) !important;
}

switch .wx-switch-input::after {
	margin: auto;
	width: 26px;
	height: 26px;
	border-radius: 100rpx;
	left: 0rpx;
	top: 0rpx;
	bottom: 0rpx;
	position: absolute;
	transform: scale(0.9, 0.9);
	transition: all 0.1s ease-in-out 0s;
}

switch .wx-switch-input.wx-switch-input-checked::after {
	margin: auto;
	left: 22px;
	box-shadow: none;
	transform: scale(0.9, 0.9);
}

radio-group {
	display: inline-block;
}



switch.radius .wx-switch-input::after,
switch.radius .wx-switch-input,
switch.radius .wx-switch-input::before {
	border-radius: 10rpx;
}

switch .wx-switch-input::before,
radio.radio::before,
checkbox .wx-checkbox-input::before,
radio .wx-radio-input::before,
radio.radio::before {
	display: none;
}

radio.radio[checked]::after {
	content: "";
	background-color: transparent;
	display: block;
	position: absolute;
	width: 8px;
	height: 8px;
	z-index: 999;
	top: 0rpx;
	left: 0rpx;
	right: 0;
	bottom: 0;
	margin: auto;
	border-radius: 200rpx;
	border: 8px solid var(--white) !important;
}

.switch-sex::after {
	content: "\e71c";
}

.switch-sex::before {
	content: "\e71a";
}

.switch-sex .wx-switch-input {
	background: var(--red) !important;
	border-color: var(--red) !important;
}

.switch-sex[checked] .wx-switch-input {
	background: var(--blue) !important;
	border-color: var(--blue) !important;
}

switch.red[checked] .wx-switch-input,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input {
	border-color: var(--red) !important;
}

switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input {
	border-color: var(--orange) !important;
}

switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input {
	border-color: var(--yellow) !important;
}

switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input {
	border-color: var(--olive) !important;
}

switch.green[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input {
	border-color: var(--green) !important;
}

switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input {
	border-color: var(--cyan) !important;
}

switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input {
	border-color: var(--blue) !important;
}

switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input {
	border-color: var(--purple) !important;
}

switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input {
	border-color: var(--mauve) !important;
}

switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input {
	border-color: var(--pink) !important;
}

switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input {
	border-color: var(--brown) !important;
}

switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input {
	border-color: var(--grey) !important;
}

switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input {
	border-color: var(--grey) !important;
}

switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input {
	border-color: var(--black) !important;
}

switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input {
	border-color: var(--white) !important;
}

switch.red[checked] .wx-switch-input.wx-switch-input-checked,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input {
	background-color: var(--red) !important;
	color: var(--white) !important;
}

switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input {
	background-color: var(--orange) !important;
	color: var(--white) !important;
}

switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input {
	background-color: var(--yellow) !important;
	color: var(--black) !important;
}

switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input {
	background-color: var(--olive) !important;
	color: var(--white) !important;
}

switch.green[checked] .wx-switch-input,
switch[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input,
radio[checked] .wx-radio-input {
	background-color: var(--green) !important;
	color: var(--white) !important;
}

switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input {
	background-color: var(--cyan) !important;
	color: var(--white) !important;
}

switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input {
	background-color: var(--blue) !important;
	color: var(--white) !important;
}

switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input {
	background-color: var(--purple) !important;
	color: var(--white) !important;
}

switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input {
	background-color: var(--mauve) !important;
	color: var(--white) !important;
}

switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input {
	background-color: var(--pink) !important;
	color: var(--white) !important;
}

switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input {
	background-color: var(--brown) !important;
	color: var(--white) !important;
}

switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input {
	background-color: var(--grey) !important;
	color: var(--white) !important;
}

switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input {
	background-color: #f0f0f0 !important;
	color: var(--black) !important;
}

switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input {
	background-color: var(--black) !important;
	color: var(--white) !important;
}

switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input {
	background-color: var(--white) !important;
	color: var(--black) !important;
}

/* ==================
          边框
 ==================== */

/* -- 实线 -- */

.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
	position: relative;
}

.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
}

.solid::after {
	border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-top::after {
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-right::after {
	border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-bottom::after {
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-left::after {
	border-left: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solids::after {
	border: 8rpx solid #eee;
}

.solids-top::after {
	border-top: 8rpx solid #eee;
}

.solids-right::after {
	border-right: 8rpx solid #eee;
}

.solids-bottom::after {
	border-bottom: 8rpx solid #eee;
}

.solids-left::after {
	border-left: 8rpx solid #eee;
}

/* -- 虚线 -- */

.dashed::after {
	border: 1rpx dashed #ddd;
}

.dashed-top::after {
	border-top: 1rpx dashed #ddd;
}

.dashed-right::after {
	border-right: 1rpx dashed #ddd;
}

.dashed-bottom::after {
	border-bottom: 1rpx dashed #ddd;
}

.dashed-left::after {
	border-left: 1rpx dashed #ddd;
}

/* -- 阴影 -- */

.shadow[class*='white'] {
	--ShadowSize: 0 1rpx 6rpx;
}

.shadow-lg {
	--ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}

.shadow-warp {
	position: relative;
	box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.shadow-warp:before,
.shadow-warp:after {
	position: absolute;
	content: "";
	top: 20rpx;
	bottom: 30rpx;
	left: 20rpx;
	width: 50%;
	box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
	transform: rotate(-3deg);
	z-index: -1;
}

.shadow-warp:after {
	right: 20rpx;
	left: auto;
	transform: rotate(3deg);
}

.shadow-blur {
	position: relative;
}

.shadow-blur::before {
	content: "";
	display: block;
	background: inherit;
	filter: blur(10rpx);
	position: absolute;
	width: 100%;
	height: 100%;
	top: 10rpx;
	left: 10rpx;
	z-index: -1;
	opacity: 0.4;
	transform-origin: 0 0;
	border-radius: inherit;
	transform: scale(1, 1);
}

/* ==================
          按钮
 ==================== */

.cu-btn {
	position: relative;
	border: 0rpx;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0 30rpx;
	font-size: 28rpx;
	height: 64rpx;
	line-height: 1;
	text-align: center;
	text-decoration: none;
	overflow: visible;
	margin-left: initial;
	transform: translate(0rpx, 0rpx);
	margin-right: initial;
}

.cu-btn::after {
	display: none;
}

.cu-btn:not([class*="bg-"]) {
	background-color: #f0f0f0;
}

.cu-btn[class*="line"] {
	background-color: transparent;
}

.cu-btn[class*="line"]::after {
	content: " ";
	display: block;
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: 12rpx;
	z-index: 1;
	pointer-events: none;
}

.cu-btn.round[class*="line"]::after {
	border-radius: 1000rpx;
}

.cu-btn[class*="lines"]::after {
	border: 6rpx solid currentColor;
}

.cu-btn[class*="bg-"]::after {
	display: none;
}

.cu-btn.sm {
	padding: 0 20rpx;
	font-size: 20rpx;
	height: 48rpx;
}

.cu-btn.lg {
	padding: 0 40rpx;
	font-size: 32rpx;
	height: 80rpx;
}

.cu-btn.icon.sm {
	width: 48rpx;
	height: 48rpx;
}

.cu-btn.icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 500rpx;
	padding: 0;
}

button.icon.lg {
	width: 80rpx;
	height: 80rpx;
}

.cu-btn.shadow-blur::before {
	top: 4rpx;
	left: 4rpx;
	filter: blur(6rpx);
	opacity: 0.6;
}

.cu-btn.button-hover {
	transform: translate(1rpx, 1rpx);
}

.block {
	display: block;
}

.cu-btn.block {
	display: flex;
}

.cu-btn[disabled] {
	opacity: 0.6;
	color: var(--white);
}

/* ==================
          徽章
 ==================== */

.cu-tag {
	font-size: 24rpx;
	vertical-align: middle;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0rpx 16rpx;
	height: 48rpx;
	font-family: Helvetica Neue, Helvetica, sans-serif;
	white-space: nowrap;
}

.cu-tag:not([class*="bg"]):not([class*="line"]) {
	background-color: var(--ghostWhite);
}

.cu-tag[class*="line-"]::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
}

.cu-tag.radius[class*="line"]::after {
	border-radius: 12rpx;
}

.cu-tag.round[class*="line"]::after {
	border-radius: 1000rpx;
}

.cu-tag[class*="line-"]::after {
	border-radius: 0;
}

.cu-tag+.cu-tag {
	margin-left: 10rpx;
}

.cu-tag.sm {
	font-size: 20rpx;
	padding: 0rpx 12rpx;
	height: 32rpx;
}

.cu-capsule {
	display: inline-flex;
	vertical-align: middle;
}

.cu-capsule+.cu-capsule {
	margin-left: 10rpx;
}

.cu-capsule .cu-tag {
	margin: 0;
}

.cu-capsule .cu-tag[class*="line-"]:last-child::after {
	border-left: 0rpx solid transparent;
}

.cu-capsule .cu-tag[class*="line-"]:first-child::after {
	border-right: 0rpx solid transparent;
}

.cu-capsule.radius .cu-tag:first-child {
	border-top-left-radius: 6rpx;
	border-bottom-left-radius: 6rpx;
}

.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*="line-"] {
	border-top-right-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
}

.cu-capsule.round .cu-tag:first-child {
	border-top-left-radius: 200rpx;
	border-bottom-left-radius: 200rpx;
	text-indent: 4rpx;
}

.cu-capsule.round .cu-tag:last-child::after,
.cu-capsule.round .cu-tag:last-child {
	border-top-right-radius: 200rpx;
	border-bottom-right-radius: 200rpx;
	text-indent: -4rpx;
}

.cu-tag.badge {
	border-radius: 200rpx;
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	font-size: 20rpx;
	padding: 0rpx 10rpx;
	height: 28rpx;
	color: var(--white);
}

.cu-tag.badge:not([class*="bg-"]) {
	background-color: #dd514c;
}

.cu-tag:empty:not([class*="cuIcon-"]) {
	padding: 0rpx;
	width: 16rpx;
	height: 16rpx;
	top: -4rpx;
	right: -4rpx;
}

.cu-tag[class*="cuIcon-"] {
	width: 32rpx;
	height: 32rpx;
	top: -4rpx;
	right: -4rpx;
}

/* ==================
          头像
 ==================== */

.cu-avatar {
	font-variant: small-caps;
	margin: 0;
	padding: 0;
	display: inline-flex;
	text-align: center;
	justify-content: center;
	align-items: center;
	background-color: #ccc;
	color: var(--white);
	white-space: nowrap;
	position: relative;
	width: 64rpx;
	height: 64rpx;
	background-size: cover;
	background-position: center;
	vertical-align: middle;
	font-size: 1.5em;
}

.cu-avatar.sm {
	width: 48rpx;
	height: 48rpx;
	font-size: 1em;
}

.cu-avatar.lg {
	width: 96rpx;
	height: 96rpx;
	font-size: 2em;
}

.cu-avatar.xl {
	width: 128rpx;
	height: 128rpx;
	font-size: 2.5em;
}

.cu-avatar .avatar-text {
	font-size: 0.4em;
}

.cu-avatar-group {
	direction: rtl;
	unicode-bidi: bidi-override;
	padding: 0 10rpx 0 40rpx;
	display: inline-block;
}

.cu-avatar-group .cu-avatar {
	margin-left: -30rpx;
	border: 4rpx solid var(--ghostWhite);
	vertical-align: middle;
}

.cu-avatar-group .cu-avatar.sm {
	margin-left: -20rpx;
	border: 1rpx solid var(--ghostWhite);
}

/* ==================
         进度条
 ==================== */

.cu-progress {
	overflow: hidden;
	height: 28rpx;
	background-color: #ebeef5;
	display: inline-flex;
	align-items: center;
	width: 100%;
}

.cu-progress+view,
.cu-progress+text {
	line-height: 1;
}

.cu-progress.xs {
	height: 10rpx;
}

.cu-progress.sm {
	height: 20rpx;
}

.cu-progress view {
	width: 0;
	height: 100%;
	align-items: center;
	display: flex;
	justify-items: flex-end;
	justify-content: space-around;
	font-size: 20rpx;
	color: var(--white);
	transition: width 0.6s ease;
}

.cu-progress text {
	align-items: center;
	display: flex;
	font-size: 20rpx;
	color: var(--black);
	text-indent: 10rpx;
}

.cu-progress.text-progress {
	padding-right: 60rpx;
}

.cu-progress.striped view {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-size: 72rpx 72rpx;
}

.cu-progress.active view {
	animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
	from {
		background-position: 72rpx 0;
	}

	to {
		background-position: 0 0;
	}
}

/* ==================
          加载
 ==================== */

.cu-load {
	display: block;
	line-height: 3em;
	text-align: center;
}

.cu-load::before {
	font-family: "cuIcon";
	display: inline-block;
	margin-right: 6rpx;
}

.cu-load.loading::before {
	content: "\e67a";
	animation: cuIcon-spin 2s infinite linear;
}

.cu-load.loading::after {
	content: "加载中...";
}

.cu-load.over::before {
	content: "\e64a";
}

.cu-load.over::after {
	content: "没有更多了";
}

.cu-load.erro::before {
	content: "\e658";
}

.cu-load.erro::after {
	content: "加载失败";
}

.cu-load.load-icon::before {
	font-size: 32rpx;
}

.cu-load.load-icon::after {
	display: none;
}

.cu-load.load-icon.over {
	display: none;
}

.cu-load.load-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 140rpx;
	left: 0;
	margin: auto;
	width: 260rpx;
	height: 260rpx;
	background-color: var(--white);
	border-radius: 10rpx;
	box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	font-size: 28rpx;
	z-index: 9999;
	line-height: 2.4em;
}

.cu-load.load-modal [class*="cuIcon-"] {
	font-size: 60rpx;
}

.cu-load.load-modal image {
	width: 70rpx;
	height: 70rpx;
}

.cu-load.load-modal::after {
	content: "";
	position: absolute;
	background-color: var(--white);
	border-radius: 50%;
	width: 200rpx;
	height: 200rpx;
	font-size: 10px;
	border-top: 6rpx solid rgba(0, 0, 0, 0.05);
	border-right: 6rpx solid rgba(0, 0, 0, 0.05);
	border-bottom: 6rpx solid rgba(0, 0, 0, 0.05);
	border-left: 6rpx solid var(--orange);
	animation: cuIcon-spin 1s infinite linear;
	z-index: -1;
}

.load-progress {
	pointer-events: none;
	top: 0;
	position: fixed;
	width: 100%;
	left: 0;
	z-index: 2000;
}

.load-progress.hide {
	display: none;
}

.load-progress .load-progress-bar {
	position: relative;
	width: 100%;
	height: 4rpx;
	overflow: hidden;
	transition: all 200ms ease 0s;
}

.load-progress .load-progress-spinner {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	z-index: 2000;
	display: block;
}

.load-progress .load-progress-spinner::after {
	content: "";
	display: block;
	width: 24rpx;
	height: 24rpx;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border: solid 4rpx transparent;
	border-top-color: inherit;
	border-left-color: inherit;
	border-radius: 50%;
	-webkit-animation: load-progress-spinner 0.4s linear infinite;
	animation: load-progress-spinner 0.4s linear infinite;
}

@-webkit-keyframes load-progress-spinner {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes load-progress-spinner {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

/* ==================
          列表
 ==================== */
.grayscale {
	filter: grayscale(1);
}

.cu-list+.cu-list {
	margin-top: 30rpx
}

.cu-list>.cu-item {
	transition: all .6s ease-in-out 0s;
	transform: translateX(0rpx)
}

.cu-list>.cu-item.move-cur {
	transform: translateX(-260rpx)
}

.cu-list>.cu-item .move {
	position: absolute;
	right: 0;
	display: flex;
	width: 260rpx;
	height: 100%;
	transform: translateX(100%)
}

.cu-list>.cu-item .move view {
	display: flex;
	flex: 1;
	justify-content: center;
	align-items: center
}

.cu-list.menu-avatar {
	overflow: hidden;
}

.cu-list.menu-avatar>.cu-item {
	position: relative;
	display: flex;
	padding-right: 10rpx;
	height: 140rpx;
	background-color: var(--white);
	justify-content: flex-end;
	align-items: center
}

.cu-list.menu-avatar>.cu-item>.cu-avatar {
	position: absolute;
	left: 30rpx
}

.cu-list.menu-avatar>.cu-item .flex .text-cut {
	max-width: 510rpx
}

.cu-list.menu-avatar>.cu-item .content {
	position: absolute;
	left: 146rpx;
	width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx);
	line-height: 1.6em;
}

.cu-list.menu-avatar>.cu-item .content.flex-sub {
	width: calc(100% - 96rpx - 60rpx - 20rpx);
}

.cu-list.menu-avatar>.cu-item .content>view:first-child {
	font-size: 30rpx;
	display: flex;
	align-items: center
}

.cu-list.menu-avatar>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10rpx;
	height: 28rpx;
	font-size: 16rpx;
	line-height: 32rpx
}

.cu-list.menu-avatar>.cu-item .action {
	width: 100rpx;
	text-align: center
}

.cu-list.menu-avatar>.cu-item .action view+view {
	margin-top: 10rpx
}

.cu-list.menu-avatar.comment>.cu-item .content {
	position: relative;
	left: 0;
	width: auto;
	flex: 1;
}

.cu-list.menu-avatar.comment>.cu-item {
	padding: 30rpx 30rpx 30rpx 120rpx;
	height: auto
}

.cu-list.menu-avatar.comment .cu-avatar {
	align-self: flex-start
}

.cu-list.menu>.cu-item {
	position: relative;
	display: flex;
	padding: 0 30rpx;
	min-height: 100rpx;
	background-color: var(--white);
	justify-content: space-between;
	align-items: center
}

.cu-list.menu>.cu-item:last-child:after {
	border: none
}

.cu-list.menu>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-bottom: 1rpx solid #ddd;
	border-radius: inherit;
	content: " ";
	transform: scale(.5);
	transform-origin: 0 0;
	pointer-events: none
}

.cu-list.menu>.cu-item.grayscale {
	background-color: #f5f5f5
}

.cu-list.menu>.cu-item.cur {
	background-color: #fcf7e9
}

.cu-list.menu>.cu-item.arrow {
	padding-right: 90rpx
}

.cu-list.menu>.cu-item.arrow:before {
	position: absolute;
	top: 0;
	right: 30rpx;
	bottom: 0;
	display: block;
	margin: auto;
	width: 30rpx;
	height: 30rpx;
	color: var(--grey);
	content: "\e6a3";
	text-align: center;
	font-size: 34rpx;
	font-family: "cuIcon";
	line-height: 30rpx
}

.cu-list.menu>.cu-item button.content {
	padding: 0;
	background-color: transparent;
	justify-content: flex-start
}

.cu-list.menu>.cu-item button.content:after {
	display: none
}

.cu-list.menu>.cu-item .cu-avatar-group .cu-avatar {
	border-color: var(--white)
}

.cu-list.menu>.cu-item .content>view:first-child {
	display: flex;
	align-items: center
}

.cu-list.menu>.cu-item .content>text[class*=cuIcon] {
	display: inline-block;
	margin-right: 10rpx;
	width: 1.6em;
	text-align: center
}

.cu-list.menu>.cu-item .content>image {
	display: inline-block;
	margin-right: 10rpx;
	width: 1.6em;
	height: 1.6em;
	vertical-align: middle
}

.cu-list.menu>.cu-item .content {
	font-size: 30rpx;
	line-height: 1.6em;
	flex: 1
}

.cu-list.menu>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10rpx;
	height: 28rpx;
	font-size: 16rpx;
	line-height: 32rpx
}

.cu-list.menu>.cu-item .action .cu-tag:empty {
	right: 10rpx
}

.cu-list.menu {
	display: block;
	overflow: hidden
}

.cu-list.menu.sm-border>.cu-item:after {
	left: 30rpx;
	width: calc(200% - 120rpx)
}

.cu-list.grid>.cu-item {
	position: relative;
	display: flex;
	padding: 20rpx 0 30rpx;
	transition-duration: 0s;
	flex-direction: column
}

.cu-list.grid>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-right: 1px solid rgba(0, 0, 0, .1);
	border-bottom: 1px solid rgba(0, 0, 0, .1);
	border-radius: inherit;
	content: " ";
	transform: scale(.5);
	transform-origin: 0 0;
	pointer-events: none
}

.cu-list.grid>.cu-item text {
	display: block;
	margin-top: 10rpx;
	color: #888;
	font-size: 26rpx;
	line-height: 40rpx
}

.cu-list.grid>.cu-item [class*=cuIcon] {
	position: relative;
	display: block;
	margin-top: 20rpx;
	width: 100%;
	font-size: 48rpx
}

.cu-list.grid>.cu-item .cu-tag {
	right: auto;
	left: 50%;
	margin-left: 20rpx
}

.cu-list.grid {
	background-color: var(--white);
	text-align: center
}

.cu-list.grid.no-border>.cu-item {
	padding-top: 10rpx;
	padding-bottom: 20rpx
}

.cu-list.grid.no-border>.cu-item:after {
	border: none
}

.cu-list.grid.no-border {
	padding: 20rpx 10rpx
}

.cu-list.grid.col-3>.cu-item:nth-child(3n):after,
.cu-list.grid.col-4>.cu-item:nth-child(4n):after,
.cu-list.grid.col-5>.cu-item:nth-child(5n):after {
	border-right-width: 0
}

.cu-list.card-menu {
	overflow: hidden;
	margin-right: 30rpx;
	margin-left: 30rpx;
	border-radius: 20rpx
}


/* ==================
          操作条
 ==================== */

.cu-bar {
	display: flex;
	position: relative;
	align-items: center;
	min-height: 100rpx;
	justify-content: space-between;
}

.cu-bar .action {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: center;
	max-width: 100%;
}

.cu-bar .action.border-title {
	position: relative;
	top: -10rpx;
}

.cu-bar .action.border-title text[class*="bg-"]:last-child {
	position: absolute;
	bottom: -0.5rem;
	min-width: 2rem;
	height: 6rpx;
	left: 0;
}

.cu-bar .action.sub-title {
	position: relative;
	top: -0.2rem;
}

.cu-bar .action.sub-title text {
	position: relative;
	z-index: 1;
}

.cu-bar .action.sub-title text[class*="bg-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.2rem;
	border-radius: 6rpx;
	width: 100%;
	height: 0.6rem;
	left: 0.6rem;
	opacity: 0.3;
	z-index: 0;
}

.cu-bar .action.sub-title text[class*="text-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.7rem;
	left: 0.5rem;
	opacity: 0.2;
	z-index: 0;
	text-align: right;
	font-weight: 900;
	font-size: 36rpx;
}

.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
	left: 0;
	right: 0;
	margin: auto;
	text-align: center;
}

.cu-bar .action:first-child {
	margin-left: 30rpx;
	font-size: 30rpx;
}

.cu-bar .action text.text-cut {
	text-align: left;
	width: 100%;
}

.cu-bar .cu-avatar:first-child {
	margin-left: 20rpx;
}

.cu-bar .action:first-child>text[class*="cuIcon-"] {
	margin-left: -0.3em;
	margin-right: 0.3em;
}

.cu-bar .action:last-child {
	margin-right: 30rpx;
}

.cu-bar .action>text[class*="cuIcon-"],
.cu-bar .action>view[class*="cuIcon-"] {
	font-size: 36rpx;
}

.cu-bar .action>text[class*="cuIcon-"]+text[class*="cuIcon-"] {
	margin-left: 0.5em;
}

.cu-bar .content {
	position: absolute;
	text-align: center;
	width: calc(100% - 340rpx);
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	margin: auto;
	height: 60rpx;
	font-size: 32rpx;
	line-height: 60rpx;
	cursor: none;
	pointer-events: none;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.cu-bar.ios .content {
	bottom: 7px;
	height: 30px;
	font-size: 32rpx;
	line-height: 30px;
}

.cu-bar.btn-group {
	justify-content: space-around;
}

.cu-bar.btn-group button {
	padding: 20rpx 32rpx;
}

.cu-bar.btn-group button {
	flex: 1;
	margin: 0 20rpx;
	max-width: 50%;
}

.cu-bar .search-form {
	background-color: #f5f5f5;
	line-height: 64rpx;
	height: 64rpx;
	font-size: 24rpx;
	color: var(--black);
	flex: 1;
	display: flex;
	align-items: center;
	margin: 0 30rpx;
}

.cu-bar .search-form+.action {
	margin-right: 30rpx;
}

.cu-bar .search-form input {
	flex: 1;
	padding-right: 30rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 26rpx;
	background-color: transparent;
}

.cu-bar .search-form [class*="cuIcon-"] {
	margin: 0 0.5em 0 0.8em;
}

.cu-bar .search-form [class*="cuIcon-"]::before {
	top: 0rpx;
}

.cu-bar.fixed,
.nav.fixed {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.foot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar {
	padding: 0;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.cu-tabbar-height {
	min-height: 100rpx;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.cu-bar.tabbar.shadow {
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar .action {
	font-size: 22rpx;
	position: relative;
	flex: 1;
	text-align: center;
	padding: 0;
	display: block;
	height: auto;
	line-height: 1;
	margin: 0;
	overflow: initial;
}

.cu-bar.tabbar.shop .action {
	width: 140rpx;
	flex: initial;
}

.cu-bar.tabbar .action.add-action {
	position: relative;
	z-index: 2;
	padding-top: 50rpx;
	background-color: inherit;
}

.cu-bar.tabbar .action.add-action [class*="cuIcon-"] {
	position: absolute;
	width: 70rpx;
	z-index: 2;
	height: 70rpx;
	border-radius: 50%;
	line-height: 70rpx;
	font-size: 50rpx;
	top: -35rpx;
	left: 0;
	right: 0;
	margin: auto;
	padding: 0;
}

.cu-bar.tabbar .action.add-action::after {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 100rpx;
	top: -50rpx;
	left: 0;
	right: 0;
	margin: auto;
	box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
	border-radius: 50rpx;
	background-color: inherit;
	z-index: 0;
}

.cu-bar.tabbar .action.add-action::before {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 30rpx;
	bottom: 30rpx;
	left: 0;
	right: 0;
	margin: auto;
	background-color: inherit;
	z-index: 1;
}

.cu-bar.tabbar .btn-group {
	flex: 1;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 0 10rpx;
}

.cu-bar.tabbar button.action::after {
	border: 0;
}

.cu-bar.tabbar .action [class*="cuIcon-"] {
	width: 100rpx;
	position: relative;
	display: block;
	height: auto;
	margin: 0 auto 10rpx;
	text-align: center;
	font-size: 40rpx;
}

.cu-bar.tabbar .action .cuIcon-cu-image {
	margin: 0 auto;
}

.cu-bar.tabbar .action .cuIcon-cu-image image {
	width: 50rpx;
	height: 50rpx;
	display: inline-block;
}

.cu-bar.tabbar .submit {
	align-items: center;
	display: flex;
	justify-content: center;
	text-align: center;
	position: relative;
	flex: 2;
	align-self: stretch;
}

.cu-bar.tabbar .submit:last-child {
	flex: 2.6;
}

.cu-bar.tabbar .submit+.submit {
	flex: 2;
}

.cu-bar.tabbar.border .action::before {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	transform: scale(0.5);
	transform-origin: 0 0;
	border-right: 1rpx solid rgba(0, 0, 0, 0.1);
	z-index: 3;
}

.cu-bar.tabbar.border .action:last-child:before {
	display: none;
}

.cu-bar.input {
	padding-right: 20rpx;
	background-color: var(--white);
}

.cu-bar.input input {
	overflow: initial;
	line-height: 64rpx;
	height: 64rpx;
	min-height: 64rpx;
	flex: 1;
	font-size: 30rpx;
	margin: 0 20rpx;
}

.cu-bar.input .action {
	margin-left: 20rpx;
}

.cu-bar.input .action [class*="cuIcon-"] {
	font-size: 48rpx;
}

.cu-bar.input input+.action {
	margin-right: 20rpx;
	margin-left: 0rpx;
}

.cu-bar.input .action:first-child [class*="cuIcon-"] {
	margin-left: 0rpx;
}

.cu-custom {
	display: block;
	position: relative;
}

.cu-custom .cu-bar .content {
	width: calc(100% - 440rpx);
}


.cu-custom .cu-bar .content image {
	height: 60rpx;
	width: 240rpx;
}

.cu-custom .cu-bar {
	min-height: 0px;
	padding-right: 220rpx;
	box-shadow: 0rpx 0rpx 0rpx;
	z-index: 9999;
}

.cu-custom .cu-bar .border-custom {
	position: relative;
	background: rgba(0, 0, 0, 0.15);
	border-radius: 1000rpx;
	height: 30px;
}

.cu-custom .cu-bar .border-custom::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	border: 1rpx solid var(--white);
	opacity: 0.5;
}

.cu-custom .cu-bar .border-custom::before {
	content: " ";
	width: 1rpx;
	height: 110%;
	position: absolute;
	top: 22.5%;
	left: 0;
	right: 0;
	margin: auto;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	opacity: 0.6;
	background-color: var(--white);
}

.cu-custom .cu-bar .border-custom text {
	display: block;
	flex: 1;
	margin: auto !important;
	text-align: center;
	font-size: 34rpx;
}

/* ==================
         导航栏
 ==================== */

.nav {
	white-space: nowrap;
}

::-webkit-scrollbar {
	display: none;
}

.nav .cu-item {
	height: 90rpx;
	display: inline-block;
	line-height: 90rpx;
	margin: 0 10rpx;
	padding: 0 20rpx;
}

.nav .cu-item.cur {
	border-bottom: 4rpx solid;
}

/* ==================
         时间轴
 ==================== */

.cu-timeline {
	display: block;
	background-color: var(--white);
}

.cu-timeline .cu-time {
	width: 120rpx;
	text-align: center;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #888;
	display: block;
}

.cu-timeline>.cu-item {
	padding: 30rpx 30rpx 30rpx 120rpx;
	position: relative;
	display: block;
	z-index: 0;
}

.cu-timeline>.cu-item:not([class*="text-"]) {
	color: #ccc;
}

.cu-timeline>.cu-item::after {
	content: "";
	display: block;
	position: absolute;
	width: 1rpx;
	background-color: #ddd;
	left: 60rpx;
	height: 100%;
	top: 0;
	z-index: 8;
}

.cu-timeline>.cu-item::before {
	font-family: "cuIcon";
	display: block;
	position: absolute;
	top: 36rpx;
	z-index: 9;
	background-color: var(--white);
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}

.cu-timeline>.cu-item:not([class*="cuIcon-"])::before {
	content: "\e763";
}

.cu-timeline>.cu-item[class*="cuIcon-"]::before {
	background-color: var(--white);
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}

.cu-timeline>.cu-item>.content {
	padding: 30rpx;
	border-radius: 6rpx;
	display: block;
	line-height: 1.6;
}

.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
	background-color: var(--ghostWhite);
	color: var(--black);
}

.cu-timeline>.cu-item>.content+.content {
	margin-top: 20rpx;
}

/* ==================
         聊天
 ==================== */

.cu-chat {
	display: flex;
	flex-direction: column;
}

.cu-chat .cu-item {
	display: flex;
	padding: 30rpx 30rpx 70rpx;
	position: relative;
}

.cu-chat .cu-item>.cu-avatar {
	width: 80rpx;
	height: 80rpx;
}

.cu-chat .cu-item>.main {
	max-width: calc(100% - 260rpx);
	margin: 0 40rpx;
	display: flex;
	align-items: center;
}

.cu-chat .cu-item>image {
	height: 320rpx;
}

.cu-chat .cu-item>.main .content {
	padding: 20rpx;
	border-radius: 6rpx;
	display: inline-flex;
	max-width: 100%;
	align-items: center;
	font-size: 30rpx;
	position: relative;
	min-height: 80rpx;
	line-height: 40rpx;
	text-align: left;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"]) {
	background-color: var(--white);
	color: var(--black);
}

.cu-chat .cu-item .date {
	position: absolute;
	font-size: 24rpx;
	color: var(--grey);
	width: calc(100% - 320rpx);
	bottom: 20rpx;
	left: 160rpx;
}

.cu-chat .cu-item .action {
	padding: 0 30rpx;
	display: flex;
	align-items: center;
}

.cu-chat .cu-item>.main .content::after {
	content: "";
	top: 27rpx;
	transform: rotate(45deg);
	position: absolute;
	z-index: 100;
	display: inline-block;
	overflow: hidden;
	width: 24rpx;
	height: 24rpx;
	left: -12rpx;
	right: initial;
	background-color: inherit;
}

.cu-chat .cu-item.self>.main .content::after {
	left: auto;
	right: -12rpx;
}

.cu-chat .cu-item>.main .content::before {
	content: "";
	top: 30rpx;
	transform: rotate(45deg);
	position: absolute;
	z-index: -1;
	display: inline-block;
	overflow: hidden;
	width: 24rpx;
	height: 24rpx;
	left: -12rpx;
	right: initial;
	background-color: inherit;
	filter: blur(5rpx);
	opacity: 0.3;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"])::before {
	background-color: var(--black);
	opacity: 0.1;
}

.cu-chat .cu-item.self>.main .content::before {
	left: auto;
	right: -12rpx;
}

.cu-chat .cu-item.self {
	justify-content: flex-end;
	text-align: right;
}

.cu-chat .cu-info {
	display: inline-block;
	margin: 20rpx auto;
	font-size: 24rpx;
	padding: 8rpx 12rpx;
	background-color: rgba(0, 0, 0, 0.2);
	border-radius: 6rpx;
	color: var(--white);
	max-width: 400rpx;
	line-height: 1.4;
}

/* ==================
         卡片
 ==================== */

.cu-card {
	display: block;
	overflow: hidden;
}

.cu-card>.cu-item {
	display: block;
	background-color: var(--white);
	overflow: hidden;
	border-radius: 10rpx;
	margin: 30rpx;
}

.cu-card>.cu-item.shadow-blur {
	overflow: initial;
}

.cu-card.no-card>.cu-item {
	margin: 0rpx;
	border-radius: 0rpx;
}

.cu-card .grid.grid-square {
	margin-bottom: -20rpx;
}

.cu-card.case .image {
	position: relative;
}

.cu-card.case .image image {
	width: 100%;
}

.cu-card.case .image .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
}

.cu-card.case .image .cu-bar {
	position: absolute;
	bottom: 0;
	width: 100%;
	background-color: transparent;
	padding: 0rpx 30rpx;
}

.cu-card.case.no-card .image {
	margin: 30rpx 30rpx 0;
	overflow: hidden;
	border-radius: 10rpx;
}

.cu-card.dynamic {
	display: block;
}

.cu-card.dynamic>.cu-item {
	display: block;
	background-color: var(--white);
	overflow: hidden;
}

.cu-card.dynamic>.cu-item>.text-content {
	padding: 0 30rpx 0;
	max-height: 6.4em;
	overflow: hidden;
	font-size: 30rpx;
	margin-bottom: 20rpx;
}

.cu-card.dynamic>.cu-item .square-img {
	width: 100%;
	height: 200rpx;
	border-radius: 6rpx;
}

.cu-card.dynamic>.cu-item .only-img {
	width: 100%;
	height: 320rpx;
	border-radius: 6rpx;
}

.cu-card.article {
	display: block;
}

.cu-card.article>.cu-item {
	padding-bottom: 30rpx;
}

.cu-card.article>.cu-item .title {
	font-size: 30rpx;
	font-weight: 900;
	color: var(--black);
	line-height: 100rpx;
	padding: 0 30rpx;
}

.cu-card.article>.cu-item .content {
	display: flex;
	padding: 0 30rpx;
}

.cu-card.article>.cu-item .content>image {
	width: 240rpx;
	height: 6.4em;
	margin-right: 20rpx;
	border-radius: 6rpx;
}

.cu-card.article>.cu-item .content .desc {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.cu-card.article>.cu-item .content .text-content {
	font-size: 28rpx;
	color: #888;
	height: 4.8em;
	overflow: hidden;
}

/* ==================
         表单
 ==================== */

.cu-form-group {
	background-color: var(--white);
	padding: 1rpx 30rpx;
	display: flex;
	align-items: center;
	min-height: 100rpx;
	justify-content: space-between;
}

.cu-form-group+.cu-form-group {
	border-top: 1rpx solid #eee;
}

.cu-form-group .title {
	text-align: justify;
	padding-right: 30rpx;
	font-size: 30rpx;
	position: relative;
	height: 60rpx;
	line-height: 60rpx;
}

.cu-form-group input {
	flex: 1;
	font-size: 30rpx;
	color: #555;
	padding-right: 20rpx;
}

.cu-form-group>text[class*="cuIcon-"] {
	font-size: 36rpx;
	padding: 0;
	box-sizing: border-box;
}

.cu-form-group textarea {
	margin: 32rpx 0 30rpx;
	height: 4.6em;
	width: 100%;
	line-height: 1.2em;
	flex: 1;
	font-size: 28rpx;
	padding: 0;
}

.cu-form-group.align-start .title {
	height: 1em;
	margin-top: 32rpx;
	line-height: 1em;
}

.cu-form-group picker {
	flex: 1;
	padding-right: 40rpx;
	overflow: hidden;
	position: relative;
}

.cu-form-group picker .picker {
	line-height: 100rpx;
	font-size: 28rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	width: 100%;
	text-align: right;
}

.cu-form-group picker::after {
	font-family: "cuIcon";
	display: block;
	content: "\e6a3";
	position: absolute;
	font-size: 34rpx;
	color: var(--grey);
	line-height: 100rpx;
	width: 60rpx;
	text-align: center;
	top: 0;
	bottom: 0;
	right: -20rpx;
	margin: auto;
}

.cu-form-group textarea[disabled],
.cu-form-group textarea[disabled] .placeholder {
	color: transparent;
}

/* ==================
         模态窗口
 ==================== */

.cu-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1110;
	opacity: 0;
	outline: 0;
	text-align: center;
	-ms-transform: scale(1.185);
	transform: scale(1.185);
	backface-visibility: hidden;
	perspective: 2000rpx;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
}

.cu-modal::before {
	content: "\200B";
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}

.cu-modal.show {
	opacity: 1;
	transition-duration: 0.3s;
	-ms-transform: scale(1);
	transform: scale(1);
	overflow-x: hidden;
	overflow-y: auto;
	pointer-events: auto;
}

.cu-dialog {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin-left: auto;
	margin-right: auto;
	width: 680rpx;
	max-width: 100%;
	background-color: #f8f8f8;
	border-radius: 10rpx;
	overflow: hidden;
}

.cu-modal.bottom-modal::before {
	vertical-align: bottom;
}

.cu-modal.bottom-modal .cu-dialog {
	width: 100%;
	border-radius: 0;
}

.cu-modal.bottom-modal {
	margin-bottom: -1000rpx;
}

.cu-modal.bottom-modal.show {
	margin-bottom: 0;
}

.cu-modal.drawer-modal {
	transform: scale(1);
	display: flex;
}

.cu-modal.drawer-modal .cu-dialog {
	height: 100%;
	min-width: 200rpx;
	border-radius: 0;
	margin: initial;
	transition-duration: 0.3s;
}

.cu-modal.drawer-modal.justify-start .cu-dialog {
	transform: translateX(-100%);
}

.cu-modal.drawer-modal.justify-end .cu-dialog {
	transform: translateX(100%);
}

.cu-modal.drawer-modal.show .cu-dialog {
	transform: translateX(0%);
}
.cu-modal .cu-dialog>.cu-bar:first-child .action{
  min-width: 100rpx;
  margin-right: 0;
  min-height: 100rpx;
}
/* ==================
         轮播
 ==================== */
swiper .a-swiper-dot {
	display: inline-block;
	width: 16rpx;
	height: 16rpx;
	background: rgba(0, 0, 0, .3);
	border-radius: 50%;
	vertical-align: middle;
}

swiper[class*="-dot"] .wx-swiper-dots {
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: center;
}

swiper.square-dot .wx-swiper-dot {
	background-color: var(--white);
	opacity: 0.4;
	width: 10rpx;
	height: 10rpx;
	border-radius: 20rpx;
	margin: 0 8rpx !important;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active {
	opacity: 1;
	width: 30rpx;
}

swiper.round-dot .wx-swiper-dot {
	width: 10rpx;
	height: 10rpx;
	position: relative;
	margin: 4rpx 8rpx !important;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after {
	content: "";
	position: absolute;
	width: 10rpx;
	height: 10rpx;
	top: 0rpx;
	left: 0rpx;
	right: 0;
	bottom: 0;
	margin: auto;
	background-color: var(--white);
	border-radius: 20rpx;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active {
	width: 18rpx;
	height: 18rpx;
}

.screen-swiper {
	min-height: 375rpx;
}

.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
	width: 100%;
	display: block;
	height: 100%;
	margin: 0;
	pointer-events: none;
}

.card-swiper {
	height: 420rpx !important;
}

.card-swiper swiper-item {
	width: 610rpx !important;
	left: 70rpx;
	box-sizing: border-box;
	padding: 40rpx 0rpx 70rpx;
	overflow: initial;
}

.card-swiper swiper-item .swiper-item {
	width: 100%;
	display: block;
	height: 100%;
	border-radius: 10rpx;
	transform: scale(0.9);
	transition: all 0.2s ease-in 0s;
	overflow: hidden;
}

.card-swiper swiper-item.cur .swiper-item {
	transform: none;
	transition: all 0.2s ease-in 0s;
}


.tower-swiper {
	height: 420rpx;
	position: relative;
	max-width: 750rpx;
	overflow: hidden;
}

.tower-swiper .tower-item {
	position: absolute;
	width: 300rpx;
	height: 380rpx;
	top: 0;
	bottom: 0;
	left: 50%;
	margin: auto;
	transition: all 0.2s ease-in 0s;
	opacity: 1;
}

.tower-swiper .tower-item.none {
	opacity: 0;
}

.tower-swiper .tower-item .swiper-item {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
	overflow: hidden;
}

/* ==================
          步骤条
 ==================== */

.cu-steps {
	display: flex;
}

scroll-view.cu-steps {
	display: block;
	white-space: nowrap;
}

scroll-view.cu-steps .cu-item {
	display: inline-block;
}

.cu-steps .cu-item {
	flex: 1;
	text-align: center;
	position: relative;
	min-width: 100rpx;
}

.cu-steps .cu-item:not([class*="text-"]) {
	color: var(--grey);
}

.cu-steps .cu-item [class*="cuIcon-"],
.cu-steps .cu-item .num {
	display: block;
	font-size: 40rpx;
	line-height: 80rpx;
}

.cu-steps .cu-item::before,
.cu-steps .cu-item::after,
.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "";
	display: block;
	position: absolute;
	height: 0px;
	width: calc(100% - 80rpx);
	border-bottom: 1px solid #ccc;
	left: calc(0px - (100% - 80rpx) / 2);
	top: 40rpx;
	z-index: 0;
}

.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "\e6a3";
	font-family: "cuIcon";
	height: 30rpx;
	border-bottom-width: 0px;
	line-height: 30rpx;
	top: 0;
	bottom: 0;
	margin: auto;
	color: #ccc;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	bottom: 40rpx;
	top: initial;
}

.cu-steps .cu-item::after {
	border-bottom: 1px solid currentColor;
	width: 0px;
	transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"]::after {
	width: calc(100% - 80rpx);
	color: currentColor;
}

.cu-steps .cu-item:first-child::before,
.cu-steps .cu-item:first-child::after {
	display: none;
}

.cu-steps .cu-item .num {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	line-height: 40rpx;
	margin: 20rpx auto;
	font-size: 24rpx;
	border: 1px solid currentColor;
	position: relative;
	overflow: hidden;
}

.cu-steps .cu-item[class*="text-"] .num {
	background-color: currentColor;
}

.cu-steps .cu-item .num::before,
.cu-steps .cu-item .num::after {
	content: attr(data-index);
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	transition: all 0.3s ease-in-out 0s;
	transform: translateY(0rpx);
}

.cu-steps .cu-item[class*="text-"] .num::before {
	transform: translateY(-40rpx);
	color: var(--white);
}

.cu-steps .cu-item .num::after {
	transform: translateY(40rpx);
	color: var(--white);
	transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"] .num::after {
	content: "\e645";
	font-family: "cuIcon";
	color: var(--white);
	transform: translateY(0rpx);
}

.cu-steps .cu-item[class*="text-"] .num.err::after {
	content: "\e646";
}

/* ==================
          布局
 ==================== */

/*  -- flex弹性布局 -- */

.flex {
	display: flex;
}

.basis-xs {
	flex-basis: 20%;
}

.basis-sm {
	flex-basis: 40%;
}

.basis-df {
	flex-basis: 50%;
}

.basis-lg {
	flex-basis: 60%;
}

.basis-xl {
	flex-basis: 80%;
}

.flex-sub {
	flex: 1;
}

.flex-twice {
	flex: 2;
}

.flex-treble {
	flex: 3;
}

.flex-direction {
	flex-direction: column;
}

.flex-wrap {
	flex-wrap: wrap;
}

.align-start {
	align-items: flex-start;
}

.align-end {
	align-items: flex-end;
}

.align-center {
	align-items: center;
}

.align-stretch {
	align-items: stretch;
}

.self-start {
	align-self: flex-start;
}

.self-center {
	align-self: flex-center;
}

.self-end {
	align-self: flex-end;
}

.self-stretch {
	align-self: stretch;
}

.align-stretch {
	align-items: stretch;
}

.justify-start {
	justify-content: flex-start;
}

.justify-end {
	justify-content: flex-end;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.justify-around {
	justify-content: space-around;
}

/* grid布局 */

.grid {
	display: flex;
	flex-wrap: wrap;
}

.grid.grid-square {
	overflow: hidden;
}

.grid.grid-square .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
	border-bottom-left-radius: 6rpx;
	padding: 6rpx 12rpx;
	height: auto;
	background-color: rgba(0, 0, 0, 0.5);
}

.grid.grid-square>view>text[class*="cuIcon-"] {
	font-size: 52rpx;
	position: absolute;
	color: var(--grey);
	margin: auto;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.grid.grid-square>view {
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 6rpx;
	position: relative;
	overflow: hidden;
}

.grid.grid-square>view.bg-img image {
	width: 100%;
	height: 100%;
	position: absolute;
}

.grid.col-1.grid-square>view {
	padding-bottom: 100%;
	height: 0;
	margin-right: 0;
}

.grid.col-2.grid-square>view {
	padding-bottom: calc((100% - 20rpx)/2);
	height: 0;
	width: calc((100% - 20rpx)/2);
}

.grid.col-3.grid-square>view {
	padding-bottom: calc((100% - 40rpx)/3);
	height: 0;
	width: calc((100% - 40rpx)/3);
}

.grid.col-4.grid-square>view {
	padding-bottom: calc((100% - 60rpx)/4);
	height: 0;
	width: calc((100% - 60rpx)/4);
}

.grid.col-5.grid-square>view {
	padding-bottom: calc((100% - 80rpx)/5);
	height: 0;
	width: calc((100% - 80rpx)/5);
}

.grid.col-2.grid-square>view:nth-child(2n),
.grid.col-3.grid-square>view:nth-child(3n),
.grid.col-4.grid-square>view:nth-child(4n),
.grid.col-5.grid-square>view:nth-child(5n){
	margin-right: 0;
}

.grid.col-1>view {
	width: 100%;
}

.grid.col-2>view {
	width: 50%;
}

.grid.col-3>view {
	width: 33.33%;
}

.grid.col-4>view {
	width: 25%;
}

.grid.col-5>view {
	width: 20%;
}

/*  -- 内外边距 -- */

.margin-0 {
	margin: 0;
}

.margin-xs {
	margin: 10rpx;
}

.margin-sm {
	margin: 20rpx;
}

.margin {
	margin: 30rpx;
}

.margin-lg {
	margin: 40rpx;
}

.margin-xl {
	margin: 50rpx;
}

.margin-top-xs {
	margin-top: 10rpx;
}

.margin-top-sm {
	margin-top: 20rpx;
}

.margin-top {
	margin-top: 30rpx;
}

.margin-top-lg {
	margin-top: 40rpx;
}

.margin-top-xl {
	margin-top: 50rpx;
}

.margin-right-xs {
	margin-right: 10rpx;
}

.margin-right-sm {
	margin-right: 20rpx;
}

.margin-right {
	margin-right: 30rpx;
}

.margin-right-lg {
	margin-right: 40rpx;
}

.margin-right-xl {
	margin-right: 50rpx;
}

.margin-bottom-xs {
	margin-bottom: 10rpx;
}

.margin-bottom-sm {
	margin-bottom: 20rpx;
}

.margin-bottom {
	margin-bottom: 30rpx;
}

.margin-bottom-lg {
	margin-bottom: 40rpx;
}

.margin-bottom-xl {
	margin-bottom: 50rpx;
}

.margin-left-xs {
	margin-left: 10rpx;
}

.margin-left-sm {
	margin-left: 20rpx;
}

.margin-left {
	margin-left: 30rpx;
}

.margin-left-lg {
	margin-left: 40rpx;
}

.margin-left-xl {
	margin-left: 50rpx;
}

.margin-lr-xs {
	margin-left: 10rpx;
	margin-right: 10rpx;
}

.margin-lr-sm {
	margin-left: 20rpx;
	margin-right: 20rpx;
}

.margin-lr {
	margin-left: 30rpx;
	margin-right: 30rpx;
}

.margin-lr-lg {
	margin-left: 40rpx;
	margin-right: 40rpx;
}

.margin-lr-xl {
	margin-left: 50rpx;
	margin-right: 50rpx;
}

.margin-tb-xs {
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}

.margin-tb-sm {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}

.margin-tb {
	margin-top: 30rpx;
	margin-bottom: 30rpx;
}

.margin-tb-lg {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
}

.margin-tb-xl {
	margin-top: 50rpx;
	margin-bottom: 50rpx;
}

.padding-0 {
	padding: 0;
}

.padding-xs {
	padding: 10rpx;
}

.padding-sm {
	padding: 20rpx;
}

.padding {
	padding: 30rpx;
}

.padding-lg {
	padding: 40rpx;
}

.padding-xl {
	padding: 50rpx;
}

.padding-top-xs {
	padding-top: 10rpx;
}

.padding-top-sm {
	padding-top: 20rpx;
}

.padding-top {
	padding-top: 30rpx;
}

.padding-top-lg {
	padding-top: 40rpx;
}

.padding-top-xl {
	padding-top: 50rpx;
}

.padding-right-xs {
	padding-right: 10rpx;
}

.padding-right-sm {
	padding-right: 20rpx;
}

.padding-right {
	padding-right: 30rpx;
}

.padding-right-lg {
	padding-right: 40rpx;
}

.padding-right-xl {
	padding-right: 50rpx;
}

.padding-bottom-xs {
	padding-bottom: 10rpx;
}

.padding-bottom-sm {
	padding-bottom: 20rpx;
}

.padding-bottom {
	padding-bottom: 30rpx;
}

.padding-bottom-lg {
	padding-bottom: 40rpx;
}

.padding-bottom-xl {
	padding-bottom: 50rpx;
}

.padding-left-xs {
	padding-left: 10rpx;
}

.padding-left-sm {
	padding-left: 20rpx;
}

.padding-left {
	padding-left: 30rpx;
}

.padding-left-lg {
	padding-left: 40rpx;
}

.padding-left-xl {
	padding-left: 50rpx;
}

.padding-lr-xs {
	padding-left: 10rpx;
	padding-right: 10rpx;
}

.padding-lr-sm {
	padding-left: 20rpx;
	padding-right: 20rpx;
}

.padding-lr {
	padding-left: 30rpx;
	padding-right: 30rpx;
}

.padding-lr-lg {
	padding-left: 40rpx;
	padding-right: 40rpx;
}

.padding-lr-xl {
	padding-left: 50rpx;
	padding-right: 50rpx;
}

.padding-tb-xs {
	padding-top: 10rpx;
	padding-bottom: 10rpx;
}

.padding-tb-sm {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.padding-tb {
	padding-top: 30rpx;
	padding-bottom: 30rpx;
}

.padding-tb-lg {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
}

.padding-tb-xl {
	padding-top: 50rpx;
	padding-bottom: 50rpx;
}

/* -- 浮动 --  */

.cf::after,
.cf::before {
	content: " ";
	display: table;
}

.cf::after {
	clear: both;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

/* ==================
          背景
 ==================== */

.line-red::after,
.lines-red::after {
	border-color: var(--red);
}

.line-orange::after,
.lines-orange::after {
	border-color: var(--orange);
}

.line-yellow::after,
.lines-yellow::after {
	border-color: var(--yellow);
}

.line-olive::after,
.lines-olive::after {
	border-color: var(--olive);
}

.line-green::after,
.lines-green::after {
	border-color: var(--green);
}

.line-cyan::after,
.lines-cyan::after {
	border-color: var(--cyan);
}

.line-blue::after,
.lines-blue::after {
	border-color: var(--blue);
}

.line-purple::after,
.lines-purple::after {
	border-color: var(--purple);
}

.line-mauve::after,
.lines-mauve::after {
	border-color: var(--mauve);
}

.line-pink::after,
.lines-pink::after {
	border-color: var(--pink);
}

.line-brown::after,
.lines-brown::after {
	border-color: var(--brown);
}

.line-grey::after,
.lines-grey::after {
	border-color: var(--grey);
}

.line-gray::after,
.lines-gray::after {
	border-color: var(--gray);
}

.line-black::after,
.lines-black::after {
	border-color: var(--black);
}

.line-white::after,
.lines-white::after {
	border-color: var(--white);
}

.bg-red {
	background-color: var(--red);
	color: var(--white);
}

.bg-orange {
	background-color: var(--orange);
	color: var(--white);
}

.bg-yellow {
	background-color: var(--yellow);
	color: var(--black);
}

.bg-olive {
	background-color: var(--olive);
	color: var(--white);
}

.bg-green {
	background-color: var(--green);
	color: var(--white);
}

.bg-cyan {
	background-color: var(--cyan);
	color: var(--white);
}

.bg-blue {
	background-color: var(--blue);
	color: var(--white);
}

.bg-purple {
	background-color: var(--purple);
	color: var(--white);
}

.bg-mauve {
	background-color: var(--mauve);
	color: var(--white);
}

.bg-pink {
	background-color: var(--pink);
	color: var(--white);
}

.bg-brown {
	background-color: var(--brown);
	color: var(--white);
}

.bg-grey {
	background-color: var(--grey);
	color: var(--white);
}

.bg-gray {
	background-color: #f0f0f0;
	color: var(--black);
}

.bg-black {
	background-color: var(--black);
	color: var(--white);
}

.bg-white {
	background-color: var(--white);
	color: var(--darkGray);
}

.bg-shadeTop {
	background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
	color: var(--white);
}

.bg-shadeBottom {
	background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
	color: var(--white);
}

.bg-red.light {
	color: var(--red);
	background-color: var(--redLight);
}

.bg-orange.light {
	color: var(--orange);
	background-color: var(--orangeLight);
}

.bg-yellow.light {
	color: var(--yellow);
	background-color: var(--yellowLight);
}

.bg-olive.light {
	color: var(--olive);
	background-color: var(--oliveLight);
}

.bg-green.light {
	color: var(--green);
	background-color: var(--greenLight);
}

.bg-cyan.light {
	color: var(--cyan);
	background-color: var(--cyanLight);
}

.bg-blue.light {
	color: var(--blue);
	background-color: var(--blueLight);
}

.bg-purple.light {
	color: var(--purple);
	background-color: var(--purpleLight);
}

.bg-mauve.light {
	color: var(--mauve);
	background-color: var(--mauveLight);
}

.bg-pink.light {
	color: var(--pink);
	background-color: var(--pinkLight);
}

.bg-brown.light {
	color: var(--brown);
	background-color: var(--brownLight);
}

.bg-grey.light {
	color: var(--grey);
	background-color: var(--greyLight);
}

.bg-gradual-red {
	background-image: var(--gradualRed);
	color: var(--white);
}

.bg-gradual-orange {
	background-image: var(--gradualOrange);
	color: var(--white);
}

.bg-gradual-green {
	background-image: var(--gradualGreen);
	color: var(--white);
}

.bg-gradual-purple {
	background-image: var(--gradualPurple);
	color: var(--white);
}

.bg-gradual-pink {
	background-image: var(--gradualPink);
	color: var(--white);
}

.bg-gradual-blue {
	background-image: var(--gradualBlue);
	color: var(--white);
}

.shadow[class*="-red"] {
	box-shadow: var(--ShadowSize) var(--redShadow);
}

.shadow[class*="-orange"] {
	box-shadow: var(--ShadowSize) var(--orangeShadow);
}

.shadow[class*="-yellow"] {
	box-shadow: var(--ShadowSize) var(--yellowShadow);
}

.shadow[class*="-olive"] {
	box-shadow: var(--ShadowSize) var(--oliveShadow);
}

.shadow[class*="-green"] {
	box-shadow: var(--ShadowSize) var(--greenShadow);
}

.shadow[class*="-cyan"] {
	box-shadow: var(--ShadowSize) var(--cyanShadow);
}

.shadow[class*="-blue"] {
	box-shadow: var(--ShadowSize) var(--blueShadow);
}

.shadow[class*="-purple"] {
	box-shadow: var(--ShadowSize) var(--purpleShadow);
}

.shadow[class*="-mauve"] {
	box-shadow: var(--ShadowSize) var(--mauveShadow);
}

.shadow[class*="-pink"] {
	box-shadow: var(--ShadowSize) var(--pinkShadow);
}

.shadow[class*="-brown"] {
	box-shadow: var(--ShadowSize) var(--brownShadow);
}

.shadow[class*="-grey"] {
	box-shadow: var(--ShadowSize) var(--greyShadow);
}

.shadow[class*="-gray"] {
	box-shadow: var(--ShadowSize) var(--grayShadow);
}

.shadow[class*="-black"] {
	box-shadow: var(--ShadowSize) var(--blackShadow);
}

.shadow[class*="-white"] {
	box-shadow: var(--ShadowSize) var(--blackShadow);
}

.text-shadow[class*="-red"] {
	text-shadow: var(--ShadowSize) var(--redShadow);
}

.text-shadow[class*="-orange"] {
	text-shadow: var(--ShadowSize) var(--orangeShadow);
}

.text-shadow[class*="-yellow"] {
	text-shadow: var(--ShadowSize) var(--yellowShadow);
}

.text-shadow[class*="-olive"] {
	text-shadow: var(--ShadowSize) var(--oliveShadow);
}

.text-shadow[class*="-green"] {
	text-shadow: var(--ShadowSize) var(--greenShadow);
}

.text-shadow[class*="-cyan"] {
	text-shadow: var(--ShadowSize) var(--cyanShadow);
}

.text-shadow[class*="-blue"] {
	text-shadow: var(--ShadowSize) var(--blueShadow);
}

.text-shadow[class*="-purple"] {
	text-shadow: var(--ShadowSize) var(--purpleShadow);
}

.text-shadow[class*="-mauve"] {
	text-shadow: var(--ShadowSize) var(--mauveShadow);
}

.text-shadow[class*="-pink"] {
	text-shadow: var(--ShadowSize) var(--pinkShadow);
}

.text-shadow[class*="-brown"] {
	text-shadow: var(--ShadowSize) var(--brownShadow);
}

.text-shadow[class*="-grey"] {
	text-shadow: var(--ShadowSize) var(--greyShadow);
}

.text-shadow[class*="-gray"] {
	text-shadow: var(--ShadowSize) var(--grayShadow);
}

.text-shadow[class*="-black"] {
	text-shadow: var(--ShadowSize) var(--blackShadow);
}

.bg-img {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.bg-mask {
	background-color: var(--black);
	position: relative;
}

.bg-mask::after {
	content: "";
	border-radius: inherit;
	width: 100%;
	height: 100%;
	display: block;
	background-color: rgba(0, 0, 0, 0.4);
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
}

.bg-mask view,
.bg-mask cover-view {
	z-index: 5;
	position: relative;
}

.bg-video {
	position: relative;
}

.bg-video video {
	display: block;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
	position: absolute;
	top: 0;
	z-index: 0;
	pointer-events: none;
}

/* ==================
          文本
 ==================== */

.text-xs {
	font-size: 20rpx;
}

.text-sm {
	font-size: 24rpx;
	line-height: 40rpx;
}

.text-df {
	font-size: 28rpx;
}

.text-lg {
	font-size: 32rpx;
}

.text-xl {
	font-size: 36rpx;
}

.text-xxl {
	font-size: 44rpx;
}

.text-sl {
	font-size: 80rpx;
}

.text-xsl {
	font-size: 120rpx;
}

.text-Abc {
	text-transform: Capitalize;
}

.text-ABC {
	text-transform: Uppercase;
}

.text-abc {
	text-transform: Lowercase;
}

.text-price::before {
	content: "¥";
	font-size: 80%;
	margin-right: 4rpx;
}

.text-cut {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.text-bold {
	font-weight: bold;
}

.text-center {
	text-align: center;
}

.text-content {
	line-height: 1.6;
}

.text-left {
	text-align: left;
}

.text-right {
	text-align: right;
}

.text-red,
.line-red,
.lines-red {
	color: var(--red);
}

.text-orange,
.line-orange,
.lines-orange {
	color: var(--orange);
}

.text-yellow,
.line-yellow,
.lines-yellow {
	color: var(--yellow);
}

.text-olive,
.line-olive,
.lines-olive {
	color: var(--olive);
}

.text-green,
.line-green,
.lines-green {
	color: var(--green);
}

.text-cyan,
.line-cyan,
.lines-cyan {
	color: var(--cyan);
}

.text-blue,
.line-blue,
.lines-blue {
	color: var(--blue);
}

.text-purple,
.line-purple,
.lines-purple {
	color: var(--purple);
}

.text-mauve,
.line-mauve,
.lines-mauve {
	color: var(--mauve);
}

.text-pink,
.line-pink,
.lines-pink {
	color: var(--pink);
}

.text-brown,
.line-brown,
.lines-brown {
	color: var(--brown);
}

.text-grey,
.line-grey,
.lines-grey {
	color: var(--grey);
}

.text-gray,
.line-gray,
.lines-gray {
	color: var(--gray);
}

.text-black,
.line-black,
.lines-black {
	color: var(--black);
}

.text-white,
.line-white,
.lines-white {
	color: var(--white);
}
