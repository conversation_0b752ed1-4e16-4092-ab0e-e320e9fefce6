.flex7-clz {
    padding-top: 40px;
    padding-left: 5px;
    padding-bottom: 20px;
    padding-right: 5px;
}

.flex8-clz {
    height: 60px;
}

.image3-clz {
    flex-shrink: 0;
    border-bottom-left-radius: 60px;
    overflow: hidden;
    width: 60px !important;
    border-top-left-radius: 60px;
    border-top-right-radius: 60px;
    border-bottom-right-radius: 60px;
    height: 60px !important;
}

.image3-size {
    height: 60px !important;
    width: 60px !important;
}

.text1-clz {
    margin-left: 5px;
    padding-top: 5px;
    font-weight: bold;
    padding-left: 10px;
    font-size: 16px !important;
    padding-bottom: 5px;
    margin-top: 0px;
    margin-bottom: 5px;
    margin-right: 5px;
    padding-right: 10px;
}

.phone-clz {
    margin-left: 5px;
    width: calc(100% - 5px - 5px) !important;
    font-size: 14px !important;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}

.password-clz {
    margin-left: 5px;
    width: calc(100% - 5px - 5px) !important;
    font-size: 14px !important;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}

.code-clz {
    margin-left: 5px;
    width: calc(100% - 5px - 5px) !important;
    font-size: 14px !important;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}

.flex1-clz {
    padding-top: 8px;
    padding-left: 15px;
    padding-bottom: 8px;
    padding-right: 15px;
}

.text2-clz {
    color: #39b54a;
}

.text5-clz {
    color: #39b54a;
}

.text4-clz {
    padding-top: 10px;
    border-bottom-left-radius: 60px;
    color: #ffffff;
    font-weight: bold;
    letter-spacing: 3px !important;
    padding-left: 5px;
    font-size: 16px !important;
    padding-bottom: 10px;
    border-top-right-radius: 60px;
    margin-right: 15px;
    margin-left: 15px;
    box-shadow: 0px 3px 7px rgba(64, 195, 3, 0.35);
    overflow: hidden;
    width: calc(100% - 15px - 15px) !important;
    border-top-left-radius: 60px;
    margin-top: 5px;
    border-bottom-right-radius: 60px;
    margin-bottom: 5px;
    text-align: center;
    padding-right: 5px;
}

.flex3-clz {
    padding-top: 8px;
    padding-left: 15px;
    padding-bottom: 8px;
    padding-right: 15px;
}

.icon3-clz {
    color: #39b54a;
}

.icon3 {
    font-size: 20px;
}

.icon2-clz {
    color: #39b54a;
}

.icon2 {
    font-size: 20px;
}

.text11-clz {
    color: #39b54a;
}

.text13-clz {
    color: #39b54a;
}

.flex2-clz {
    margin-left: 5px;
    padding-top: 8px;
    padding-left: 15px;
    width: calc(100% - 5px - 5px) !important;
    padding-bottom: 8px;
    margin-top: 15px;
    margin-bottom: 5px;
    margin-right: 5px;
    padding-right: 15px;
}

.line-clz {
    flex: 1;
}

.text6-clz {
    padding-top: 5px;
    padding-left: 5px;
    padding-bottom: 5px;
    padding-right: 5px;
}

.line1-clz {
    flex: 1;
}

.flex4-clz {
    padding-top: 8px;
    padding-left: 15px;
    padding-bottom: 8px;
    padding-right: 15px;
}

.image-size {
    height: 40px !important;
    width: 40px !important;
}

.image2-size {
    height: 40px !important;
    width: 40px !important;
}

.image1-size {
    height: 40px !important;
    width: 40px !important;
}

.container10021 {
    padding-left: 0px;
    padding-right: 0px;


    background-position: top center;
    background-size: contain;
    background-repeat: no-repeat;
}
