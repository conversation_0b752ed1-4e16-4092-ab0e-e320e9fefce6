{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749195639657}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgJCBmcm9tICdqcXVlcnknOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJMZWZ0TWVudSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVzZXJMbmFtZTogIiIsDQogICAgICByb2xlOiAiIiwNCiAgICAgIGFjdGl2ZU1lbnU6IG51bGwsIC8vIOeUqOS6jui3n+i4quW9k+WJjea/gOa0u+eahOiPnOWNlQ0KICAgICAgc2hvd2V4aXN0OiBmYWxzZSwNCiAgICB9Ow0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMudXNlckxuYW1lID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlckxuYW1lIik7DQogICAgdGhpcy5yb2xlID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgicm9sZSIpOw0KDQogICAgLy8g5L+u5pS56I+c5Y2V54K55Ye75LqL5Lu2DQogICAgJCgnLmhhcy1hcnJvdycpLmNsaWNrKGZ1bmN0aW9uIChlKSB7DQogICAgICBlLnByZXZlbnREZWZhdWx0KCk7DQoNCiAgICAgIC8vIOS4uueItmxp5YWD57Sg5re75YqgbW0tYWN0aXZl57G7DQogICAgICAkKHRoaXMpLnBhcmVudCgnbGknKS5hZGRDbGFzcygnbW0tYWN0aXZlJyk7DQoNCiAgICAgICQodGhpcykubmV4dCgndWwnKS50b2dnbGVDbGFzcygnbW0tc2hvdycpOw0KDQogICAgICAvLyDlhbPpl63lhbbku5bmiZPlvIDnmoToj5zljZUNCiAgICAgICQoJy5oYXMtYXJyb3cnKS5ub3QodGhpcykucGFyZW50KCdsaScpLnJlbW92ZUNsYXNzKCdtbS1hY3RpdmUnKTsNCiAgICAgICQoJy5oYXMtYXJyb3cnKS5ub3QodGhpcykubmV4dCgndWwnKS5yZW1vdmVDbGFzcygnbW0tc2hvdycpOw0KICAgIH0pOw0KDQoNCiAgfSwNCiAgbWV0aG9kczogew0KDQogICAgdG9nZ2xlU2hvd0V4aXN0KCkgew0KICAgICAgdGhpcy5zaG93ZXhpc3QgPSAhdGhpcy5zaG93ZXhpc3Q7DQoNCiAgICAgIGlmICh0aGlzLnNob3dleGlzdCkgew0KICAgICAgICAkKCIuZHJvcGRvd24tbWVudSIpLnJlbW92ZUNsYXNzKCJzaG93Iik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAkKCIuZHJvcGRvd24tbWVudSIpLmFkZENsYXNzKCJzaG93Iik7DQogICAgICB9DQoNCiAgICB9LA0KDQogICAgZXhpdDogZnVuY3Rpb24gKCkgew0KICAgICAgdmFyIF90aGlzID0gdGhpczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oIuehruiupOmAgOWHuuWQlz8iLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgidXNlckxuYW1lIik7DQogICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgicm9sZSIpOw0KICAgICAgICAgIF90aGlzLiRyb3V0ZXIucHVzaCgiLyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4geyB9KTsNCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";AAsYA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;;;EAGJ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC;;IAEF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACrB,CAAC;EACH;AACF,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"deznav\" style=\"background:#8BC34A;\">\r\n    <div class=\"deznav-scroll mm-active\">\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '管理员'\">\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">科室管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/partsAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加科室</router-link></li>\r\n            <li><router-link to=\"/partsManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理科室</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '医生'\">\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">聊天管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/chatinfoManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理聊天</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n            <li><router-link to=\"/doctorInfo\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>修改个人信息</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">邮件模板管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n      showexist: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n\r\n    // 修改菜单点击事件\r\n    $('.has-arrow').click(function (e) {\r\n      e.preventDefault();\r\n\r\n      // 为父li元素添加mm-active类\r\n      $(this).parent('li').addClass('mm-active');\r\n\r\n      $(this).next('ul').toggleClass('mm-show');\r\n\r\n      // 关闭其他打开的菜单\r\n      $('.has-arrow').not(this).parent('li').removeClass('mm-active');\r\n      $('.has-arrow').not(this).next('ul').removeClass('mm-show');\r\n    });\r\n\r\n\r\n  },\r\n  methods: {\r\n\r\n    toggleShowExist() {\r\n      this.showexist = !this.showexist;\r\n\r\n      if (this.showexist) {\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      } else {\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"]}]}