{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue", "mtime": 1749196192738}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";AAoCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEjB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEV,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;KACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC;UACE;AACV,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;UACE;;;IAGN,CAAC;;;EAGH,CAAC;AACH,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>  \r\n  <div class=\"body\">  \r\n    <div class=\"login-container\">  \r\n           <h2 style=\"text-align: center; font-size: 24px; margin-bottom: 24px;\">医院挂号预约系统</h2>\r\n            <form>  \r\n               <div>  \r\n                   <label for=\"uaccount\">账号</label>  \r\n                   <input type=\"text\" id=\"uaccount\" placeholder=\"请输入账号\" required v-model=\"loginModel.username\">  \r\n               </div>  \r\n               <div>  \r\n                   <label for=\"password\">密码</label>  \r\n                   <input type=\"password\" id=\"password\" placeholder=\"请输入密码\" required v-model=\"loginModel.password\">  \r\n               </div>  \r\n                   <div>  \r\n                   <label>身份</label>  \r\n                   <div class=\"role-selection\">  \r\n                              <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\r\n      <el-radio label=\"医生\" v-model=\"loginModel.radio\">医生</el-radio>\r\n \r\n                   </div>  \r\n               </div> \r\n    \r\n               <button type=\"button\" @click=\"login\">登录</button>  \r\n           </form>  \r\n            \r\n \r\n<!--           <p>还没有账户？ <a href=\"#\">注册</a></p> --> \r\n       </div>  \r\n       <div class=\"bubble\" style=\"width: 60px; height: 60px; left: 20%; animation-delay: 0s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 40px; height: 40px; left: 50%; animation-delay: 2s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 80px; height: 80px; left: 80%; animation-delay: 4s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 30px; height: 30px; left: 30%; animation-delay: 1s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 50px; height: 50px; left: 70%; animation-delay: 3s;\"></div>   \r\n   </div>  \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      year: new Date().getFullYear(),\r\n      loginModel: {\r\n        username: \"\",\r\n        password: \"\",\r\n        radio: \"管理员\",\r\n      },\r\n      loginModel2: {},\r\n     \r\n    };\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    \r\n  },\r\n  methods: {\r\n    login() {\r\n      let that = this;  \r\n\r\n      if (that.loginModel.username == \"\") {\r\n        that.$message({\r\n          message: \"请输入账号\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      if (that.loginModel.password == \"\") {\r\n        that.$message({\r\n          message: \"请输入密码\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }   \r\n      \r\n      this.loading = true;\r\n     var role = that.loginModel.radio; //获取身份\r\nif (role == '管理员') {\r\n      let url = base + \"/admin/login\";\r\n      this.loginModel2.aname = this.loginModel.username;\r\n      this.loginModel2.loginpassword = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\r\n          sessionStorage.setItem(\"role\", \"管理员\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟管理员登录');\r\n        const mockUser = { aid: 1, aname: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"管理员\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\nelse if (role == '医生') {\r\n      let url = base + \"/doctor/login\";\r\n      this.loginModel2.daccount = this.loginModel.username;\r\n      this.loginModel2.password = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.daccount);\r\n          sessionStorage.setItem(\"role\", \"医生\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      });\r\n          }\r\n    \r\n     \r\n    },\r\n    \r\n    \r\n  },\r\n};\r\n</script>\r\n   \r\n<style scoped>  \r\n@import \"../assets/css/body.css\";\r\n/* 全局样式重置 */  \r\n* {  \r\n    margin: 0;  \r\n    padding: 0;  \r\n    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  \r\n}  \r\n\r\nbody {  \r\n    width: 100%;  \r\n    height: 100%;  \r\n    overflow: hidden; /* 确保没有滚动条 */  \r\n}  \r\n\r\n.body {  \r\n    background: linear-gradient(to bottom right, #009688, #8BC34A);  \r\n    display: flex;  \r\n    align-items: center;  \r\n    justify-content: center;  \r\n    height: 100vh;  \r\n}  \r\n\r\n.login-container {  \r\n    background: white;  \r\n    border-radius: 8px;  \r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  \r\n    padding: 32px;  \r\n    position: relative;  \r\n    z-index: 10;  \r\n    transition: transform 0.3s ease;  \r\n    width: 400px;  \r\n}  \r\n\r\n.login-container:hover {  \r\n    transform: scale(1.05);  \r\n}  \r\n\r\n.bubble {  \r\n    position: absolute;  \r\n    bottom: -100px;  \r\n    border-radius: 50%;  \r\n    background: rgba(255, 255, 255, 0.6);  \r\n    animation: rise 10s infinite;  \r\n}  \r\n\r\n@keyframes rise {  \r\n    0% {  \r\n        transform: translateY(0);  \r\n        opacity: 1;  \r\n    }  \r\n    100% {  \r\n        transform: translateY(-600px);  \r\n        opacity: 0;  \r\n    }  \r\n}  \r\n\r\ninput {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    margin: 10px 0;  \r\n    border: 1px solid #ccc;  \r\n    border-radius: 4px;  \r\n}  \r\n\r\nbutton {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    background-color: #8BC34A;  \r\n    color: white;  \r\n    border: none;  \r\n    border-radius: 4px;  \r\n    cursor: pointer;  \r\n    transition: background-color 0.2s;  \r\n}  \r\n\r\nbutton:hover {  \r\n    background-color: #81d522;  \r\n}  \r\n\r\np {  \r\n    text-align: center;  \r\n    color: #666;  \r\n}  \r\n\r\na {  \r\n    color: #8BC34A;  \r\n    text-decoration: none;  \r\n}  \r\n\r\na:hover {  \r\n    text-decoration: underline;  \r\n}  \r\n\r\n.role-selection {  \r\n    display: flex;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n\r\n.role-selection input {  \r\n    margin-right: 5px;  \r\n}  \r\n\r\ninput[type=\"radio\"] {  \r\n    display: flex;  \r\n    width: 50px;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n</style>\r\n\r\n"]}]}