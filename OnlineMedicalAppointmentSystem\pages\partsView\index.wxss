
.flex2-clz {
    background-color: #17b28a;
    z-index: 100;
    color: #ffffff;
}
.text1-clz {
    margin-left: 10px;
    font-size: 18px;
    margin-top: 0px;
    margin-bottom: 0px;
    margin-right: 0px;
}
.text-clz {
    margin-left: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text2-clz {
    font-size: 18px;
}
.search-clz {
    margin-left: 10px;
    border-bottom-left-radius: 50px;
    box-shadow: 0px 2px 3px rgba(23, 178, 138, 0.65);
    overflow: hidden;
    width: calc(100% - 10px - 10px) !important;
    border-top-left-radius: 50px;
    margin-top: 10px;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
    margin-bottom: 10px;
    margin-right: 10px;
}
.flex10-clz {
    margin-left: 10px;
    border-bottom-left-radius: 10px;
    box-shadow: 1px 1px 3px 1px rgba(154, 154, 154, 0.37);
    z-index: 100;
    overflow: hidden;
    width: calc(100% - 10px - 10px) !important;
    border-top-left-radius: 10px;
    margin-top: 0px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    margin-bottom: 10px;
    margin-right: 10px;
}
.flex1-clz {
    padding-top: 10px;
    border-bottom-left-radius: 10px;
    z-index: 100;
    padding-left: 10px;
    padding-bottom: 10px;
    border-top-right-radius: 10px;
    margin-right: 10px;
    margin-left: 10px;
    box-shadow: 1px 1px 3px 1px rgba(154, 154, 154, 0.37);
    overflow: hidden;
    width: calc(100% - 10px - 10px) !important;
    border-top-left-radius: 10px;
    margin-top: 10px;
    border-bottom-right-radius: 10px;
    margin-bottom: 10px;
    padding-right: 10px;
}
.image-clz {
    border-bottom-left-radius: 6px;
    overflow: hidden;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
.flex3-clz {
    padding-top: 0px;
    z-index: 100;
    flex: 1;
    padding-left: 5px;
    padding-bottom: 0px;
    height: 100px;
    padding-right: 0px;
}
.flex6-clz {
    z-index: 100;
    flex: 1;
}
.flex4-clz {
    z-index: 100;
}
.text4-clz {
    margin-left: 0px;
    color: #ff9e01;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text5-clz {
    margin-left: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text9-clz {
    border: 1px solid #17b28a;
    padding-top: 5px;
    border-bottom-left-radius: 20px;
    color: #17b28a;
    padding-left: 10px;
    padding-bottom: 5px;
    border-top-right-radius: 20px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 20px;
    margin-top: 5px;
    border-bottom-right-radius: 20px;
    margin-bottom: 5px;
    padding-right: 10px;
}
.flex5-clz {
    z-index: 100;
}
.text7-clz {
    border: 1px solid #0ba1f7;
    padding-top: 3px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 3px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.text8-clz {
    border: 1px solid #0ba1f7;
    padding-top: 3px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 3px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.flex8-clz {
    padding-top: 10px;
    border-bottom-left-radius: 10px;
    z-index: 100;
    padding-left: 10px;
    padding-bottom: 10px;
    border-top-right-radius: 10px;
    margin-right: 10px;
    margin-left: 10px;
    box-shadow: 1px 1px 3px 1px rgba(154, 154, 154, 0.37);
    overflow: hidden;
    width: calc(100% - 10px - 10px) !important;
    border-top-left-radius: 10px;
    margin-top: 10px;
    border-bottom-right-radius: 10px;
    margin-bottom: 10px;
    padding-right: 10px;
}
.image3-clz {
    border-bottom-left-radius: 6px;
    overflow: hidden;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
.flex12-clz {
    padding-top: 0px;
    z-index: 100;
    flex: 1;
    padding-left: 5px;
    padding-bottom: 0px;
    height: 100px;
    padding-right: 0px;
}
.flex16-clz {
    z-index: 100;
    flex: 1;
}
.flex18-clz {
    z-index: 100;
}
.text15-clz {
    margin-left: 0px;
    color: #ff9e01;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text19-clz {
    margin-left: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text22-clz {
    border: 1px solid #17b28a;
    padding-top: 5px;
    border-bottom-left-radius: 20px;
    color: #17b28a;
    padding-left: 10px;
    padding-bottom: 5px;
    border-top-right-radius: 20px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 20px;
    margin-top: 5px;
    border-bottom-right-radius: 20px;
    margin-bottom: 5px;
    padding-right: 10px;
}
.flex20-clz {
    z-index: 100;
}
.text24-clz {
    border: 1px solid #0ba1f7;
    padding-top: 5px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 5px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.text26-clz {
    border: 1px solid #0ba1f7;
    padding-top: 5px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 5px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.flex7-clz {
    padding-top: 10px;
    border-bottom-left-radius: 10px;
    z-index: 100;
    padding-left: 10px;
    padding-bottom: 10px;
    border-top-right-radius: 10px;
    margin-right: 10px;
    margin-left: 10px;
    box-shadow: 1px 1px 3px 1px rgba(154, 154, 154, 0.37);
    overflow: hidden;
    width: calc(100% - 10px - 10px) !important;
    border-top-left-radius: 10px;
    margin-top: 10px;
    border-bottom-right-radius: 10px;
    margin-bottom: 10px;
    padding-right: 10px;
}
.image2-clz {
    border-bottom-left-radius: 6px;
    overflow: hidden;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
.flex9-clz {
    padding-top: 0px;
    z-index: 100;
    flex: 1;
    padding-left: 5px;
    padding-bottom: 0px;
    height: 100px;
    padding-right: 0px;
}
.flex11-clz {
    z-index: 100;
    flex: 1;
}
.flex13-clz {
    z-index: 100;
}
.text12-clz {
    margin-left: 0px;
    color: #ff9e01;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text14-clz {
    margin-left: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.text16-clz {
    border: 1px solid #17b28a;
    padding-top: 5px;
    border-bottom-left-radius: 20px;
    color: #17b28a;
    padding-left: 10px;
    padding-bottom: 5px;
    border-top-right-radius: 20px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 20px;
    margin-top: 5px;
    border-bottom-right-radius: 20px;
    margin-bottom: 5px;
    padding-right: 10px;
}
.flex15-clz {
    z-index: 100;
}
.text18-clz {
    border: 1px solid #0ba1f7;
    padding-top: 5px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 5px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.text20-clz {
    border: 1px solid #0ba1f7;
    padding-top: 5px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 5px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.text6-clz {
    border: 1px solid #0ba1f7;
    padding-top: 5px;
    border-bottom-left-radius: 3px;
    color: #0ba1f7;
    padding-left: 5px;
    padding-bottom: 5px;
    border-top-right-radius: 3px;
    margin-right: 5px;
    margin-left: 0px;
    overflow: hidden;
    border-top-left-radius: 3px;
    margin-top: 0px;
    border-bottom-right-radius: 3px;
    margin-bottom: 0px;
    padding-right: 5px;
}
.container {
    padding-left: 0px;
    padding-right: 0px;

    font-size: 12px;
}
