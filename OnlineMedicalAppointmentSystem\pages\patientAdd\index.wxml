<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText">返回</view>
        <view slot="content">添加就诊人</view>
    </diy-navbar>

    <form bindsubmit="submitForm" class="flex diy-form diy-col-24 justify-center">
        <view class="diy-form-item diy-col-24">
            <view class="title">姓名：</view>
            <view class="input">
                <input name="peoname" placeholder="请输入姓名" />
            </view>
        </view>

        <view class="diy-form-item diy-col-24">
            <view class="title">性别：</view>
            <view class="input">
                <picker bindchange="bindGenderChange" value="{{genderIndex}}" range="{{genderArray}}">
                    <view class="picker">{{genderArray[genderIndex]}}</view>
                </picker>
            </view>
        </view>

        <view class="diy-form-item diy-col-24">
            <view class="title">手机号：</view>
            <view class="input">
                <input name="phone" type="number" placeholder="请输入手机号" />
            </view>
        </view>

        <view class="diy-form-item diy-col-24">
            <view class="title">年龄：</view>
            <view class="input">
                <input name="age" type="number" placeholder="请输入年龄" />
            </view>
        </view>

        <view class="flex diy-col-24 justify-center">
            <button form-type="submit" class="diy-btn green flex1 margin-xs">提交</button>
        </view>
    </form>
</view>