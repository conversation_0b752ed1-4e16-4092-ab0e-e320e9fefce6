{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749195815470}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXJMbmFtZTogIiIsCiAgICAgIHJvbGU6ICIiLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgc3RhdGlzdGljczogewogICAgICAgIHBhdGllbnRDb3VudDogMCwKICAgICAgICBkb2N0b3JDb3VudDogMCwKICAgICAgICB0b2RheUFwcG9pbnRtZW50Q291bnQ6IDAsCiAgICAgICAgdG90YWxBcHBvaW50bWVudENvdW50OiAwLAogICAgICAgIGRvY3RvclRvZGF5QXBwb2ludG1lbnRDb3VudDogMCwKICAgICAgICBkb2N0b3JUb3RhbEFwcG9pbnRtZW50Q291bnQ6IDAKICAgICAgfQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLnVzZXJMbmFtZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXJMbmFtZSIpOwogICAgdGhpcy5yb2xlID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgicm9sZSIpOwogICAgdGhpcy5sb2FkU3RhdGlzdGljcygpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yqg6L2957uf6K6h5pWw5o2uCiAgICBhc3luYyBsb2FkU3RhdGlzdGljcygpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ1c2VyIikpOwogICAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9zdGF0aXN0aWNzL2Rhc2hib2FyZCI7CiAgICAgICAgY29uc3QgcGFyYW1zID0gewogICAgICAgICAgcm9sZTogdGhpcy5yb2xlCiAgICAgICAgfTsKCiAgICAgICAgLy8g5aaC5p6c5piv5Yy755Sf77yM5Lyg6YCS5Yy755SfSUQKICAgICAgICBpZiAodGhpcy5yb2xlID09PSAn5Yy755SfJyAmJiB1c2VyKSB7CiAgICAgICAgICBwYXJhbXMuZG9jdG9ySWQgPSB1c2VyLmRpZDsKICAgICAgICB9CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0LnBvc3QodXJsLCBwYXJhbXMpOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuc3RhdGlzdGljcyA9IHJlc3BvbnNlLnJlc2RhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlue7n+iuoeaVsOaNruWksei0pScpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3nu5/orqHmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlue7n+iuoeaVsOaNruWksei0pScpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "data", "userLname", "role", "loading", "statistics", "patientCount", "doctor<PERSON>ount", "todayAppointmentCount", "totalAppointmentCount", "doctorTodayAppointmentCount", "doctorTotalAppointmentCount", "mounted", "sessionStorage", "getItem", "loadStatistics", "methods", "user", "JSON", "parse", "url", "params", "doctorId", "did", "response", "post", "code", "resdata", "$message", "error", "console"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div style=\"width: 100%; padding: 20px;\" id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div style=\"text-align: center; margin-bottom: 30px;\">\r\n      <h2 style=\"color: #409EFF; margin-bottom: 10px;\">欢迎使用医院挂号预约系统</h2>\r\n      <p style=\"font-size: 16px; color: #666;\">\r\n        账号：<b style=\"color: #E6A23C;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #E6A23C;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div v-loading=\"loading\" style=\"display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;\">\r\n      <!-- 管理员统计卡片 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon patients\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.patientCount || 0 }}</h3>\r\n              <p>患者总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon doctors\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorCount || 0 }}</h3>\r\n              <p>医生总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.todayAppointmentCount || 0 }}</h3>\r\n              <p>今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.totalAppointmentCount || 0 }}</h3>\r\n              <p>总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n\r\n      <!-- 医生统计卡片 -->\r\n      <template v-if=\"role === '医生'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTodayAppointmentCount || 0 }}</h3>\r\n              <p>我的今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTotalAppointmentCount || 0 }}</h3>\r\n              <p>我的总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n    </div>\r\n\r\n    <!-- 操作提示 -->\r\n    <div style=\"text-align: center; margin-top: 40px; color: #909399;\">\r\n      <p style=\"font-size: 14px;\">请在左侧菜单中选择您要进行的操作！</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      loading: false,\r\n      statistics: {\r\n        patientCount: 0,\r\n        doctorCount: 0,\r\n        todayAppointmentCount: 0,\r\n        totalAppointmentCount: 0,\r\n        doctorTodayAppointmentCount: 0,\r\n        doctorTotalAppointmentCount: 0\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        const user = JSON.parse(sessionStorage.getItem(\"user\"));\r\n        let url = base + \"/statistics/dashboard\";\r\n\r\n        const params = {\r\n          role: this.role\r\n        };\r\n\r\n        // 如果是医生，传递医生ID\r\n        if (this.role === '医生' && user) {\r\n          params.doctorId = user.did;\r\n        }\r\n\r\n        const response = await request.post(url, params);\r\n        if (response.code === 200) {\r\n          this.statistics = response.resdata;\r\n        } else {\r\n          this.$message.error('获取统计数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error);\r\n        this.$message.error('获取统计数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stat-card {\r\n  width: 280px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.patients {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.doctors {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.today-appointments {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.total-appointments {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-info h3 {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.stat-info p {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin: 5px 0 0 0;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .stat-card {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAoGA,OAAOA,OAAO,IAAIC,IAAG,QAAS,qBAAqB;AAEnD,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;QACVC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,CAAC;QACdC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE,CAAC;QACxBC,2BAA2B,EAAE,CAAC;QAC9BC,2BAA2B,EAAE;MAC/B;IACF,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACV,SAAQ,GAAIW,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACX,IAAG,GAAIU,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB,CAAC;EACDC,OAAO,EAAE;IACP;IACA,MAAMD,cAAcA,CAAA,EAAG;MACrB,IAAI,CAACX,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAMa,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACN,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,IAAIM,GAAE,GAAIpB,IAAG,GAAI,uBAAuB;QAExC,MAAMqB,MAAK,GAAI;UACblB,IAAI,EAAE,IAAI,CAACA;QACb,CAAC;;QAED;QACA,IAAI,IAAI,CAACA,IAAG,KAAM,IAAG,IAAKc,IAAI,EAAE;UAC9BI,MAAM,CAACC,QAAO,GAAIL,IAAI,CAACM,GAAG;QAC5B;QAEA,MAAMC,QAAO,GAAI,MAAMzB,OAAO,CAAC0B,IAAI,CAACL,GAAG,EAAEC,MAAM,CAAC;QAChD,IAAIG,QAAQ,CAACE,IAAG,KAAM,GAAG,EAAE;UACzB,IAAI,CAACrB,UAAS,GAAImB,QAAQ,CAACG,OAAO;QACpC,OAAO;UACL,IAAI,CAACC,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAC;QACjC;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACD,QAAQ,CAACC,KAAK,CAAC,UAAU,CAAC;MACjC,UAAU;QACR,IAAI,CAACzB,OAAM,GAAI,KAAK;MACtB;IACF;EACF;AACF,CAAC", "ignoreList": []}]}