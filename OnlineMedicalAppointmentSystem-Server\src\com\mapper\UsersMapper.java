package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Users;

public interface UsersMapper {

	//返回所有记录
	public List<Users> findUsersList();
	
	//查询多条记录
	public List<Users> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertUsers(Users users);

	//根据ID删除
	public int deleteUsers(String id);
	
	//更新
	public int updateUsers(Users users);
	
	//根据ID得到对应的记录
	public Users queryUsersById(String id);
	
}

