package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ChatinfoMapper;
import com.model.Chatinfo;
import com.util.PageBean;
@Service
public class ChatinfoServiceImpl implements ChatinfoService{
        
	@Autowired
	private ChatinfoMapper chatinfoMapper;

	//查询多条记录
	public List<Chatinfo> queryChatinfoList(Chatinfo chatinfo,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(chatinfo, page);
		
		List<Chatinfo> getChatinfo = chatinfoMapper.query(map);
		
		return getChatinfo;
	}
	
	//得到记录总数
	@Override
	public int getCount(Chatinfo chatinfo) {
		Map<String, Object> map = getQueryMap(chatinfo, null);
		int count = chatinfoMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Chatinfo chatinfo,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(chatinfo!=null){
			map.put("cid", chatinfo.getCid());
			map.put("lname", chatinfo.getLname());
			map.put("did", chatinfo.getDid());
			map.put("content", chatinfo.getContent());
			map.put("sendtime", chatinfo.getSendtime());
			map.put("flag", chatinfo.getFlag());
			map.put("sort", chatinfo.getSort());
			map.put("condition", chatinfo.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertChatinfo(Chatinfo chatinfo) throws Exception {
		return chatinfoMapper.insertChatinfo(chatinfo);
	}

	//根据ID删除
	public int deleteChatinfo(int id) throws Exception {
		return chatinfoMapper.deleteChatinfo(id);
	}

	//更新
	public int updateChatinfo(Chatinfo chatinfo) throws Exception {
		return chatinfoMapper.updateChatinfo(chatinfo);
	}
	
	//根据ID得到对应的记录
	public Chatinfo queryChatinfoById(int id) throws Exception {
		Chatinfo po =  chatinfoMapper.queryChatinfoById(id);
		return po;
	}
}

