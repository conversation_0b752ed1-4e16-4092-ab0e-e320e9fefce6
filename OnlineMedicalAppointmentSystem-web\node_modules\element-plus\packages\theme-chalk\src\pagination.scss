@use "sass:map";

@import 'mixins/mixins';
@import 'mixins/utils';
@import 'mixins/var';
@import 'common/var';
@import 'select';

@include b(pagination) {
  @include set-component-css-var('pagination', $--pagination);

  white-space: nowrap;
  padding: 2px 5px;
  color: var(--el-pagination-font-color);
  font-weight: bold;
  @include utils-clearfix;

  span:not([class*='suffix']),
  button {
    display: inline-block;
    font-size: var(--el-pagination-font-size);
    min-width: var(--el-pagination-button-width);
    height: var(--el-pagination-button-height);
    line-height: var(--el-pagination-button-height);
    vertical-align: top;
    box-sizing: border-box;
  }

  .#{$namespace}-input__inner {
    text-align: center;
    -moz-appearance: textfield;
    line-height: normal;
  }

  // pagesize 的下拉 icon
  .#{$namespace}-input__suffix {
    right: 0;
    transform: scale(0.8);
  }

  .#{$namespace}-select .#{$namespace}-input {
    width: 100px;
    margin: 0 5px;

    .#{$namespace}-input__inner {
      padding-right: 25px;
      border-radius: var(--el-pagination-border-radius);
    }
  }

  button {
    border: none;
    padding: 0 6px;
    background: transparent;

    &:focus {
      outline: none;
    }

    &:hover {
      color: var(--el-pagination-hover-color);
    }

    &:disabled {
      color: var(--el-pagination-button-disabled-color);
      background-color: var(--el-pagination-button-disabled-background-color);
      cursor: not-allowed;
    }
  }

  .btn-prev,
  .btn-next {
    background: center center no-repeat;
    background-size: 16px;
    background-color: var(--el-pagination-background-color);
    cursor: pointer;
    margin: 0;
    color: var(--el-pagination-button-color);

    .#{$namespace}-icon {
      display: block;
      font-size: 12px;
      font-weight: bold;
    }
  }

  .btn-prev {
    padding-right: 12px;
  }

  .btn-next {
    padding-left: 12px;
  }

  .#{$namespace}-pager li.disabled {
    color: var(--el-text-color-placeholder);
    cursor: not-allowed;
  }

  @include m(small) {
    .btn-prev,
    .btn-next,
    .#{$namespace}-pager li,
    .#{$namespace}-pager li.btn-quicknext,
    .#{$namespace}-pager li.btn-quickprev,
    .#{$namespace}-pager li:last-child {
      border-color: transparent;
      font-size: var(--el-font-size-extra-small);
      line-height: var(--el-pagination-line-height-extra-small);
      height: var(--el-pagination-height-extra-small);
      min-width: 22px;
    }

    .arrow.disabled {
      visibility: hidden;
    }

    .more::before,
    li.more::before {
      line-height: var(--el-pagination-line-height-extra-small);
    }

    span:not([class*='suffix']),
    button {
      height: var(--el-pagination-height-extra-small);
      line-height: var(--el-pagination-line-height-extra-small);
    }

    @include e(editor) {
      height: var(--el-pagination-line-height-extra-small);
      &.#{$namespace}-input .#{$namespace}-input__inner {
        height: var(--el-pagination-height-extra-small);
      }
    }

    .#{$namespace}-input__inner,
    .#{$namespace}-input--mini {
      height: var(--el-pagination-height-extra-small) !important;
      line-height: var(--el-pagination-line-height-extra-small);
    }

    .#{$namespace}-input__suffix {
      line-height: var(--el-pagination-line-height-extra-small);
      .#{$namespace}-input__suffix-inner {
        line-height: var(--el-pagination-line-height-extra-small);
        i.el-select__caret {
          line-height: var(--el-pagination-line-height-extra-small);
        }
      }
    }
  }

  @include e(sizes) {
    margin: 0 10px 0 0;
    font-weight: normal;
    color: var(--el-text-color-regular);

    .#{$namespace}-input .#{$namespace}-input__inner {
      font-size: var(--el-pagination-font-size);
      padding-left: 8px;

      &:hover {
        border-color: var(--el-pagination-hover-color);
      }
    }
  }

  @include e(total) {
    margin-right: 10px;
    font-weight: normal;
    color: var(--el-text-color-regular);
  }

  @include e(jump) {
    margin-left: 24px;
    font-weight: normal;
    color: var(--el-text-color-regular);

    .#{$namespace}-input__inner {
      padding: 0 3px;
    }
  }

  @include e(rightwrapper) {
    float: right;
  }

  @include e(editor) {
    line-height: 18px;
    padding: 0 2px;
    height: var(--el-pagination-button-height);

    text-align: center;
    margin: 0 2px;
    box-sizing: border-box;
    border-radius: var(--el-pagination-border-radius);

    &.#{$namespace}-input {
      width: 50px;
    }

    &.#{$namespace}-input .#{$namespace}-input__inner {
      height: var(--el-pagination-button-height);
    }

    .#{$namespace}-input__inner::-webkit-inner-spin-button,
    .#{$namespace}-input__inner::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }

  @include when(background) {
    .btn-prev,
    .btn-next,
    .#{$namespace}-pager li {
      margin: 0 5px;
      background-color: map.get($--colors, 'info', 'light-9');
      color: var(--el-text-color-regular);
      min-width: 30px;
      border-radius: 2px;

      &.disabled {
        color: var(--el-text-color-placeholder);
      }
    }

    .btn-prev,
    .btn-next {
      padding: 0;

      &:disabled {
        color: var(--el-text-color-placeholder);
      }
    }

    .#{$namespace}-pager li:not(.disabled) {
      &:hover {
        color: var(--el-pagination-hover-color);
      }

      &.active {
        background-color: var(--el-color-primary);
        color: var(--el-color-white);
      }
    }

    &.#{$namespace}-pagination--small {
      .btn-prev,
      .btn-next,
      .#{$namespace}-pager li {
        margin: 0 3px;
        min-width: 22px;
      }
    }
  }
}

@include b(pager) {
  user-select: none;
  list-style: none;
  display: inline-block;
  vertical-align: top;
  font-size: 0;
  padding: 0;
  margin: 0;

  .more::before {
    line-height: 30px;
  }

  li {
    padding: 0 4px;
    background: var(--el-pagination-background-color);
    vertical-align: top;
    display: inline-block;
    font-size: var(--el-pagination-font-size);
    min-width: var(--el-pagination-button-width);
    height: var(--el-pagination-button-height);
    line-height: var(--el-pagination-button-height);
    cursor: pointer;
    box-sizing: border-box;
    text-align: center;
    margin: 1px;

    &.btn-quicknext,
    &.btn-quickprev {
      line-height: 28px;
      color: var(--el-pagination-button-color);

      &.disabled {
        color: var(--el-text-color-placeholder);
      }
    }

    &.btn-quickprev:hover {
      cursor: pointer;
    }

    &.btn-quicknext:hover {
      cursor: pointer;
    }

    &.active + li {
      border-left: 0;
    }

    &:focus-visible {
      outline: 1px solid var(--el-pagination-hover-color);
    }

    &:hover {
      color: var(--el-pagination-hover-color);
    }

    &.active {
      color: var(--el-pagination-hover-color);
      cursor: default;
    }
  }
}
