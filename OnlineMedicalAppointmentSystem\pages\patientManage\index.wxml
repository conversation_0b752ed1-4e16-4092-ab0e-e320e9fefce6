<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText">返回</view>
        <view slot="content">就诊人管理</view>
    </diy-navbar>

    <view class="patient-list">
        <view class="empty-tip" wx:if="{{!loading && patients.length === 0}}">
            暂无就诊人信息
        </view>

        <view class="patient-item" wx:for="{{patients}}" wx:key="peoid">
            <view class="patient-info">
                <view class="name">{{item.peoname}}</view>
                <view class="detail">
                    <text>{{item.gender}}</text>
                    <text class="separator">|</text>
                    <text>{{item.age}}岁</text>
                    <text class="separator">|</text>
                    <text>{{item.phone}}</text>
                </view>
            </view>
            <view class="delete-btn" bindtap="deletePatient" data-id="{{item.peoid}}">
                删除
            </view>
        </view>
    </view>

    <view class="add-btn-container">
        <button class="diy-btn green" bindtap="goToAdd">添加就诊人</button>
    </view>
</view>