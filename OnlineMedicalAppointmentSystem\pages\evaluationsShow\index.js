const App = getApp();
Page({
  data: {
    url: App.Config.fileBasePath,
    msgs1: [],
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs1(); //获取数据列表
  },

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 2,
      eid: this.data.globalOption.id, //获取传递过来的id
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/evaluations_List").then((data) => {
      //调用服务器接口
      this.setData({
        msgs1: data.data, //把从服务器端得到的值赋值给数组
      });
    });
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
