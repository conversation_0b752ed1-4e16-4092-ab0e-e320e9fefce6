@import 'mixins/mixins';
@import 'common/var';

@include b(tabs) {
  @include e(header) {
    padding: 0;
    position: relative;
    margin: 0 0 15px;
  }
  @include e(active-bar) {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background-color: var(--el-color-primary);
    z-index: 1;
    transition: transform var(--el-transition-duration)
      cubic-bezier(0.645, 0.045, 0.355, 1);
    list-style: none;
  }
  @include e(new-tab) {
    float: right;
    border: 1px solid #d3dce6;
    height: 18px;
    width: 18px;
    line-height: 18px;
    margin: 12px 0 9px 10px;
    border-radius: 3px;
    text-align: center;
    font-size: 12px;
    color: #d3dce6;
    cursor: pointer;
    transition: all 0.15s;

    .#{$namespace}-icon-plus {
      transform: scale(0.8, 0.8);
    }

    &:hover {
      color: var(--el-color-primary);
    }
  }
  @include e(nav-wrap) {
    overflow: hidden;
    margin-bottom: -1px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: var(--el-border-color-light);
      z-index: var(--el-index-normal);
    }

    @include when(scrollable) {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }
  @include e(nav-scroll) {
    overflow: hidden;
  }
  @include e((nav-next, nav-prev)) {
    position: absolute;
    cursor: pointer;
    line-height: 44px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  @include e(nav-next) {
    right: 0;
  }
  @include e(nav-prev) {
    left: 0;
  }
  @include e(nav) {
    white-space: nowrap;
    position: relative;
    transition: transform var(--el-transition-duration);
    float: left;
    z-index: calc(var(--el-index-normal) + 1);

    @include when(stretch) {
      min-width: 100%;
      display: flex;
      & > * {
        flex: 1;
        text-align: center;
      }
    }
  }
  @include e(item) {
    padding: 0 20px;
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    position: relative;

    &:focus,
    &:focus:active {
      outline: none;
    }

    & .#{$namespace}-icon-close {
      border-radius: 50%;
      text-align: center;
      transition: all var(--el-transition-duration)
        cubic-bezier(0.645, 0.045, 0.355, 1);
      margin-left: 5px;
      &:before {
        transform: scale(0.9);
        display: inline-block;
      }

      &:hover {
        background-color: var(--el-text-color-placeholder);
        color: $--color-white;
      }
    }

    @include when(active) {
      color: var(--el-color-primary);
    }

    &:hover {
      color: var(--el-color-primary);
      cursor: pointer;
    }

    @include when(disabled) {
      color: $--disabled-color-base;
      cursor: default;
    }
  }
  @include e(content) {
    overflow: hidden;
    position: relative;
  }
  @include m(card) {
    > .#{$namespace}-tabs__header {
      border-bottom: 1px solid var(--el-border-color-light);
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav-wrap::after {
      content: none;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav {
      border: 1px solid var(--el-border-color-light);
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      box-sizing: border-box;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__active-bar {
      display: none;
    }
    > .#{$namespace}-tabs__header
      .#{$namespace}-tabs__item
      .#{$namespace}-icon-close {
      position: relative;
      font-size: 12px;
      width: 0;
      height: 14px;
      vertical-align: middle;
      line-height: 15px;
      overflow: hidden;
      top: -1px;
      right: -2px;
      transform-origin: 100% 50%;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__item {
      border-bottom: 1px solid transparent;
      border-left: 1px solid var(--el-border-color-light);
      transition: color var(--el-transition-duration)
          cubic-bezier(0.645, 0.045, 0.355, 1),
        padding var(--el-transition-duration)
          cubic-bezier(0.645, 0.045, 0.355, 1);
      &:first-child {
        border-left: none;
      }
      &.is-closable {
        &:hover {
          padding-left: 13px;
          padding-right: 13px;

          & .#{$namespace}-icon-close {
            width: 14px;
          }
        }
      }
      &.is-active {
        border-bottom-color: $--color-white;

        &.is-closable {
          padding-left: 20px;
          padding-right: 20px;

          .#{$namespace}-icon-close {
            width: 14px;
          }
        }
      }
    }
  }
  @include m(border-card) {
    background: $--color-white;
    border: 1px solid var(--el-border-color-base);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);

    > .#{$namespace}-tabs__content {
      padding: 15px;
    }
    > .#{$namespace}-tabs__header {
      background-color: $--background-color-base;
      border-bottom: 1px solid var(--el-border-color-light);
      margin: 0;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__nav-wrap::after {
      content: none;
    }
    > .#{$namespace}-tabs__header .#{$namespace}-tabs__item {
      transition: all var(--el-transition-duration)
        cubic-bezier(0.645, 0.045, 0.355, 1);
      border: 1px solid transparent;
      margin-top: -1px;
      color: var(--el-text-color-secondary);

      &:first-child {
        margin-left: -1px;
      }

      & + .#{$namespace}-tabs__item {
        margin-left: -1px;
      }

      &.is-active {
        color: var(--el-color-primary);
        background-color: $--color-white;
        border-right-color: var(--el-border-color-base);
        border-left-color: var(--el-border-color-base);
      }
      &:not(.is-disabled):hover {
        color: var(--el-color-primary);
      }
      &.is-disabled {
        color: $--disabled-color-base;
      }
    }

    > .#{$namespace}-tabs__header
      .is-scrollable
      .#{$namespace}-tabs__item:first-child {
      margin-left: 0;
    }
  }
  @include m((top, bottom)) {
    .#{$namespace}-tabs__item.is-top:nth-child(2),
    .#{$namespace}-tabs__item.is-bottom:nth-child(2) {
      padding-left: 0;
    }
    .#{$namespace}-tabs__item.is-top:last-child,
    .#{$namespace}-tabs__item.is-bottom:last-child {
      padding-right: 0;
    }

    &.#{$namespace}-tabs--border-card,
    &.#{$namespace}-tabs--card,
    .#{$namespace}-tabs--left,
    .#{$namespace}-tabs--right {
      > .#{$namespace}-tabs__header {
        .#{$namespace}-tabs__item:nth-child(2) {
          padding-left: 20px;
        }
        .#{$namespace}-tabs__item:last-child {
          padding-right: 20px;
        }
      }
    }
  }
  @include m(bottom) {
    .#{$namespace}-tabs__header.is-bottom {
      margin-bottom: 0;
      margin-top: 10px;
    }
    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-bottom {
        border-bottom: 0;
        border-top: 1px solid var(--el-border-color-base);
      }
      .#{$namespace}-tabs__nav-wrap.is-bottom {
        margin-top: -1px;
        margin-bottom: 0;
      }
      .#{$namespace}-tabs__item.is-bottom:not(.is-active) {
        border: 1px solid transparent;
      }
      .#{$namespace}-tabs__item.is-bottom {
        margin: 0 -1px -1px -1px;
      }
    }
  }
  @include m((left, right)) {
    overflow: hidden;

    .#{$namespace}-tabs__header.is-left,
    .#{$namespace}-tabs__header.is-right,
    .#{$namespace}-tabs__nav-wrap.is-left,
    .#{$namespace}-tabs__nav-wrap.is-right,
    .#{$namespace}-tabs__nav-scroll {
      height: 100%;
    }

    .#{$namespace}-tabs__active-bar.is-left,
    .#{$namespace}-tabs__active-bar.is-right {
      top: 0;
      bottom: auto;
      width: 2px;
      height: auto;
    }

    .#{$namespace}-tabs__nav-wrap.is-left,
    .#{$namespace}-tabs__nav-wrap.is-right {
      margin-bottom: 0;

      > .#{$namespace}-tabs__nav-prev,
      > .#{$namespace}-tabs__nav-next {
        height: 30px;
        line-height: 30px;
        width: 100%;
        text-align: center;
        cursor: pointer;

        i {
          transform: rotateZ(90deg);
        }
      }
      > .#{$namespace}-tabs__nav-prev {
        left: auto;
        top: 0;
      }
      > .#{$namespace}-tabs__nav-next {
        right: auto;
        bottom: 0;
      }

      &.is-scrollable {
        padding: 30px 0;
      }

      &::after {
        height: 100%;
        width: 2px;
        bottom: auto;
        top: 0;
      }
    }

    .#{$namespace}-tabs__nav.is-left,
    .#{$namespace}-tabs__nav.is-right {
      float: none;
    }
    .#{$namespace}-tabs__item.is-left,
    .#{$namespace}-tabs__item.is-right {
      display: block;
    }
  }
  @include m(left) {
    .#{$namespace}-tabs__header.is-left {
      float: left;
      margin-bottom: 0;
      margin-right: 10px;
    }
    .#{$namespace}-tabs__nav-wrap.is-left {
      margin-right: -1px;
      &::after {
        left: auto;
        right: 0;
      }
    }
    .#{$namespace}-tabs__active-bar.is-left {
      right: 0;
      left: auto;
    }
    .#{$namespace}-tabs__item.is-left {
      text-align: right;
    }

    &.#{$namespace}-tabs--card {
      .#{$namespace}-tabs__active-bar.is-left {
        display: none;
      }
      .#{$namespace}-tabs__item.is-left {
        border-left: none;
        border-right: 1px solid var(--el-border-color-light);
        border-bottom: none;
        border-top: 1px solid var(--el-border-color-light);
        text-align: left;
      }
      .#{$namespace}-tabs__item.is-left:first-child {
        border-right: 1px solid var(--el-border-color-light);
        border-top: none;
      }
      .#{$namespace}-tabs__item.is-left.is-active {
        border: 1px solid var(--el-border-color-light);
        border-right-color: #fff;
        border-left: none;
        border-bottom: none;

        &:first-child {
          border-top: none;
        }
        &:last-child {
          border-bottom: none;
        }
      }

      .#{$namespace}-tabs__nav {
        border-radius: 4px 0 0 4px;
        border-bottom: 1px solid var(--el-border-color-light);
        border-right: none;
      }

      .#{$namespace}-tabs__new-tab {
        float: none;
      }
    }

    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-left {
        border-right: 1px solid #dfe4ed;
      }
      .#{$namespace}-tabs__item.is-left {
        border: 1px solid transparent;
        margin: -1px 0 -1px -1px;

        &.is-active {
          border-color: transparent;
          border-top-color: rgb(209, 219, 229);
          border-bottom-color: rgb(209, 219, 229);
        }
      }
    }
  }
  @include m(right) {
    .#{$namespace}-tabs__header.is-right {
      float: right;
      margin-bottom: 0;
      margin-left: 10px;
    }

    .#{$namespace}-tabs__nav-wrap.is-right {
      margin-left: -1px;
      &::after {
        left: 0;
        right: auto;
      }
    }

    .#{$namespace}-tabs__active-bar.is-right {
      left: 0;
    }

    &.#{$namespace}-tabs--card {
      .#{$namespace}-tabs__active-bar.is-right {
        display: none;
      }
      .#{$namespace}-tabs__item.is-right {
        border-bottom: none;
        border-top: 1px solid var(--el-border-color-light);
      }
      .#{$namespace}-tabs__item.is-right:first-child {
        border-left: 1px solid var(--el-border-color-light);
        border-top: none;
      }
      .#{$namespace}-tabs__item.is-right.is-active {
        border: 1px solid var(--el-border-color-light);
        border-left-color: #fff;
        border-right: none;
        border-bottom: none;

        &:first-child {
          border-top: none;
        }
        &:last-child {
          border-bottom: none;
        }
      }

      .#{$namespace}-tabs__nav {
        border-radius: 0 4px 4px 0;
        border-bottom: 1px solid var(--el-border-color-light);
        border-left: none;
      }
    }
    &.#{$namespace}-tabs--border-card {
      .#{$namespace}-tabs__header.is-right {
        border-left: 1px solid #dfe4ed;
      }
      .#{$namespace}-tabs__item.is-right {
        border: 1px solid transparent;
        margin: -1px -1px -1px 0;

        &.is-active {
          border-color: transparent;
          border-top-color: rgb(209, 219, 229);
          border-bottom-color: rgb(209, 219, 229);
        }
      }
    }
  }
}

.slideInRight-transition,
.slideInLeft-transition {
  display: inline-block;
}
.slideInRight-enter {
  animation: slideInRight-enter var(--el-transition-duration);
}
.slideInRight-leave {
  position: absolute;
  left: 0;
  right: 0;
  animation: slideInRight-leave var(--el-transition-duration);
}
.slideInLeft-enter {
  animation: slideInLeft-enter var(--el-transition-duration);
}
.slideInLeft-leave {
  position: absolute;
  left: 0;
  right: 0;
  animation: slideInLeft-leave var(--el-transition-duration);
}

@keyframes slideInRight-enter {
  0% {
    opacity: 0;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes slideInRight-leave {
  0% {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    opacity: 0;
  }
}
@keyframes slideInLeft-enter {
  0% {
    opacity: 0;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }

  to {
    opacity: 1;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes slideInLeft-leave {
  0% {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    opacity: 0;
  }
}
