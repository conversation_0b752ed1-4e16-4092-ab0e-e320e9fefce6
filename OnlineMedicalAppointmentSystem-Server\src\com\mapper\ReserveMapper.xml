<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ReserveMapper">
	<select id="findReserveList"  resultType="Reserve">
		select * from reserve 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Reserve">
	    select  *  
        from reserve a  left join parts b on a.pid=b.pid  	
		<where>
      		<if test="rid != null and rid !=0 ">
		    and a.rid = #{rid}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="plid != null and plid !=0 ">
		    and a.plid = #{plid}
		</if>
		<if test="rdate != null and rdate != ''">
		    and a.rdate = #{rdate}
		</if>
		<if test="rtime != null and rtime != ''">
		    and a.rtime = #{rtime}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="peoid != null and peoid !=0 ">
		    and a.peoid = #{peoid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="flag != null and flag != ''">
		    and a.flag = #{flag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} rid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from reserve a  left join parts b on a.pid=b.pid  
		<where>
      		<if test="rid != null and rid !=0 ">
		    and a.rid = #{rid}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="plid != null and plid !=0 ">
		    and a.plid = #{plid}
		</if>
		<if test="rdate != null and rdate != ''">
		    and a.rdate = #{rdate}
		</if>
		<if test="rtime != null and rtime != ''">
		    and a.rtime = #{rtime}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="peoid != null and peoid !=0 ">
		    and a.peoid = #{peoid}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="flag != null and flag != ''">
		    and a.flag = #{flag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryReserveById" parameterType="int" resultType="Reserve">
    select  *  
     from reserve a  left join parts b on a.pid=b.pid  	 where a.rid=#{value}
  </select>
 
	<insert id="insertReserve" useGeneratedKeys="true" keyProperty="rid" parameterType="Reserve">
    insert into reserve
    (pid,did,plid,rdate,rtime,pmoney,lname,peoid,addtime,flag,results)
    values
    (#{pid},#{did},#{plid},#{rdate},#{rtime},#{pmoney},#{lname},#{peoid},now(),#{flag},#{results});
  </insert>
	
	<update id="updateReserve" parameterType="Reserve" >
    update reserve 
    <set>
		<if test="pid != null ">
		    pid = #{pid},
		</if>
		<if test="did != null ">
		    did = #{did},
		</if>
		<if test="plid != null ">
		    plid = #{plid},
		</if>
		<if test="rdate != null and rdate != ''">
		    rdate = #{rdate},
		</if>
		<if test="rtime != null and rtime != ''">
		    rtime = #{rtime},
		</if>
		<if test="pmoney != null ">
		    pmoney = #{pmoney},
		</if>
		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="peoid != null ">
		    peoid = #{peoid},
		</if>
		<if test="addtime != null and addtime != ''">
		    addtime = #{addtime},
		</if>
		<if test="flag != null and flag != ''">
		    flag = #{flag},
		</if>
		<if test="results != null and results != ''">
		    results = #{results},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="rid != null or rid != ''">
      rid=#{rid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteReserve" parameterType="int">
    delete from  reserve where rid=#{value}
  </delete>

	
	
</mapper>

 
