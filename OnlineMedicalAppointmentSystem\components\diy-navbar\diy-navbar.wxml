

<view class="cu-custom" style="height:{{CustomBar}}px">
  <view class="cu-bar fixed {{bgImage!=''?'none-bg text-white bg-img':''}} {{bgColor}}" style="height:{{CustomBar}}px;padding-top:{{StatusBar}}px;{{bgImage?'background-image:url(' + bgImage+')':''}}">
    <view class="action" bindtap="BackPage" wx:if="{{isBack}}">
      <text class="diy-icon-back"></text>
      <slot name="backText"></slot>
    </view>
    <view class="action border-custom"  wx:if="{{isCustom}}" style="width:{{Custom.width}}px;height:{{Custom.height}}px;margin-left:calc(750rpx - {{Custom.right}}px)">
      <text class="cuIcon-back" bindtap="BackPage"></text>
      <text class="cuIcon-homefill" bindtap="toHome"></text>
    </view>
    <view class="content" style="top:{{StatusBar}}px">
      <slot name="content"></slot>
    </view>
    <slot name="right"></slot>
  </view>
</view>