{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\WangEditor.vue"], "names": [], "mappings": ";AAUA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;EACH,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;EACH,CAAC;;;EAGD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7D,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACpB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,EAAE;QACL,CAAC;MACH,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;QACH;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAGtC,CAAC;EACH,CAAC;AACH,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/WangEditor.vue", "sourceRoot": "", "sourcesContent": ["<template lang=\"html\">\r\n  <div class=\"editor\">\r\n    <div ref=\"toolbar\" class=\"toolbar1\">\r\n    </div>\r\n    <div ref=\"editor\" class=\"text\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport E from \"wangeditor\";\r\nimport request, { base } from \"../../utils/http\";\r\n\r\nexport default {\r\n  name: \"editorItem\",\r\n  data() {\r\n    return {\r\n      // uploadPath,\r\n      editor: null,\r\n      info_: null,\r\n    };\r\n  },\r\n\r\n  props: {\r\n    modelValue: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    isClear: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n\r\n\r\n  watch: {\r\n    isClear(val) {\r\n      // 触发清除文本域内容\r\n      if (val) {\r\n        this.editor.txt.clear();\r\n        this.info_ = null;\r\n      }\r\n    },\r\n    modelValue: function (value) {\r\n      console.log(value);\r\n      if (value !== this.editor.txt.html()) {\r\n        this.editor.txt.html(this.value);\r\n      }\r\n    },\r\n    //value为编辑框输入的内容，这里我监听了一下值，当父组件调用得时候，如果给value赋值了，子组件将会显示父组件赋给的值\r\n  },\r\n\r\n  mounted() {\r\n    this.seteditor();\r\n    this.editor.txt.html(this.modelValue);\r\n  },\r\n  methods: {\r\n    seteditor() {\r\n      this.editor = new E(this.$refs.toolbar, this.$refs.editor);\r\n      this.editor.config.uploadImgShowBase64 = false; // base 64 存储图片\r\n      this.editor.config.uploadImgServer = base + \"/common/uploadFile\"; // 配置服务器端地址\r\n      this.editor.config.uploadImgHeaders = {}; // 自定义 header\r\n      this.editor.config.uploadFileName = \"file\"; // 后端接受上传文件的参数名\r\n      this.editor.config.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M\r\n      this.editor.config.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片\r\n      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间\r\n\r\n      // 配置菜单\r\n      this.editor.config.menus = [\r\n        \"head\", // 标题\r\n        \"bold\", // 粗体\r\n        \"fontSize\", // 字号\r\n        \"fontName\", // 字体\r\n        \"italic\", // 斜体\r\n        \"underline\", // 下划线\r\n        \"strikeThrough\", // 删除线\r\n        \"foreColor\", // 文字颜色\r\n        \"backColor\", // 背景颜色\r\n        \"link\", // 插入链接\r\n        \"list\", // 列表\r\n        \"justify\", // 对齐方式\r\n        \"quote\", // 引用\r\n        \"emoticon\", // 表情\r\n        \"image\", // 插入图片\r\n        \"table\", // 表格\r\n        \"video\", // 插入视频\r\n        \"code\", // 插入代码\r\n        \"undo\", // 撤销\r\n        \"redo\", // 重复\r\n        \"fullscreen\", // 全屏\r\n      ];\r\n\r\n      this.editor.config.uploadImgHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 插入图片失败回调\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 图片上传成功回调\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 网络超时的回调\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 图片上传错误的回调\r\n        },\r\n        customInsert: (insertImg, result, editor) => {\r\n          // 图片上传成功，插入图片的回调\r\n          //result为上传图片成功的时候返回的数据，这里我打印了一下发现后台返回的是data：[{url:\"路径的形式\"},...]\r\n          // console.log(result.data[0].url)\r\n          //insertImg()为插入图片的函数\r\n          //循环插入图片\r\n          // for (let i = 0; i < 1; i++) {\r\n          console.log(result);\r\n          let url = result.resdata.url;\r\n          insertImg(url);\r\n          // }\r\n        },\r\n      };\r\n\r\n      // 在menus配置后添加视频上传相关配置\r\n      this.editor.config.uploadVideoServer = base + \"/common/uploadFile\"; // 视频上传服务器地址\r\n      this.editor.config.uploadVideoName = \"file\"; // 视频上传参数名\r\n      this.editor.config.uploadVideoMaxSize = 100 * 1024 * 1024; // 限制视频大小为100M\r\n      this.editor.config.uploadVideoAccept = ['mp4', 'mov', 'avi', 'wmv']; // 限制视频格式\r\n\r\n      // 添加视频上传的钩子函数\r\n      this.editor.config.uploadVideoHooks = {\r\n        fail: (xhr, editor, result) => {\r\n          // 视频上传失败的回调\r\n          console.log('视频上传失败', result)\r\n        },\r\n        success: (xhr, editor, result) => {\r\n          // 视频上传成功的回调\r\n          console.log('视频上传成功', result)\r\n        },\r\n        error: (xhr, editor) => {\r\n          // 视频上传错误的回调\r\n          console.log('视频上传错误')\r\n        },\r\n        timeout: (xhr, editor) => {\r\n          // 视频上传超时的回调\r\n          console.log('视频上传超时')\r\n        },\r\n        customInsert: (insertVideo, result) => {\r\n          console.log('插入视频', result)\r\n          let url = result.resdata.url\r\n          // 设置视频的显示样式\r\n          insertVideo(url, null, {\r\n            width: '100%',\r\n            height: 'auto',\r\n            controls: 'controls'\r\n          })\r\n        }\r\n      }\r\n\r\n      // 配置视频上传的校验\r\n      this.editor.config.uploadVideoCheck = function (videoFile) {\r\n        // 视频大小限制\r\n        if (videoFile.size > 100 * 1024 * 1024) {\r\n          return '视频大小不能超过100M'\r\n        }\r\n        return true\r\n      }\r\n\r\n      this.editor.config.onchange = (html) => {\r\n        this.info_ = html; // 绑定当前组件的值\r\n        this.$emit(\"change\", this.info_); // 将内容同步到父组件中\r\n      };\r\n      // 创建富文本编辑器\r\n      this.editor.create();\r\n      this.editor.txt.html(this.modelValue)\r\n\r\n\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"css\">\r\n.editor {\r\n  width: 100%;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  z-index: 0;\r\n}\r\n\r\n.toolbar1 {\r\n  border: 1px solid #ccc;\r\n}\r\n\r\n.text {\r\n  border: 1px solid #ccc;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n\r\n"]}]}