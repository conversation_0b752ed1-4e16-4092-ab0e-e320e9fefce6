package com.service;
import java.util.List;

import com.model.Patient;
import com.util.PageBean;

public interface PatientService{
	
	//查询多条记录
	public List<Patient> queryPatientList(Patient patient,PageBean page) throws Exception;
 
	//添加
	public int insertPatient(Patient patient) throws Exception ;
	
	//根据ID删除
	public int deletePatient(int id) throws Exception ;
	
	//更新
	public int updatePatient(Patient patient) throws Exception ;
	
	//根据ID查询单条数据
	public Patient queryPatientById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Patient patient);

}

