const App = getApp();
Page({
  data: {
    msgs1: [],
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
    this.getMsgs1();
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {},

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 2,
      id: this.data.globalOption.id,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/news_List").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.data, //把从服务器端得到的值赋值给数组
      });
    });
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
