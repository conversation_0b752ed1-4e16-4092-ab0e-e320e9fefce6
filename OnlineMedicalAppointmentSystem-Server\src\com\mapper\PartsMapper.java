package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Parts;

public interface PartsMapper {

	//返回所有记录
	public List<Parts> findPartsList();
	
	//查询多条记录
	public List<Parts> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertParts(Parts parts);

	//根据ID删除
	public int deleteParts(int id);
	
	//更新
	public int updateParts(Parts parts);
	
	//根据ID得到对应的记录
	public Parts queryPartsById(int id);
	
}

