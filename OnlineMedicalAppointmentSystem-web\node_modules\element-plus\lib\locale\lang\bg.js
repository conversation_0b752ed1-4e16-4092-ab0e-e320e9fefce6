'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var bg = {
    name: 'bg',
    el: {
        colorpicker: {
            confirm: 'OK',
            clear: 'Изчисти',
        },
        datepicker: {
            now: 'Сега',
            today: 'Днес',
            cancel: 'Откажи',
            clear: 'Изчисти',
            confirm: 'ОК',
            selectDate: 'Избери дата',
            selectTime: 'Избери час',
            startDate: 'Начална дата',
            startTime: 'Начален час',
            endDate: 'Крайна дата',
            endTime: 'Краен час',
            prevYear: 'Previous Year',
            nextYear: 'Next Year',
            prevMonth: 'Previous Month',
            nextMonth: 'Next Month',
            year: '',
            month1: 'Януари',
            month2: 'Февруари',
            month3: 'Март',
            month4: 'Април',
            month5: 'Май',
            month6: 'Юни',
            month7: 'Юли',
            month8: 'Август',
            month9: 'Септември',
            month10: 'Октомври',
            month11: 'Ноември',
            month12: 'Декември',
            weeks: {
                sun: 'Нед',
                mon: 'Пон',
                tue: 'Вто',
                wed: 'Сря',
                thu: 'Чет',
                fri: 'Пет',
                sat: 'Съб',
            },
            months: {
                jan: 'Яну',
                feb: 'Фев',
                mar: 'Мар',
                apr: 'Апр',
                may: 'Май',
                jun: 'Юни',
                jul: 'Юли',
                aug: 'Авг',
                sep: 'Сеп',
                oct: 'Окт',
                nov: 'Ное',
                dec: 'Дек',
            },
        },
        select: {
            loading: 'Зареждане',
            noMatch: 'Няма намерени',
            noData: 'Няма данни',
            placeholder: 'Избери',
        },
        cascader: {
            noMatch: 'Няма намерени',
            loading: 'Зареждане',
            placeholder: 'Избери',
            noData: 'Няма данни',
        },
        pagination: {
            goto: 'Иди на',
            pagesize: '/страница',
            total: 'Общо {total}',
            pageClassifier: '',
        },
        messagebox: {
            title: 'Съобщение',
            confirm: 'ОК',
            cancel: 'Откажи',
            error: 'Невалидни данни',
        },
        upload: {
            deleteTip: 'press delete to remove',
            delete: 'Изтрий',
            preview: 'Прегледай',
            continue: 'Продължи',
        },
        table: {
            emptyText: 'Няма данни',
            confirmFilter: 'Потвърди',
            resetFilter: 'Изчисти',
            clearFilter: 'Всички',
            sumText: 'Sum',
        },
        tree: {
            emptyText: 'Няма данни',
        },
        transfer: {
            noMatch: 'Няма намерени',
            noData: 'Няма данни',
            titles: ['List 1', 'List 2'],
            filterPlaceholder: 'Enter keyword',
            noCheckedFormat: '{total} items',
            hasCheckedFormat: '{checked}/{total} checked',
        },
        image: {
            error: 'FAILED',
        },
        pageHeader: {
            title: 'Back',
        },
        popconfirm: {
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
        },
    },
};

exports.default = bg;
