{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue?vue&type=template&id=c1f1971a&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "id", "href", "_createBlock", "_component_el_config_provider", "locale", "$data", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_Header", "_component_LeftMenu", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$route", "meta", "title", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_component_router_view"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n\r\n<el-config-provider :locale=\"locale\">\r\n\r\n  <div class=\"main-wrapper\" id=\"main-wrapper\">\r\n\r\n\r\n  <Header />\r\n  <LeftMenu />\r\n\r\n  <div class=\"content-body\">\r\n        <!-- row -->\t\r\n  <div class=\"page-titles\">\r\n    <ol class=\"breadcrumb\">\r\n      <li><h5 class=\"bc-title\" id=\"title1\">{{ this.$route.meta.title }}</h5></li>\r\n      <li class=\"breadcrumb-item\"><a href=\"/main\">\r\n        <svg width=\"17\" height=\"17\" viewBox=\"0 0 17 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M2.125 6.375L8.5 1.41667L14.875 6.375V14.1667C14.875 14.5424 14.7257 14.9027 14.4601 15.1684C14.1944 15.4341 13.8341 15.5833 13.4583 15.5833H3.54167C3.16594 15.5833 2.80561 15.4341 2.53993 15.1684C2.27426 14.9027 2.125 14.5424 2.125 14.1667V6.375Z\" stroke=\"#2C2C2C\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          <path d=\"M6.375 15.5833V8.5H10.625V15.5833\" stroke=\"#2C2C2C\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        </svg>\r\n        首页 </a>\r\n      </li>\r\n      <li class=\"breadcrumb-item active\"><a href=\"javascript:void(0)\" id=\"title2\">{{ this.$route.meta.title }}</a></li>\r\n    </ol>\r\n    \r\n  </div>\r\n  <div class=\"container-fluid\">\r\n    <router-view />    \r\n  </div>\r\n </div>\r\n\r\n\r\n\r\n</div>\r\n</el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\nimport $ from 'jquery';\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style scoped>\r\n@import url(../assets/css/h_style.css);\r\n</style>\r\n\r\n"], "mappings": ";;EAIOA,KAAK,EAAC,cAAc;EAACC,EAAE,EAAC;;;EAMxBD,KAAK,EAAC;AAAc;;EAEpBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAY;;EACZA,KAAK,EAAC,UAAU;EAACC,EAAE,EAAC;;;EAQxBD,KAAK,EAAC;AAAwB;;EAAIE,IAAI,EAAC,oBAAoB;EAACD,EAAE,EAAC;;;EAIlED,KAAK,EAAC;AAAiB;;;;;;uBAxB9BG,YAAA,CAgCqBC,6BAAA;IAhCAC,MAAM,EAAEC,KAAA,CAAAD;EAAM;sBAEjC,MA6BI,CA7BJE,mBAAA,CA6BI,OA7BJC,UA6BI,GA1BJC,YAAA,CAAUC,iBAAA,GACVD,YAAA,CAAYE,mBAAA,GAEZJ,mBAAA,CAmBK,OAnBLK,UAmBK,GAlBCC,mBAAA,SAAY,EAClBN,mBAAA,CAaM,OAbNO,UAaM,GAZJP,mBAAA,CAUK,MAVLQ,UAUK,GATHR,mBAAA,CAA2E,aAAvEA,mBAAA,CAAkE,MAAlES,UAAkE,EAAAC,gBAAA,MAAzBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,6BAC9Db,mBAAA,CAMK;MANDP,KAAK,EAAC;IAAiB,IAACO,mBAAA,CAKnB;MALsBL,IAAI,EAAC;IAAO,IACzCK,mBAAA,CAGM;MAHDc,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC;QAChElB,mBAAA,CAAmU;MAA7TmB,CAAC,EAAC,yPAAyP;MAACC,MAAM,EAAC,SAAS;MAAC,gBAAc,EAAC,OAAO;MAAC,iBAAe,EAAC;QAC1TpB,mBAAA,CAA6G;MAAvGmB,CAAC,EAAC,mCAAmC;MAACC,MAAM,EAAC,SAAS;MAAC,gBAAc,EAAC,OAAO;MAAC,iBAAe,EAAC;2BAChG,MACH,E,wBAELpB,mBAAA,CAAiH,MAAjHqB,UAAiH,GAA9ErB,mBAAA,CAAyE,KAAzEsB,UAAyE,EAAAZ,gBAAA,MAAxBC,MAAM,CAACC,IAAI,CAACC,KAAK,iB,OAIzGb,mBAAA,CAEM,OAFNuB,UAEM,GADJrB,YAAA,CAAesB,sBAAA,E", "ignoreList": []}]}