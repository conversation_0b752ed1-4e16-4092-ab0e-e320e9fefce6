@use "sass:map";

@import 'mixins/mixins';
@import 'checkbox';
@import 'tag';
@import 'common/var';

@include b(table-column) {
  @include m(selection) {
    .cell {
      padding-left: 14px;
      padding-right: 14px;
    }
  }
}

@include b(table-filter) {
  border: solid 1px var(--el-border-color-lighter);
  border-radius: 2px;
  background-color: $--color-white;
  box-shadow: map.get($--dropdown, 'menu-box-shadow');
  box-sizing: border-box;
  margin: 2px 0;

  /** used for dropdown mode */
  @include e(list) {
    padding: 5px 0;
    margin: 0;
    list-style: none;
    min-width: 100px;
  }

  @include e(list-item) {
    line-height: 36px;
    padding: 0 10px;
    cursor: pointer;
    font-size: var(--el-font-size-base);

    &:hover {
      background-color: map.get($--dropdown, 'menuItem-hover-fill');
      color: map.get($--dropdown, 'menuItem-hover-color');
    }

    @include when(active) {
      background-color: var(--el-color-primary);
      color: $--color-white;
    }
  }

  @include e(content) {
    min-width: 100px;
  }

  @include e(bottom) {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 8px;

    button {
      background: transparent;
      border: none;
      color: var(--el-text-color-regular);
      cursor: pointer;
      font-size: var(--el-font-size-small);
      padding: 0 3px;

      &:hover {
        color: var(--el-color-primary);
      }

      &:focus {
        outline: none;
      }

      &.is-disabled {
        color: $--disabled-color-base;
        cursor: not-allowed;
      }
    }
  }

  @include e(wrap) {
    max-height: 280px;
  }

  @include e(checkbox-group) {
    padding: 10px;

    label.#{$namespace}-checkbox {
      display: block;
      margin-right: 5px;
      margin-bottom: 8px;
      margin-left: 5px;
    }

    .#{$namespace}-checkbox:last-child {
      margin-bottom: 0;
    }
  }
}
