<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 我的收藏 </view>
    </diy-navbar>
    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="diy-col-24 rows">
            <view class="diy-col-6">
                <image src="{{url}}{{item.by2}}" mode="aspectFit|aspectFill|widthFix" style="height:90px;" />
            </view>
            <view class="diy-col-18  padding-left-sm ">
                <view class="text-lg">{{item.by1}}</view>
                <view class="grid col-1">

                    <view>收藏时间：{{item.ctime}}</view>

                </view>
                <view class="diy-col-24 ">
                    <view class="cu-tag bg-red  fr round" style="margin-left:10rpx" data-id="{{item.cid}}"
                        catch:tap="dele">删除</view>
                    <view class="cu-tag bg-blue  fr round" bindtap="navigateTo" data-url="doctorView"
                        data-id="{{item.did}}">查看</view>
                </view>
            </view>
        </view>
    </block>
    <view class="clearfix"></view>
</view>