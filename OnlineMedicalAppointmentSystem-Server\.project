<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>OnlineMedicalAppointmentSystem</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.wst.common.project.facet.core.builder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.ui.externaltools.ExternalToolBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
				<dictionary>
					<key>LaunchConfigHandle</key>
					<value>&lt;project&gt;/.externalToolBuilders/org.eclipse.wst.jsdt.core.javascriptValidator (1).launch</value>
				</dictionary>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.j2eedt.core.WebClasspathBuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.jdt.core.javabuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.j2eedt.core.J2EEProjectValidator</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.j2eedt.core.DeploymentDescriptorValidator</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.wst.validation.validationbuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.ast.deploy.core.DeploymentBuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.springframework.springbuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>com.genuitec.eclipse.hibernate.HibernateBuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.genuitec.eclipse.hibernate.hibernatenature</nature>
		<nature>com.genuitec.eclipse.springframework.springnature</nature>
		<nature>com.genuitec.eclipse.ast.deploy.core.deploymentnature</nature>
		<nature>org.eclipse.jem.workbench.JavaEMFNature</nature>
		<nature>com.genuitec.eclipse.j2eedt.core.webnature</nature>
		<nature>org.eclipse.jdt.core.javanature</nature>
		<nature>org.eclipse.wst.jsdt.core.jsNature</nature>
		<nature>org.eclipse.wst.common.project.facet.core.nature</nature>
		<nature>org.eclipse.wst.common.modulecore.ModuleCoreNature</nature>
	</natures>
	<filteredResources>
		<filter>
			<id>1701335431974</id>
			<name></name>
			<type>30</type>
			<matcher>
				<id>org.eclipse.core.resources.regexFilterMatcher</id>
				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
