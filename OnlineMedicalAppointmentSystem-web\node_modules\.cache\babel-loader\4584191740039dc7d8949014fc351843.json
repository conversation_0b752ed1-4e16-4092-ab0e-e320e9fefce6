{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\utils\\http.js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\utils\\http.js", "mtime": 1749193949006}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKLy9heGlvcy5kZWZhdWx0cy53aXRoQ3JlZGVudGlhbHMgPSB0cnVlCmV4cG9ydCBjb25zdCBiYXNlID0gJ2h0dHA6Ly8xMjcuMC4wLjE6ODA4OC9PbmxpbmVNZWRpY2FsQXBwb2ludG1lbnRTeXN0ZW1fU2VydmVyL2FwaSc7CmNvbnN0IHJlcXVlc3QgPSBheGlvcy5jcmVhdGUoewogIHRpbWVvdXQ6IDUwMDAKfSk7CnJlcXVlc3QuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKGNvbmZpZyA9PiB7CiAgY29uZmlnLmhlYWRlcnNbJ0NvbnRlbnQtVHlwZSddID0gJ2FwcGxpY2F0aW9uL2pzb247Y2hhcnNldD11dGYtOCc7CiAgLy8gY29uZmlnLmhlYWRlcnNbJ3Rva2VuJ10gPSB1c2VyLnRva2VuOyAgLy8g6K6+572u6K+35rGC5aS0CiAgaWYgKGNvbmZpZy51cmwuaW5jbHVkZXMoJz8nKSkgewogICAgbGV0IGluZGV4ID0gY29uZmlnLnVybC5pbmRleE9mKCc/Jyk7CiAgICBsZXQgbmV3VXJsID0gY29uZmlnLnVybC5zdWJzdHJpbmcoMCwgaW5kZXgpICsgJy5hY3Rpb24nICsgY29uZmlnLnVybC5zdWJzdHJpbmcoaW5kZXgpOwogICAgLy8g5pu05pawY29uZmlnLnVybAogICAgY29uZmlnLnVybCA9IG5ld1VybDsKICB9IGVsc2UgewogICAgY29uZmlnLnVybCArPSAnLmFjdGlvbic7CiAgfQogIHJldHVybiBjb25maWc7Cn0sIGVycm9yID0+IHsKICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpOwp9KTsKCi8vIOWPr+S7peWcqOaOpeWPo+WTjeW6lOWQjue7n+S4gOWkhOeQhue7k+aenApyZXF1ZXN0LmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UocmVzcG9uc2UgPT4gewogIGxldCByZXMgPSByZXNwb25zZS5kYXRhOwogIC8vIOWmguaenOaYr+i/lOWbnueahOaWh+S7tgogIGlmIChyZXNwb25zZS5jb25maWcucmVzcG9uc2VUeXBlID09PSAnYmxvYicpIHsKICAgIHJldHVybiByZXM7CiAgfQogIC8vIOWFvOWuueacjeWKoeerr+i/lOWbnueahOWtl+espuS4suaVsOaNrgogIGlmICh0eXBlb2YgcmVzID09PSAnc3RyaW5nJykgewogICAgcmVzID0gcmVzID8gSlNPTi5wYXJzZShyZXMpIDogcmVzOwogIH0KICByZXR1cm4gcmVzOwp9LCBlcnJvciA9PiB7CiAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTsKfSk7CmV4cG9ydCBkZWZhdWx0IHJlcXVlc3Q7"}, {"version": 3, "names": ["axios", "base", "request", "create", "timeout", "interceptors", "use", "config", "headers", "url", "includes", "index", "indexOf", "newUrl", "substring", "error", "Promise", "reject", "response", "res", "data", "responseType", "JSON", "parse"], "sources": ["I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/utils/http.js"], "sourcesContent": ["import axios from 'axios';\r\n//axios.defaults.withCredentials = true\r\nexport const base = 'http://127.0.0.1:8088/OnlineMedicalAppointmentSystem_Server/api';\r\n\r\nconst request = axios.create({\r\n    timeout: 5000\r\n})\r\n\r\nrequest.interceptors.request.use(config => {\r\n    config.headers['Content-Type'] = 'application/json;charset=utf-8';\r\n    // config.headers['token'] = user.token;  // 设置请求头\r\n    if (config.url.includes('?')) {\r\n        let index = config.url.indexOf('?');\r\n        let newUrl = config.url.substring(0, index) + '.action' + config.url.substring(index);\r\n        // 更新config.url\r\n        config.url = newUrl;\r\n    } else {\r\n        config.url += '.action';\r\n    }\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n});\r\n\r\n// 可以在接口响应后统一处理结果\r\nrequest.interceptors.response.use(\r\n    response => {\r\n        let res = response.data;\r\n        // 如果是返回的文件\r\n        if (response.config.responseType === 'blob') {\r\n            return res\r\n        }\r\n        // 兼容服务端返回的字符串数据\r\n        if (typeof res === 'string') {\r\n            res = res ? JSON.parse(res) : res\r\n        }\r\n        return res;\r\n    },\r\n    error => {\r\n        return Promise.reject(error)\r\n    }\r\n)\r\n\r\nexport default request\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAO,MAAMC,IAAI,GAAG,iEAAiE;AAErF,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EACzBC,OAAO,EAAE;AACb,CAAC,CAAC;AAEFF,OAAO,CAACG,YAAY,CAACH,OAAO,CAACI,GAAG,CAACC,MAAM,IAAI;EACvCA,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;EACjE;EACA,IAAID,MAAM,CAACE,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,IAAIC,KAAK,GAAGJ,MAAM,CAACE,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC;IACnC,IAAIC,MAAM,GAAGN,MAAM,CAACE,GAAG,CAACK,SAAS,CAAC,CAAC,EAAEH,KAAK,CAAC,GAAG,SAAS,GAAGJ,MAAM,CAACE,GAAG,CAACK,SAAS,CAACH,KAAK,CAAC;IACrF;IACAJ,MAAM,CAACE,GAAG,GAAGI,MAAM;EACvB,CAAC,MAAM;IACHN,MAAM,CAACE,GAAG,IAAI,SAAS;EAC3B;EACA,OAAOF,MAAM;AACjB,CAAC,EAAEQ,KAAK,IAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;;AAEF;AACAb,OAAO,CAACG,YAAY,CAACa,QAAQ,CAACZ,GAAG,CAC7BY,QAAQ,IAAI;EACR,IAAIC,GAAG,GAAGD,QAAQ,CAACE,IAAI;EACvB;EACA,IAAIF,QAAQ,CAACX,MAAM,CAACc,YAAY,KAAK,MAAM,EAAE;IACzC,OAAOF,GAAG;EACd;EACA;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GAAGA,GAAG;EACrC;EACA,OAAOA,GAAG;AACd,CAAC,EACDJ,KAAK,IAAI;EACL,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAeb,OAAO", "ignoreList": []}]}