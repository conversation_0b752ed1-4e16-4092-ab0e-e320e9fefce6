package com.controller;

import com.model.ReportData;
import com.service.ReportDataService;
import com.util.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
public class ReportDataAction {

	@Autowired
	private ReportDataService reportDataService;

	//查询报表
	@RequestMapping(value="/queryReport")
	public String queryReport(ReportData reportdata,HttpServletRequest req) throws Exception {

		String f = req.getParameter("f");

		if (f == null) {
			reportdata.setSql("select date_format(ltime, '%Y-%m-%d') as name, count(*) as num \n" +
			"from leaves \n" +
			"where flag = '审核通过' \n" +
			"group by date_format(ltime, '%Y-%m-%d') order by name desc");
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/total/total1.jsp";
		}
		else if(f.equals("1"))
		{
			reportdata.setSql("select date_format(ltime, '%Y-%m') as name, count(*) as num \n" +
			"from leaves \n" +
			"where flag = '审核通过' \n" +
			"group by date_format(ltime, '%Y-%m') order by name desc");
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/total/total2.jsp";
		}
		else if(f.equals("2"))
		{
			reportdata.setSql("select date_format(ltime, '%Y') as name, count(*) as num \n" +
			"from leaves \n" +
			"where flag = '审核通过' \n" +
			"group by date_format(ltime, '%Y') order by name desc");
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/total/total3.jsp";
		}
		else if(f.equals("3"))
		{
			reportdata.setSql(" select a.sno as name,sname as by1,tel as by2,special as by3," +
			" classes as by4,ifnull(num,0) as num\n" +
			" from students a\n" +
			" left join (select sno,count(*) as num from leaves where flag='审核通过' group by sno) b " +
			" on a.sno=b.sno\n" +
			" order by num desc");
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/total/total4.jsp";
		}
		else if(f.equals("4"))
		{
			reportdata.setSql(" select tname as name,count(*) as num\n" +
			" from leaves a left join ltype l on a.tid = l.tid\n" +
			" where flag='审核通过'\n" +
			" group by tname");
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/total/total5.jsp";
		}
		else {
			List<ReportData> getReportData = reportDataService.report(reportdata);

			req.setAttribute("ReportDataList", getReportData);
			return "/admin/ltype/ltypeList.jsp";
		}

	}

}

