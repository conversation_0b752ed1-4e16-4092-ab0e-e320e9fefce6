package com.service;
import java.util.List;

import com.model.Mailremindertemplate;
import com.util.PageBean;

public interface MailremindertemplateService{
	
	//查询多条记录
	public List<Mailremindertemplate> queryMailremindertemplateList(Mailremindertemplate mailremindertemplate,PageBean page) throws Exception;
 
	//添加
	public int insertMailremindertemplate(Mailremindertemplate mailremindertemplate) throws Exception ;
	
	//根据ID删除
	public int deleteMailremindertemplate(int id) throws Exception ;
	
	//更新
	public int updateMailremindertemplate(Mailremindertemplate mailremindertemplate) throws Exception ;
	
	//根据ID查询单条数据
	public Mailremindertemplate queryMailremindertemplateById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Mailremindertemplate mailremindertemplate);

}

