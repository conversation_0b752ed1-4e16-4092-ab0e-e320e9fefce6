const App = getApp();
Page({
  data: {
    keys: "",
    msgs1: [],
    msgs2: [],
    msgs3: [],
    doctors: [],
    url: App.Config.fileBasePath,
    indicatorDots: true,
    autoplay: true,
    interval: 5000,
    duration: 1000,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
    this.getMsgs1();
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs1();
  },

  getMsgs1() {
    //设置要传递的参数
    let param = {
      f: 1,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/Index_List").then((data) => {
      //执行服务器Servlet

      this.setData({
        msgs1: data.data,
        msgs2: data.data2,
        msgs3: data.data3,
      });
    });
  },

  goDoctorDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: "/pages/doctorView/index?id=" + id,
    });
  },

  changeValue(e) {
    this.setData({
      keys: e.detail.value,
    });
  },
  //搜索
  search() {
    //如果为空，就弹出提示
    if (this.data.keys == "") {
      wx.showToast({
        title: "请输入要搜索的关键词",
        icon: "none",
        duration: 2000,
      });
      return false;
    } else {
      App.globalData.sk = this.data.keys;
      wx.switchTab({
        url: "/pages/doctorList/index",
      });
    }
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
