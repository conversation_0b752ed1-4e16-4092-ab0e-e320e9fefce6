<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.AdminMapper">
	<select id="findAdminList"  resultType="Admin">
		select * from admin 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Admin">
	    select  *  
        from admin a  	
		<where>
      		<if test="aid != null and aid !=0 ">
		    and a.aid = #{aid}
		</if>
		<if test="aname != null and aname != ''">
		    and a.aname = #{aname}
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    and a.loginpassword = #{loginpassword}
		</if>
		<if test="arole != null and arole != ''">
		    and a.arole = #{arole}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} aid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from admin a  
		<where>
      		<if test="aid != null and aid !=0 ">
		    and a.aid = #{aid}
		</if>
		<if test="aname != null and aname != ''">
		    and a.aname = #{aname}
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    and a.loginpassword = #{loginpassword}
		</if>
		<if test="arole != null and arole != ''">
		    and a.arole = #{arole}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryAdminById" parameterType="int" resultType="Admin">
    select  *  
     from admin a  	 where a.aid=#{value}
  </select>
 
	<insert id="insertAdmin" useGeneratedKeys="true" keyProperty="aid" parameterType="Admin">
    insert into admin
    (aname,loginpassword,arole)
    values
    (#{aname},#{loginpassword},#{arole});
  </insert>
	
	<update id="updateAdmin" parameterType="Admin" >
    update admin 
    <set>
		<if test="aname != null and aname != ''">
		    aname = #{aname},
		</if>
		<if test="loginpassword != null and loginpassword != ''">
		    loginpassword = #{loginpassword},
		</if>
		<if test="arole != null and arole != ''">
		    arole = #{arole},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="aid != null or aid != ''">
      aid=#{aid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteAdmin" parameterType="int">
    delete from  admin where aid=#{value}
  </delete>

	
	
</mapper>

 
