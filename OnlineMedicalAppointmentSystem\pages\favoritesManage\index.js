const App = getApp();
Page({
  data: {
    msgs1: [],
    url: App.Config.fileBasePath,
  },

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //获取数据列表
  getMsgs1() {
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"), //得到当前登录的用户名
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/favorites_List").then((data) => {
      //调用服务器接口
      this.setData({
        msgs1: data.data, //把从服务器端得到的值赋值给数组
      });
    });
  },

  //删除
  dele(e) {
    var that = this;
    const dataset = e.currentTarget.dataset; //获取到绑定的数据
    let param = {
      f: 1,
      cid: dataset.id, //获取传递过来的id
    };
    App.WxService.showModal({
      title: "友情提示",
      content: "您确定要执行此操作吗？",
    }).then((data) => {
      if (data.confirm == 1) {
        App.HttpService.delData(param, "/favorites_Delete").then((data) => {
          //调用服务器接口
          App.WxService.showToast({
            title: "删除成功!",
            icon: "success",
            duration: 1500,
          });
          setTimeout(function () {
            that.getMsgs1();
          }, 1500);
        });
      }
    });
  },
});
