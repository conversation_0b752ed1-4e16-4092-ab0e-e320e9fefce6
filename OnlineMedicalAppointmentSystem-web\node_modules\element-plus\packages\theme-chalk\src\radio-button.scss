@use "sass:map";

@import 'mixins/mixins';
@import 'mixins/utils';
@import 'mixins/var';
@import 'mixins/button';
@import 'common/var';

@include b(radio-button) {
  @include set-component-css-var('radio-button', $--radio-button);
}

@include b(radio-button) {
  position: relative;
  display: inline-block;
  outline: none;

  @include e(inner) {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    vertical-align: middle;
    background: var(
      --el-button-default-background-color,
      map.get($--button, 'default-background-color')
    );
    border: $--border-base;
    font-weight: var(
      --el-button-font-weight,
      map.get($--button, 'font-weight')
    );
    border-left: 0;
    color: var(
      --el-button-default-font-color,
      map.get($--button, 'default-font-color')
    );
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    position: relative;
    cursor: pointer;
    transition: var(--el-transition-all);

    @include button-size(
      map.get($--button-padding-vertical, 'default'),
      map.get($--button-padding-horizontal, 'default'),
      map.get($--button-font-size, 'default'),
      0
    );

    &:hover {
      color: var(--el-color-primary);
    }

    & [class*='#{$namespace}-icon-'] {
      line-height: 0.9;

      & + span {
        margin-left: 5px;
      }
    }
  }

  &:first-child {
    .#{$namespace}-radio-button__inner {
      border-left: $--border-base;
      border-radius: var(--el-border-radius-base) 0 0
        var(--el-border-radius-base);
      box-shadow: none !important;
    }
  }

  @include e(original-radio) {
    opacity: 0;
    outline: none;
    position: absolute;
    z-index: -1;

    &:checked {
      & + .#{$namespace}-radio-button__inner {
        color: var(
          --el-radio-button-checked-font-color,
          map.get($--radio-button, 'checked-font-color')
        );
        background-color: var(
          --el-radio-button-checked-background-color,
          map.get($--radio-button, 'checked-background-color')
        );
        border-color: var(
          --el-radio-button-checked-border-color,
          map.get($--radio-button, 'checked-border-color')
        );
        box-shadow: -1px 0 0 0
          var(
            --el-radio-button-checked-border-color,
            map.get($--radio-button, 'checked-border-color')
          );
      }
    }

    &:disabled {
      & + .#{$namespace}-radio-button__inner {
        color: var(
          --el-button-disabled-font-color,
          map.get($--button, 'disabled-font-color')
        );
        cursor: not-allowed;
        background-image: none;
        background-color: var(
          --el-button-disabled-background-color,
          map.get($--button, 'disabled-background-color')
        );
        border-color: var(
          --el-button-disabled-border-color,
          map.get($--button, 'disabled-border-color')
        );
        box-shadow: none;
      }
      &:checked + .#{$namespace}-radio-button__inner {
        background-color: var(--el-radio-button-disabled-checked-fill);
      }
    }
  }

  &:last-child {
    .#{$namespace}-radio-button__inner {
      border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base)
        0;
    }
  }

  &:first-child:last-child {
    .#{$namespace}-radio-button__inner {
      border-radius: var(--el-border-radius-base);
    }
  }

  @each $size in (medium, small, mini) {
    @include m($size) {
      & .#{$namespace}-radio-button__inner {
        @include button-size(
          map.get($--button-padding-vertical, $size),
          map.get($--button-padding-horizontal, $size),
          map.get($--button-font-size, $size),
          0
        );
      }
    }
  }

  &:focus:not(.is-focus):not(:active):not(.is-disabled) {
    /*获得焦点时 样式提醒*/
    box-shadow: 0 0 2px 2px var(--el-radio-button-checked-border-color);
  }
}
