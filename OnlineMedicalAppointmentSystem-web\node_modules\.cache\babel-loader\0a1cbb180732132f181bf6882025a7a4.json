{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\mailremindertemplate\\MailremindertemplateEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\mailremindertemplate\\MailremindertemplateEdit.vue", "mtime": 1749198972932}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "id", "isClear", "uploadVisible", "btnLoading", "formData", "created", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "$refs", "editor", "txt", "html", "content", "save", "validate", "valid", "code", "$message", "message", "type", "offset", "msg", "<PERSON><PERSON><PERSON><PERSON>", "val"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\mailremindertemplate\\MailremindertemplateEdit.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\n<el-form-item label=\"提前提醒/天\" prop=\"days\">\n<el-input v-model=\"formData.days\" placeholder=\"提前提醒/天\"  style=\"width:50%;\" ></el-input>\n</el-form-item>\n<el-form-item label=\"邮件模板\" prop=\"content\">\n<WangEditor  ref=\"wangEditorRef\" v-model=\"formData.content\" :config=\"editorConfig\"   :isClear=\"isClear\" @change=\"editorChange\"></WangEditor>\n</el-form-item>\n<el-form-item>\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\n\n</el-form-item>\n</el-form>\n\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport WangEditor from \"../../../components/WangEditor\";\nexport default {\n  name: 'MailremindertemplateEdit',\n  components: {\n    WangEditor,\n  },  \n    data() {\n      return {   \n        id: '',\n        isClear: false,\n        uploadVisible: false, \n        btnLoading: false, //保存按钮加载状态     \n        formData: {}, //表单数据           \n        \n      };\n    },\n    created() {\n    this.id =4;\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n//获取列表数据\n        getDatas() {\n          let para = {\n          };\n          this.listLoading = true;\n          let url = base + \"/mailremindertemplate/get?id=\" + this.id;\n          request.post(url, para).then((res) => {\n            this.formData = JSON.parse(JSON.stringify(res.resdata));\n            this.listLoading = false;\n            this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.content);\n            \n          });\n        },\n    \n        // 添加\n        save() {\n          this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\n            if (valid) {\n              let url = base + \"/mailremindertemplate/update\";\n              this.btnLoading = true;\n              \n              request.post(url, this.formData).then((res) => { //发送请求         \n                if (res.code == 200) {\n                  this.$message({\n                    message: \"操作成功\",\n                    type: \"success\",\n                    offset: 320,\n                  });\n                \n                } else {\n                  this.$message({\n                    message:res.msg,\n                    type: \"error\",\n                    offset: 320,\n                  });\n                }\n                this.btnLoading = false;\n              });\n            }\n    \n          });\n        },\n  \n          \n           \n            // 富文本编辑器\n    editorChange(val) {\n      this.formData.content = val;\n    },\n   \n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": "AAoBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,0BAA0B;EAChCC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACV,IAAI,CAACL,EAAC,GAAG,CAAC;IACR,IAAI,CAACM,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAEb;IACQD,QAAQA,CAAA,EAAG;MACT,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIf,IAAG,GAAI,+BAA8B,GAAI,IAAI,CAACK,EAAE;MAC1DN,OAAO,CAACiB,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACT,QAAO,GAAIU,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;QACxB,IAAI,CAACS,KAAK,CAAC,eAAe,CAAC,CAACC,MAAM,CAACC,GAAG,CAACC,IAAI,CAAC,IAAI,CAACjB,QAAQ,CAACkB,OAAO,CAAC;MAEpE,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACL,KAAK,CAAC,aAAa,CAAC,CAACM,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIf,GAAE,GAAIf,IAAG,GAAI,8BAA8B;UAC/C,IAAI,CAACQ,UAAS,GAAI,IAAI;UAEtBT,OAAO,CAACiB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACN,QAAQ,CAAC,CAACQ,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACa,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZC,OAAO,EAAE,MAAM;gBACfC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;YAEJ,OAAO;cACL,IAAI,CAACH,QAAQ,CAAC;gBACZC,OAAO,EAACf,GAAG,CAACkB,GAAG;gBACfF,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAAC3B,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAIG;IACR6B,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAAC7B,QAAQ,CAACkB,OAAM,GAAIW,GAAG;IAC7B;EAEE;AACN", "ignoreList": []}]}