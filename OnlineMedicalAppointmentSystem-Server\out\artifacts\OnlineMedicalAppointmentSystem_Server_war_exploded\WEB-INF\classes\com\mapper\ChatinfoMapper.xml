<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.ChatinfoMapper">
	<select id="findChatinfoList"  resultType="Chatinfo">
		select * from chatinfo 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Chatinfo">
	    select  *  
        from chatinfo a  	
		<where>
      		<if test="cid != null and cid !=0 ">
		    and a.cid = #{cid}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="sendtime != null and sendtime != ''">
		    and a.sendtime = #{sendtime}
		</if>
		<if test="flag != null and flag !=0 ">
		    and a.flag = #{flag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} cid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from chatinfo a  
		<where>
      		<if test="cid != null and cid !=0 ">
		    and a.cid = #{cid}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="sendtime != null and sendtime != ''">
		    and a.sendtime = #{sendtime}
		</if>
		<if test="flag != null and flag !=0 ">
		    and a.flag = #{flag}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryChatinfoById" parameterType="int" resultType="Chatinfo">
    select  *  
     from chatinfo a  	 where a.cid=#{value}
  </select>
 
	<insert id="insertChatinfo" useGeneratedKeys="true" keyProperty="cid" parameterType="Chatinfo">
    insert into chatinfo
    (lname,did,content,sendtime,flag)
    values
    (#{lname},#{did},#{content},now(),#{flag});
  </insert>
	
	<update id="updateChatinfo" parameterType="Chatinfo" >
    update chatinfo 
    <set>
		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="did != null ">
		    did = #{did},
		</if>
		<if test="content != null and content != ''">
		    content = #{content},
		</if>
		<if test="sendtime != null and sendtime != ''">
		    sendtime = #{sendtime},
		</if>
		<if test="flag != null ">
		    flag = #{flag},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="cid != null or cid != ''">
      cid=#{cid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteChatinfo" parameterType="int">
    delete from  chatinfo where cid=#{value}
  </delete>

	
	
</mapper>

 
