.patient-list {
    padding: 20rpx;
}

.empty-tip {
    text-align: center;
    color: #999;
    padding: 40rpx 0;
}

.patient-item {
    background: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
}

.patient-info {
    flex: 1;
}

.patient-info .name {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}

.patient-info .detail {
    font-size: 28rpx;
    color: #666;
}

.separator {
    margin: 0 10rpx;
    color: #ddd;
}

.delete-btn {
    color: #ff4d4f;
    font-size: 28rpx;
    padding: 10rpx 20rpx;
}

.add-btn-container {
    position: fixed;
    bottom: 40rpx;
    left: 20rpx;
    right: 20rpx;
}

.add-btn-container .diy-btn {
    width: 100%;
} 