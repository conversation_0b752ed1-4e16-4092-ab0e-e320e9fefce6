package com.controller;

import com.response.Response;
import com.service.StatisticsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Resource
    private StatisticsService statisticsService;

    /**
     * 获取仪表板统计数据
     */
    @ResponseBody
    @PostMapping(value = "/dashboard")
    @CrossOrigin
    public Response getDashboardStatistics(@RequestBody Map<String, Object> params, HttpServletRequest req) throws Exception {
        try {
            String role = (String) params.get("role");
            Map<String, Object> statistics = new HashMap<>();

            if ("管理员".equals(role)) {
                // 管理员统计数据
                statistics.put("patientCount", statisticsService.getPatientCount());
                statistics.put("doctorCount", statisticsService.getDoctorCount());
                statistics.put("todayAppointmentCount", statisticsService.getTodayAppointmentCount());
                statistics.put("totalAppointmentCount", statisticsService.getTotalAppointmentCount());
            } else if ("医生".equals(role)) {
                // 医生统计数据
                Object doctorIdObj = params.get("doctorId");
                if (doctorIdObj != null) {
                    Integer doctorId = null;
                    if (doctorIdObj instanceof String) {
                        doctorId = Integer.parseInt((String) doctorIdObj);
                    } else if (doctorIdObj instanceof Integer) {
                        doctorId = (Integer) doctorIdObj;
                    }
                    
                    if (doctorId != null) {
                        statistics.put("doctorTodayAppointmentCount", 
                            statisticsService.getDoctorTodayAppointmentCount(doctorId));
                        statistics.put("doctorTotalAppointmentCount", 
                            statisticsService.getDoctorTotalAppointmentCount(doctorId));
                    }
                }
            }

            return Response.success(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
    }
}
