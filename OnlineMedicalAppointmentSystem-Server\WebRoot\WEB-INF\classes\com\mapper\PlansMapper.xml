<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.PlansMapper">
	<select id="findPlansList"  resultType="Plans">
		select * from plans 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Plans">
	    select  *  
        from plans a  left join doctor b on a.did=b.did  	
		<where>
      		<if test="plid != null and plid !=0 ">
		    and a.plid = #{plid}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="weeks != null and weeks != ''">
		    and a.weeks = #{weeks}
		</if>
		<if test="ptime != null and ptime != ''">
		    and a.ptime = #{ptime}
		</if>
		<if test="people != null and people !=0 ">
		    and a.people = #{people}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} plid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from plans a  left join doctor b on a.did=b.did  
		<where>
      		<if test="plid != null and plid !=0 ">
		    and a.plid = #{plid}
		</if>
		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="weeks != null and weeks != ''">
		    and a.weeks = #{weeks}
		</if>
		<if test="ptime != null and ptime != ''">
		    and a.ptime = #{ptime}
		</if>
		<if test="people != null and people !=0 ">
		    and a.people = #{people}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryPlansById" parameterType="int" resultType="Plans">
    select  *  
     from plans a  left join doctor b on a.did=b.did  	 where a.plid=#{value}
  </select>
 
	<insert id="insertPlans" useGeneratedKeys="true" keyProperty="plid" parameterType="Plans">
    insert into plans
    (did,weeks,ptime,people)
    values
    (#{did},#{weeks},#{ptime},#{people});
  </insert>
	
	<update id="updatePlans" parameterType="Plans" >
    update plans 
    <set>
		<if test="did != null ">
		    did = #{did},
		</if>
		<if test="weeks != null and weeks != ''">
		    weeks = #{weeks},
		</if>
		<if test="ptime != null and ptime != ''">
		    ptime = #{ptime},
		</if>
		<if test="people != null ">
		    people = #{people},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="plid != null or plid != ''">
      plid=#{plid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deletePlans" parameterType="int">
    delete from  plans where plid=#{value}
  </delete>

	
	
</mapper>

 
