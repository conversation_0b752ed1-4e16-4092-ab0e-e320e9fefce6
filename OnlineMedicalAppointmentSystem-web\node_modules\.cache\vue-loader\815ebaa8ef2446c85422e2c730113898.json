{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue", "mtime": 1749198812664}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue"], "names": [], "mappings": ";AA4CA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEZ,CAAC;IACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACxE,CAAC,SAAS,CAAC;;MAEL,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACV,CAAC,EAAE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;WACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;eACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;iBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC;eACH,EAAE,CAAC,CAAC,CAAC,EAAE;iBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACb,CAAC,CAAC;eACJ;eACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvB,CAAC,CAAC;WACJ;;SAEF,CAAC,CAAC;IACP,CAAC;;OAEE,CAAC,EAAE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC;QACJ,CAAC;;;IAGL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;;;;MAIC,CAAC;AACP", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/plans/PlansAdd.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"医生\" prop=\"did\">\r\n<el-select v-model=\"formData.did\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in doctorList\" :key=\"item.did\" :label=\"item.dname\" :value=\"item.did\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"星期\" prop=\"weeks\">\r\n<el-select v-model=\"formData.weeks\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"星期一\" value=\"星期一\"></el-option>\r\n<el-option label=\"星期二\" value=\"星期二\"></el-option>\r\n<el-option label=\"星期三\" value=\"星期三\"></el-option>\r\n<el-option label=\"星期四\" value=\"星期四\"></el-option>\r\n<el-option label=\"星期五\" value=\"星期五\"></el-option>\r\n<el-option label=\"星期六\" value=\"星期六\"></el-option>\r\n<el-option label=\"星期日\" value=\"星期日\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"时间段\" prop=\"ptime\">\r\n<el-select v-model=\"formData.ptime\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"8:00-9:00\" value=\"8:00-9:00\"></el-option>\r\n<el-option label=\"9:00-10:00\" value=\"9:00-10:00\"></el-option>\r\n<el-option label=\"10:00-11:00\" value=\"10:00-11:00\"></el-option>\r\n<el-option label=\"11:00-12:00\" value=\"11:00-12:00\"></el-option>\r\n<el-option label=\"14:00-15:00\" value=\"14:00-15:00\"></el-option>\r\n<el-option label=\"15:00-16:00\" value=\"15:00-16:00\"></el-option>\r\n<el-option label=\"16:00-17:00\" value=\"16:00-17:00\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"号数\" prop=\"people\">\r\n<el-input v-model=\"formData.people\" placeholder=\"号数\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'PlansAdd',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {   \r\n        uploadVisible: false, \r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          did: [{ required: true, message: '请选择医生', trigger: 'onchange' }],\r\n          weeks: [{ required: true, message: '请选择星期', trigger: 'onchange' }],\r\n          ptime: [{ required: true, message: '请选择时间段', trigger: 'onchange' }],\r\n          people: [{ required: true, message: '请输入号数', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n      this.getdoctorList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/plans/add\";\r\n             this.btnLoading = true;\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"操作成功\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/PlansManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/PlansManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdoctorList() {\r\n      let para = {};\r\n      this.listLoading = true;\r\n      let url = base + \"/doctor/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.doctorList = res.resdata;\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}