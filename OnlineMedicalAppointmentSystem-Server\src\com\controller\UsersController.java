package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/users")
public class UsersController{
	
	@Resource
	private UsersService usersService;
	
	//患者列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Users>> list(@RequestBody Users users, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = usersService.getCount(users);
		//获取当前页记录
		List<Users> usersList = usersService.queryUsersList(users, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(usersList, counts, page_count);
	}
        
	//添加患者
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Users users, HttpServletRequest req) throws Exception {
		try {
			//判断用户名是否存在
            Users users1 = new Users();
            users1.setLname(users.getLname());
            List<Users> usersList = usersService.queryUsersList(users1, null);
            if (usersList.size() > 0) {
                return Response.error(201, "用户名已存在，请重新输入");
            }
            else
            {
                usersService.insertUsers(users); //添加
            }
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除患者
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			usersService.deleteUsers(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改患者
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Users users, HttpServletRequest req) throws Exception {
		try {
			usersService.updateUsers(users); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回患者详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			String id = req.getParameter("id");
			Users users=usersService.queryUsersById(id); //根据ID查询
			return Response.success(users);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
	//登录
    @ResponseBody
    @PostMapping(value = "/login")
    @CrossOrigin
    public Response login(@RequestBody Users users, HttpServletRequest request) throws Exception {

        try {
            List<Users> usersList = usersService.queryUsersList(users, null);   //查询

            //判断是否有数据
            if (usersList.size() > 0) {
                return Response.success(usersList.get(0));  //登录成功
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error();
        }
        return Response.error(201, "用户名或密码错误");
    }
	//修改密码
    @ResponseBody
    @PostMapping(value = "/updatePwd")
    @CrossOrigin
    public Response updatePwd(@RequestBody Users users, HttpServletRequest req) throws Exception {
        try {
            Users users1 = usersService.queryUsersById(users.getLname()); //根据ID查询
            //判断原密码是否正确
            if (!users1.getUpassword().equals(users.getBy1())) {
                return Response.error(201, "原密码错误");
            } else {
                users1.setUpassword(users.getBy2());  //新密码
                usersService.updateUsers(users1); //修改新密码
            }

        } catch (Exception e) {
            return Response.error();
        }
        return Response.success();
    }

}

