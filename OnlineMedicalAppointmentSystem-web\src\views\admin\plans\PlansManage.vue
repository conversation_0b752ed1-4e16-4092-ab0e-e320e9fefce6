﻿<template>
  <div style="width: 100%;line-height: 30px;text-align: left;">
    <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
      <el-form :inline="true" :model="filters">
        <el-form-item label="医生" prop="did">
          <el-select v-model="filters.did" placeholder="请选择" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in doctorList" :key="item.did" :label="item.dname" :value="item.did"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="星期" prop="weeks">
          <el-select v-model="filters.weeks" placeholder="请选择" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="0" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-col>

    <el-table :data="datalist" border stripe style="width: 100%" v-loading="listLoading" highlight-current-row
      max-height="600" size="small">
      <el-table-column prop="plid" label="ID" align="center"></el-table-column>
      <el-table-column prop="dname" label="医生" align="center"></el-table-column>
      <el-table-column prop="weeks" label="星期" align="center"></el-table-column>
      <el-table-column prop="ptime" label="时间段" align="center"></el-table-column>
      <el-table-column prop="people" label="号数" align="center"></el-table-column>
      <el-table-column label="操作" min-width="200" align="center">
        <template #default="scope">
          <el-button type="success" size="mini" @click="handleEdit(scope.$index, scope.row)" icon="el-icon-edit"
            style=" padding: 3px 6px 3px 6px;">编辑</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete"
            style=" padding: 3px 6px 3px 6px;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize"
      background layout="total, prev, pager, next, jumper" :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"></el-pagination>

  </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'plans',
  components: {

  },
  data() {
    return {
      filters: {
        //列表查询参数
        did: '',
        weeks: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,
      doctorList: [], //医生

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据  

    };
  },
  created() {
    this.getDatas();
    this.getdoctorList();
  },


  methods: {


    // 删除排班
    handleDelete(index, row) {
      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          let url = base + "/plans/del?id=" + row.plid;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: "删除成功",
              type: "success",
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => { });
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {
        did: this.filters.did,
        weeks: this.filters.weeks,
        sort: "a.", // 修复sort字段，提供有效的表别名前缀

      };
      this.listLoading = true;
      let url = base + "/plans/list?currentPage=" + this.page.currentPage + "&pageSize=" + this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      }).catch((error) => {
        console.error('获取坐诊安排列表失败:', error);
        this.listLoading = false;
        this.$message.error('获取坐诊安排列表失败');
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    getdoctorList() {
      let para = {
        sort: "a." // 修复sort字段，提供有效的表别名前缀
      };
      this.listLoading = true;
      let url = base + "/doctor/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.doctorList = res.resdata;
        this.listLoading = false;
      }).catch((error) => {
        console.error('获取医生列表失败:', error);
        this.listLoading = false;
        this.$message.error('获取医生列表失败');
      });
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: "/PlansDetail",
        query: {
          id: row.plid,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: "/PlansEdit",
        query: {
          id: row.plid,
        },
      });
    },
  },
}

</script>
<style scoped></style>
