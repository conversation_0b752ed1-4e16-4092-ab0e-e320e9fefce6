<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      
<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="lname" label="用户名"  align="center"></el-table-column>
<el-table-column prop="did" label="医生id"  align="center"></el-table-column>
<el-table-column prop="content" label="聊天内容"  align="center"></el-table-column>
<el-table-column prop="sendtime" label="发送时间"  align="center"></el-table-column>
<el-table-column prop="flag" label="标识"  align="center"></el-table-column>
<el-table-column label="操作" min-width="200" align="center">
<template #default="scope">
<el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete" style=" padding: 3px 6px 3px 6px;">删除</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize" 
 background layout="total, prev, pager, next, jumper" :total="page.totalCount" 
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'chatinfo',
  components: {
    
  },  
    data() {
      return {
       
        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,      
        
        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据  
    
      };
    },
    created() {
      this.getDatas();
    },

 
    methods: {    

              
       // 删除聊天
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/chatinfo/del?id=" + row.cid;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
        getDatas() {
          let para = {
            sort: "" // 添加必需的sort字段
          };
          this.listLoading = true;
          let url = base + "/chatinfo/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;
          request.post(url, para).then((res) => {
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          }).catch((error) => {
            console.error('获取聊天信息列表失败:', error);
            this.listLoading = false;
            this.$message.error('获取聊天信息列表失败');
          });
        },
        
           
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/ChatinfoDetail",
             query: {
                id: row.cid,
              },
          });
        },
    
        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/ChatinfoEdit",
             query: {
                id: row.cid,
              },
          });
        },
      },
}

</script>
<style scoped>
</style>
 

