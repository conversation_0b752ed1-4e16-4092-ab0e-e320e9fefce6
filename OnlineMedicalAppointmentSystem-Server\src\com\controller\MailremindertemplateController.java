package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/mailremindertemplate")
public class MailremindertemplateController{
	
	@Resource
	private MailremindertemplateService mailremindertemplateService;
	
	//邮件模板列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Mailremindertemplate>> list(@RequestBody Mailremindertemplate mailremindertemplate, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = mailremindertemplateService.getCount(mailremindertemplate);
		//获取当前页记录
		List<Mailremindertemplate> mailremindertemplateList = mailremindertemplateService.queryMailremindertemplateList(mailremindertemplate, page);
		//遍历
		for (Mailremindertemplate mailremindertemplate2 : mailremindertemplateList) {
			mailremindertemplate2.setContent(removeHTML.Html2Text(mailremindertemplate2.getContent()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(mailremindertemplateList, counts, page_count);
	}
        
	//添加邮件模板
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Mailremindertemplate mailremindertemplate, HttpServletRequest req) throws Exception {
		try {
			mailremindertemplateService.insertMailremindertemplate(mailremindertemplate); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除邮件模板
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			mailremindertemplateService.deleteMailremindertemplate(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改邮件模板
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Mailremindertemplate mailremindertemplate, HttpServletRequest req) throws Exception {
		try {
			mailremindertemplateService.updateMailremindertemplate(mailremindertemplate); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回邮件模板详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Mailremindertemplate mailremindertemplate=mailremindertemplateService.queryMailremindertemplateById(id); //根据ID查询
			return Response.success(mailremindertemplate);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

