package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/parts")
public class PartsController{
	
	@Resource
	private PartsService partsService;
	
	//科室列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Parts>> list(@RequestBody Parts parts, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = partsService.getCount(parts);
		//获取当前页记录
		List<Parts> partsList = partsService.queryPartsList(parts, page);
		//遍历
		for (Parts parts2 : partsList) {
			parts2.setPmemo(removeHTML.Html2Text(parts2.getPmemo()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(partsList, counts, page_count);
	}
        
	//添加科室
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Parts parts, HttpServletRequest req) throws Exception {
		try {
			partsService.insertParts(parts); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除科室
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			partsService.deleteParts(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改科室
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Parts parts, HttpServletRequest req) throws Exception {
		try {
			partsService.updateParts(parts); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回科室详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Parts parts=partsService.queryPartsById(id); //根据ID查询
			return Response.success(parts);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

