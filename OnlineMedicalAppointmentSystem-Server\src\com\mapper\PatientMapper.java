package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Patient;

public interface PatientMapper {

	//返回所有记录
	public List<Patient> findPatientList();
	
	//查询多条记录
	public List<Patient> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertPatient(Patient patient);

	//根据ID删除
	public int deletePatient(int id);
	
	//更新
	public int updatePatient(Patient patient);
	
	//根据ID得到对应的记录
	public Patient queryPatientById(int id);
	
}

