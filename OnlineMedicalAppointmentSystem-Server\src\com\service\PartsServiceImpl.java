package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.PartsMapper;
import com.model.Parts;
import com.util.PageBean;
@Service
public class PartsServiceImpl implements PartsService{
        
	@Autowired
	private PartsMapper partsMapper;

	//查询多条记录
	public List<Parts> queryPartsList(Parts parts,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(parts, page);
		
		List<Parts> getParts = partsMapper.query(map);
		
		return getParts;
	}
	
	//得到记录总数
	@Override
	public int getCount(Parts parts) {
		Map<String, Object> map = getQueryMap(parts, null);
		int count = partsMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Parts parts,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(parts!=null){
			map.put("pid", parts.getPid());
			map.put("pname", parts.getPname());
			map.put("pmemo", parts.getPmemo());
			map.put("sort", parts.getSort());
			map.put("condition", parts.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertParts(Parts parts) throws Exception {
		return partsMapper.insertParts(parts);
	}

	//根据ID删除
	public int deleteParts(int id) throws Exception {
		return partsMapper.deleteParts(id);
	}

	//更新
	public int updateParts(Parts parts) throws Exception {
		return partsMapper.updateParts(parts);
	}
	
	//根据ID得到对应的记录
	public Parts queryPartsById(int id) throws Exception {
		Parts po =  partsMapper.queryPartsById(id);
		return po;
	}
}

