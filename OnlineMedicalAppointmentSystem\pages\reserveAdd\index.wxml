<view class="container">
	<diy-navbar bgColor="green" isBack="{{true}}">
		<view slot="backText"> 返回 </view>
		<view slot="content"> 提交预约 </view>
	</diy-navbar>

	<form bindsubmit="submitForm" bindreset="resetForm" class="flex diy-form diy-col-24 justify-center">
		<view class="diy-form-item diy-col-24">
			<view class="title">科室：</view>
			<view class="input">
				<text class="cu-tag line-cyan">{{msgs1[0].pname}}</text>
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title">医生：</view>
			<view class="input">
				<text class="cu-tag line-cyan">{{msgs1[0].dname}}</text>
			</view>
		</view>

		<view class="diy-form-item diy-col-24">
			<view class="title"> 预约日期： </view>
			<view class="input">
				<text class="cu-tag line-cyan">{{rdate}}</text>
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 时间段： </view>
			<view class="input">
				<text class="cu-tag line-cyan">{{rtime}}</text>
			</view>
		</view>
		<view class="diy-form-item diy-col-24">
			<view class="title"> 挂号费： </view>
			<view class="input">
				<text class="cu-tag text-red text-price">{{msgs1[0].price}}</text>
			</view>
		</view>



		<!-- 就诊人选择 -->
		<view class="diy-form-item diy-col-24">
			<view class="title">就诊人：</view>
			<view class="input">
				<view class="patient-list">
					<view class="patient-item {{selectedPatient.peoid === item.peoid ? 'selected' : ''}}"
						wx:for="{{patients}}" wx:key="peoid" bindtap="selectPatient" data-index="{{index}}">
						<text class="name">{{item.peoname}}</text>
						<text class="info">{{item.gender}} | {{item.age}}岁 | {{item.phone}}</text>
					</view>
				</view>
				<view class="add-patient">
					<button class="diy-btn green" size="mini" bindtap="goToAddPatient">添加就诊人</button>
				</view>
			</view>
		</view>

		<view class="flex diy-col-24 justify-center">
			<button style="" form-type="submit" class="diy-btn green flex1 margin-xs">提交</button>

			<button style="" form-type="reset" class="diy-btn green flex1 margin-xs">重置</button>
		</view>

	</form>

	<view class="clearfix"></view>
</view>