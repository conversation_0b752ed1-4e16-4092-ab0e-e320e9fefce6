package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Mailremindertemplate;

public interface MailremindertemplateMapper {

	//返回所有记录
	public List<Mailremindertemplate> findMailremindertemplateList();
	
	//查询多条记录
	public List<Mailremindertemplate> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertMailremindertemplate(Mailremindertemplate mailremindertemplate);

	//根据ID删除
	public int deleteMailremindertemplate(int id);
	
	//更新
	public int updateMailremindertemplate(Mailremindertemplate mailremindertemplate);
	
	//根据ID得到对应的记录
	public Mailremindertemplate queryMailremindertemplateById(int id);
	
}

