package com.service;
import java.util.List;

import com.model.Chatinfo;
import com.util.PageBean;

public interface ChatinfoService{
	
	//查询多条记录
	public List<Chatinfo> queryChatinfoList(Chatinfo chatinfo,PageBean page) throws Exception;
 
	//添加
	public int insertChatinfo(Chatinfo chatinfo) throws Exception ;
	
	//根据ID删除
	public int deleteChatinfo(int id) throws Exception ;
	
	//更新
	public int updateChatinfo(Chatinfo chatinfo) throws Exception ;
	
	//根据ID查询单条数据
	public Chatinfo queryChatinfoById(int id) throws Exception ;
	
	//得到记录总数
	int getCount(Chatinfo chatinfo);

}

