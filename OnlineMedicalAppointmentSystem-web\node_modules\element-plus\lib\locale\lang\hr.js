'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var hr = {
    name: 'hr',
    el: {
        colorpicker: {
            confirm: 'OK',
            clear: '<PERSON><PERSON><PERSON><PERSON>',
        },
        datepicker: {
            now: '<PERSON><PERSON>',
            today: '<PERSON><PERSON>',
            cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            clear: '<PERSON><PERSON><PERSON><PERSON>',
            confirm: 'OK',
            selectDate: 'Odaberi datum',
            selectTime: 'Odaberi vrijeme',
            startDate: '<PERSON><PERSON> početka',
            startTime: 'Vrijeme početka',
            endDate: 'Da<PERSON> završetka',
            endTime: 'Vrijeme završetka',
            prevYear: 'Prethodna godina',
            nextYear: 'Sljedeća godina',
            prevMonth: 'Prethodni mjesec',
            nextMonth: 'Sljedeći mjesec',
            year: '',
            month1: 'Siječanj',
            month2: 'Veljača',
            month3: '<PERSON><PERSON><PERSON><PERSON>',
            month4: 'Travanj',
            month5: 'Svibanj',
            month6: 'Lipanj',
            month7: 'Srpanj',
            month8: '<PERSON><PERSON><PERSON>',
            month9: 'Rujan',
            month10: 'Listopad',
            month11: '<PERSON><PERSON><PERSON>',
            month12: 'Prosinac',
            week: 'tjedan',
            weeks: {
                sun: 'Ned',
                mon: 'Pon',
                tue: 'Uto',
                wed: 'Sri',
                thu: 'Čet',
                fri: 'Pet',
                sat: 'Sub',
            },
            months: {
                jan: 'Jan',
                feb: 'Feb',
                mar: 'Mar',
                apr: 'Apr',
                may: 'May',
                jun: 'Jun',
                jul: 'Jul',
                aug: 'Aug',
                sep: 'Sep',
                oct: 'Oct',
                nov: 'Nov',
                dec: 'Dec',
            },
        },
        select: {
            loading: 'Učitavanje',
            noMatch: 'Nema pronađenih podataka',
            noData: 'Nema podataka',
            placeholder: 'Izaberi',
        },
        cascader: {
            noMatch: 'Nema pronađenih podataka',
            loading: 'Učitavanje',
            placeholder: 'Izaberi',
            noData: 'Nema podataka',
        },
        pagination: {
            goto: 'Idi na',
            pagesize: '/stranica',
            total: 'Ukupno {total}',
            pageClassifier: '',
        },
        messagebox: {
            title: 'Poruka',
            confirm: 'OK',
            cancel: 'Otkaži',
            error: 'Pogrešan unos',
        },
        upload: {
            deleteTip: 'pritisnite izbriši za brisanje',
            delete: 'Izbriši',
            preview: 'Pregled',
            continue: 'Nastavak',
        },
        table: {
            emptyText: 'Nema podataka',
            confirmFilter: 'Potvrdi',
            resetFilter: 'Resetiraj',
            clearFilter: 'Sve',
            sumText: 'Suma',
        },
        tree: {
            emptyText: 'Nema podataka',
        },
        transfer: {
            noMatch: 'Nema pronađenih podataka',
            noData: 'Nema podataka',
            titles: ['Lista 1', 'Lista 2'],
            filterPlaceholder: 'Unesite ključnu riječ',
            noCheckedFormat: '{total} stavki',
            hasCheckedFormat: '{checked}/{total} checked',
        },
        image: {
            error: 'FAILED',
        },
        pageHeader: {
            title: 'Back',
        },
        popconfirm: {
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
        },
    },
};

exports.default = hr;
