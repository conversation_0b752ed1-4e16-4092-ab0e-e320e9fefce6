{"version": 3, "file": "escape.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["escape.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAElD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACvB,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,EAAE,EAAE,MAAM,CAAC;CACf,CAAC,CAAC;AAEH,yDAAyD;AACzD,MAAM,CAAC,MAAM,YAAY;AACrB,uEAAuE;AACvE,MAAM,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI;IAChC,CAAC,CAAC,CAAC,GAAW,EAAE,KAAa,EAAU,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAE;IACjE,CAAC,CAAC,uEAAuE;QACvE,CAAC,CAAS,EAAE,KAAa,EAAU,EAAE,CACjC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK;gBACtC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;gBACvB,MAAM;gBACN,OAAO;YACT,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAExC;;;;;;GAMG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW;IACjC,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7C,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;YACxC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;aAAM;YACH,GAAG,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,YAAY,CACjD,GAAG,EACH,CAAC,CACJ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YAClB,4CAA4C;YAC5C,OAAO,GAAG,WAAW,CAAC,SAAS,IAAI,MAAM,CACrC,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAC7B,CAAC;SACL;KACJ;IAED,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,SAAS,CAAC;AAEhC;;;;;;;;;GASG;AACH,SAAS,UAAU,CACf,KAAa,EACb,GAAwB;IAExB,OAAO,SAAS,MAAM,CAAC,IAAY;QAC/B,IAAI,KAAK,CAAC;QACV,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YAC/B,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE;gBACzB,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;aAClD;YAED,kDAAkD;YAClD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;YAE3C,kCAAkC;YAClC,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;SAC7B;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC;AACN,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAE7D;;;;;GAKG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,UAAU,CACrC,aAAa,EACb,IAAI,GAAG,CAAC;IACJ,CAAC,EAAE,EAAE,QAAQ,CAAC;IACd,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,GAAG,EAAE,QAAQ,CAAC;CAClB,CAAC,CACL,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAChC,cAAc,EACd,IAAI,GAAG,CAAC;IACJ,CAAC,EAAE,EAAE,OAAO,CAAC;IACb,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,EAAE,EAAE,MAAM,CAAC;IACZ,CAAC,GAAG,EAAE,QAAQ,CAAC;CAClB,CAAC,CACL,CAAC"}