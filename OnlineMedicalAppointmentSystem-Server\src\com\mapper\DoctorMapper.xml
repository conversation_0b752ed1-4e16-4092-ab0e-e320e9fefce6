<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.DoctorMapper">
	<select id="findDoctorList"  resultType="Doctor">
		select * from doctor 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Doctor">
	    select  a.*,b.pname  
        from doctor a  left join parts b on a.pid=b.pid  	
		<where>
      		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="daccount != null and daccount != ''">
		    and a.daccount = #{daccount}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="dname != null and dname != ''">
		    and a.dname = #{dname}
		</if>
		<if test="sex != null and sex != ''">
		    and a.sex = #{sex}
		</if>
		<if test="photo != null and photo != ''">
		    and a.photo = #{photo}
		</if>
		<if test="jobs != null and jobs != ''">
		    and a.jobs = #{jobs}
		</if>
		<if test="tel != null and tel != ''">
		    and a.tel = #{tel}
		</if>
		<if test="shac != null and shac != ''">
		    and a.shac = #{shac}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="dmemo != null and dmemo != ''">
		    and a.dmemo = #{dmemo}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} did desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from doctor a  left join parts b on a.pid=b.pid  
		<where>
      		<if test="did != null and did !=0 ">
		    and a.did = #{did}
		</if>
		<if test="daccount != null and daccount != ''">
		    and a.daccount = #{daccount}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="dname != null and dname != ''">
		    and a.dname = #{dname}
		</if>
		<if test="sex != null and sex != ''">
		    and a.sex = #{sex}
		</if>
		<if test="photo != null and photo != ''">
		    and a.photo = #{photo}
		</if>
		<if test="jobs != null and jobs != ''">
		    and a.jobs = #{jobs}
		</if>
		<if test="tel != null and tel != ''">
		    and a.tel = #{tel}
		</if>
		<if test="shac != null and shac != ''">
		    and a.shac = #{shac}
		</if>
		<if test="pid != null and pid !=0 ">
		    and a.pid = #{pid}
		</if>
		<if test="dmemo != null and dmemo != ''">
		    and a.dmemo = #{dmemo}
		</if>
		<if test="addtime != null and addtime != ''">
		    and a.addtime = #{addtime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryDoctorById" parameterType="int" resultType="Doctor">
    select  *  
     from doctor a  left join parts b on a.pid=b.pid  	 where a.did=#{value}
  </select>
 
	<insert id="insertDoctor" useGeneratedKeys="true" keyProperty="did" parameterType="Doctor">
    insert into doctor
    (daccount,password,dname,sex,photo,jobs,tel,shac,price,pid,dmemo,addtime)
    values
    (#{daccount},#{password},#{dname},#{sex},#{photo},#{jobs},#{tel},#{shac},#{price},#{pid},#{dmemo},now());
  </insert>
	
	<update id="updateDoctor" parameterType="Doctor" >
    update doctor 
    <set>
		<if test="daccount != null and daccount != ''">
		    daccount = #{daccount},
		</if>
		<if test="password != null and password != ''">
		    password = #{password},
		</if>
		<if test="dname != null and dname != ''">
		    dname = #{dname},
		</if>
		<if test="sex != null and sex != ''">
		    sex = #{sex},
		</if>
		<if test="photo != null and photo != ''">
		    photo = #{photo},
		</if>
		<if test="jobs != null and jobs != ''">
		    jobs = #{jobs},
		</if>
		<if test="tel != null and tel != ''">
		    tel = #{tel},
		</if>
		<if test="shac != null and shac != ''">
		    shac = #{shac},
		</if>
		<if test="price != null ">
		    price = #{price},
		</if>
		<if test="pid != null ">
		    pid = #{pid},
		</if>
		<if test="dmemo != null and dmemo != ''">
		    dmemo = #{dmemo},
		</if>
		<if test="addtime != null and addtime != ''">
		    addtime = #{addtime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="did != null or did != ''">
      did=#{did}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteDoctor" parameterType="int">
    delete from  doctor where did=#{value}
  </delete>

	
	
</mapper>

 
