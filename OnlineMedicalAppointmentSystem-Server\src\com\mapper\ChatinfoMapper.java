package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Chatinfo;

public interface ChatinfoMapper {

	//返回所有记录
	public List<Chatinfo> findChatinfoList();
	
	//查询多条记录
	public List<Chatinfo> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertChatinfo(Chatinfo chatinfo);

	//根据ID删除
	public int deleteChatinfo(int id);
	
	//更新
	public int updateChatinfo(Chatinfo chatinfo);
	
	//根据ID得到对应的记录
	public Chatinfo queryChatinfoById(int id);
	
}

