package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Reserve;

public interface ReserveMapper {

	//返回所有记录
	public List<Reserve> findReserveList();
	
	//查询多条记录
	public List<Reserve> query(Map<String,Object> inputParam);
	
	//得到记录总数
	int getCount(Map<String,Object> inputParam);
	
	//添加
	public int insertReserve(Reserve reserve);

	//根据ID删除
	public int deleteReserve(int id);
	
	//更新
	public int updateReserve(Reserve reserve);
	
	//根据ID得到对应的记录
	public Reserve queryReserveById(int id);
	
}

