{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue?vue&type=template&id=a44c444e&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749195815470}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "id", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$data", "userLname", "_hoisted_5", "role", "_hoisted_6", "_Fragment", "key", "_createVNode", "_component_el_card", "shadow", "_hoisted_7", "_hoisted_8", "statistics", "patientCount", "_hoisted_9", "_hoisted_10", "doctor<PERSON>ount", "_hoisted_11", "_hoisted_12", "todayAppointmentCount", "_hoisted_13", "_hoisted_14", "totalAppointmentCount", "_hoisted_15", "_hoisted_16", "doctorTodayAppointmentCount", "_hoisted_17", "_hoisted_18", "doctorTotalAppointmentCount", "loading"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue"], "sourcesContent": ["<template>\r\n  <div style=\"width: 100%; padding: 20px;\" id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div style=\"text-align: center; margin-bottom: 30px;\">\r\n      <h2 style=\"color: #409EFF; margin-bottom: 10px;\">欢迎使用医院挂号预约系统</h2>\r\n      <p style=\"font-size: 16px; color: #666;\">\r\n        账号：<b style=\"color: #E6A23C;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #E6A23C;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div v-loading=\"loading\" style=\"display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;\">\r\n      <!-- 管理员统计卡片 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon patients\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.patientCount || 0 }}</h3>\r\n              <p>患者总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon doctors\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorCount || 0 }}</h3>\r\n              <p>医生总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.todayAppointmentCount || 0 }}</h3>\r\n              <p>今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.totalAppointmentCount || 0 }}</h3>\r\n              <p>总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n\r\n      <!-- 医生统计卡片 -->\r\n      <template v-if=\"role === '医生'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTodayAppointmentCount || 0 }}</h3>\r\n              <p>我的今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTotalAppointmentCount || 0 }}</h3>\r\n              <p>我的总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n    </div>\r\n\r\n    <!-- 操作提示 -->\r\n    <div style=\"text-align: center; margin-top: 40px; color: #909399;\">\r\n      <p style=\"font-size: 14px;\">请在左侧菜单中选择您要进行的操作！</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      loading: false,\r\n      statistics: {\r\n        patientCount: 0,\r\n        doctorCount: 0,\r\n        todayAppointmentCount: 0,\r\n        totalAppointmentCount: 0,\r\n        doctorTodayAppointmentCount: 0,\r\n        doctorTotalAppointmentCount: 0\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        const user = JSON.parse(sessionStorage.getItem(\"user\"));\r\n        let url = base + \"/statistics/dashboard\";\r\n\r\n        const params = {\r\n          role: this.role\r\n        };\r\n\r\n        // 如果是医生，传递医生ID\r\n        if (this.role === '医生' && user) {\r\n          params.doctorId = user.did;\r\n        }\r\n\r\n        const response = await request.post(url, params);\r\n        if (response.code === 200) {\r\n          this.statistics = response.resdata;\r\n        } else {\r\n          this.$message.error('获取统计数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error);\r\n        this.$message.error('获取统计数据失败');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stat-card {\r\n  width: 280px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.patients {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.doctors {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.today-appointments {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.total-appointments {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-info h3 {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.stat-info p {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin: 5px 0 0 0;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .stat-card {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;EACOA,KAAmC,EAAnC;IAAA;IAAA;EAAA,CAAmC;EAACC,EAAE,EAAC;;;EAErCD,KAAgD,EAAhD;IAAA;IAAA;EAAA;AAAgD;;EAEhDA,KAAqC,EAArC;IAAA;IAAA;EAAA;AAAqC;;EAChCA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EACvBA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EAKRA,KAA2E,EAA3E;IAAA;IAAA;IAAA;IAAA;EAAA;AAA2E;;EAIzFE,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EAQnBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EAQnBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EAQnBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EAWnBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EAQnBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;;;uBAlFhCC,mBAAA,CA+FM,OA/FNC,UA+FM,GA9FJC,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAkE;IAA9DN,KAA4C,EAA5C;MAAA;MAAA;IAAA;EAA4C,GAAC,cAAY,sBAC7DM,mBAAA,CAGI,KAHJE,UAGI,G,2CAHqC,MACpC,IAAAF,mBAAA,CAA8C,KAA9CG,UAA8C,EAAAC,gBAAA,CAAhBC,KAAA,CAAAC,SAAS,kB,2CAAO,OAC9C,IAAAN,mBAAA,CAAyC,KAAzCO,UAAyC,EAAAH,gBAAA,CAAXC,KAAA,CAAAG,IAAI,iB,KAIzCT,mBAAA,UAAa,E,+BACbF,mBAAA,CA8EM,OA9ENY,UA8EM,GA7EJV,mBAAA,aAAgB,EACAM,KAAA,CAAAG,IAAI,c,cAApBX,mBAAA,CAgDWa,SAAA;IAAAC,GAAA;EAAA,IA/CTC,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARNe,UAQM,G,0BAPJf,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAoB,IAC7BI,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,G,sBAEzBI,mBAAA,CAGM,OAHNgB,UAGM,GAFJhB,mBAAA,CAA2C,YAAAI,gBAAA,CAApCC,KAAA,CAAAY,UAAU,CAACC,YAAY,uB,0BAC9BlB,mBAAA,CAAW,WAAR,MAAI,qB;;MAKbY,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARNmB,UAQM,G,0BAPJnB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAmB,IAC5BI,mBAAA,CAAkC;MAA/BJ,KAAK,EAAC;IAAoB,G,sBAE/BI,mBAAA,CAGM,OAHNoB,WAGM,GAFJpB,mBAAA,CAA0C,YAAAI,gBAAA,CAAnCC,KAAA,CAAAY,UAAU,CAACI,WAAW,uB,0BAC7BrB,mBAAA,CAAW,WAAR,MAAI,qB;;MAKbY,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARNsB,WAQM,G,0BAPJtB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA8B,IACvCI,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,G,sBAEzBI,mBAAA,CAGM,OAHNuB,WAGM,GAFJvB,mBAAA,CAAoD,YAAAI,gBAAA,CAA7CC,KAAA,CAAAY,UAAU,CAACO,qBAAqB,uB,0BACvCxB,mBAAA,CAAY,WAAT,OAAK,qB;;MAKdY,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARNyB,WAQM,G,4BAPJzB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA8B,IACvCI,mBAAA,CAAgC;MAA7BJ,KAAK,EAAC;IAAkB,G,sBAE7BI,mBAAA,CAGM,OAHN0B,WAGM,GAFJ1B,mBAAA,CAAoD,YAAAI,gBAAA,CAA7CC,KAAA,CAAAY,UAAU,CAACU,qBAAqB,uB,0BACvC3B,mBAAA,CAAW,WAAR,MAAI,qB;;uEAMfD,mBAAA,YAAe,EACCM,KAAA,CAAAG,IAAI,a,cAApBX,mBAAA,CAwBWa,SAAA;IAAAC,GAAA;EAAA,IAvBTC,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARN4B,WAQM,G,4BAPJ5B,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA8B,IACvCI,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,G,sBAEzBI,mBAAA,CAGM,OAHN6B,WAGM,GAFJ7B,mBAAA,CAA0D,YAAAI,gBAAA,CAAnDC,KAAA,CAAAY,UAAU,CAACa,2BAA2B,uB,4BAC7C9B,mBAAA,CAAc,WAAX,SAAO,qB;;MAKhBY,YAAA,CAUUC,kBAAA;IAVDjB,KAAK,EAAC,WAAW;IAACkB,MAAM,EAAC;;sBAChC,MAQM,CARNd,mBAAA,CAQM,OARN+B,WAQM,G,4BAPJ/B,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA8B,IACvCI,mBAAA,CAAgC;MAA7BJ,KAAK,EAAC;IAAkB,G,sBAE7BI,mBAAA,CAGM,OAHNgC,WAGM,GAFJhC,mBAAA,CAA0D,YAAAI,gBAAA,CAAnDC,KAAA,CAAAY,UAAU,CAACgB,2BAA2B,uB,4BAC7CjC,mBAAA,CAAa,WAAV,QAAM,qB;;gGAzEHK,KAAA,CAAA6B,OAAO,E,GAgFvBnC,mBAAA,UAAa,E,4BACbC,mBAAA,CAEM;IAFDN,KAA6D,EAA7D;MAAA;MAAA;MAAA;IAAA;EAA6D,IAChEM,mBAAA,CAAiD;IAA9CN,KAAwB,EAAxB;MAAA;IAAA;EAAwB,GAAC,mBAAiB,E", "ignoreList": []}]}