package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.PatientMapper;
import com.model.Patient;
import com.util.PageBean;
@Service
public class PatientServiceImpl implements PatientService{
        
	@Autowired
	private PatientMapper patientMapper;

	//查询多条记录
	public List<Patient> queryPatientList(Patient patient,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(patient, page);
		
		List<Patient> getPatient = patientMapper.query(map);
		
		return getPatient;
	}
	
	//得到记录总数
	@Override
	public int getCount(Patient patient) {
		Map<String, Object> map = getQueryMap(patient, null);
		int count = patientMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Patient patient,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(patient!=null){
			map.put("peoid", patient.getPeoid());
			map.put("lname", patient.getLname());
			map.put("peoname", patient.getPeoname());
			map.put("phone", patient.getPhone());
			map.put("gender", patient.getGender());
			map.put("age", patient.getAge());
			map.put("sort", patient.getSort());
			map.put("condition", patient.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertPatient(Patient patient) throws Exception {
		return patientMapper.insertPatient(patient);
	}

	//根据ID删除
	public int deletePatient(int id) throws Exception {
		return patientMapper.deletePatient(id);
	}

	//更新
	public int updatePatient(Patient patient) throws Exception {
		return patientMapper.updatePatient(patient);
	}
	
	//根据ID得到对应的记录
	public Patient queryPatientById(int id) throws Exception {
		Patient po =  patientMapper.queryPatientById(id);
		return po;
	}
}

